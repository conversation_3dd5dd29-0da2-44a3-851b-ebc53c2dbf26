# 项目相关配置
system:
  # 名称
  name: AiManager
  # 版本
  version: 3.8.8
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/AiManager/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ~/Documents/work/AiManager/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 18086
  servlet:
    # 应用的访问路径
    context-path: /manageapi
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.sinco: info
    org.springframework: warn
    org.springframework.data.mongodb.core.MongoTemplate: INFO

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  # redis 配置
  redis:
    db:
      manage:
        host: 127.0.0.1
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 3
        # 密码
        password:
        # 连接超时时间
        timeout: 10000
      piclumen:
        host: 127.0.0.1
        # 端口，默认为6379
        port: 6379
        # 数据库索引(piclumen)
        database: 0
        # 密码
        password:
        # 连接超时时间
        timeout : 10000
      comfy:
        host: 127.0.0.1
        # 端口，默认为6379
        port: 6379
        # 数据库索引(comfy)
        database: 4
        # 密码
        password:
        # 连接超时时间
        timeout: 10000
    pool:
      # 连接池中的最小空闲连接
      min-idle: 0
      # 连接池中的最大空闲连接
      max-idle: 8
      # 连接池的最大数据库连接数
      max-active: 8
      # #连接池最大阻塞等待时间（使用负值表示没有限制）
      max-wait: -1ms
  # 数据源配置
  shardingsphere:
    datasource:
      # 数据源名称，以英文逗号分隔，需要跟下面的每个数据源配置对应上
      names: ds0, ds1
      # 主库连接信息
      ds0:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: ************************************************************************************************************************************************************************************************************************************
        username: ne
        password: HiwjqefiwojfsdfG88#
      ds1:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: ******************************************************************************************************************************************************************************************************
        username: ne
        password: HiwjqefiwojfsdfG88#
      #    masterslave:
      #      # 从库负载均衡算法，可选值为：round_robin 和 random
      #      load-balance-algorithm-type: round_robin
      #      # 最终的数据源名称（可以随便指定）
      #      name: ds
      #      # 主库数据源名称
      #      master-data-source-name: ds1
      #      # 从库数据源名称列表，多个逗号分隔
      #      slave-data-source-names: ds0
      # 分库分表规则配置
    rules:
      # 读写分离
      readwrite-splitting:
        data-sources:
          ds:
            # 读写分离类型，比如：Static，Dynamic，动态方式需要配合高可用功能，具体参考下方链接
            # https://blog.csdn.net/ShardingSphere/article/details/123243843
            type: Static
            loadBalancerName: round-robin
            props:
              # 注意，如果接口有事务，读写分离不生效，默认全部使用主库，为了保证数据一致性
              write-data-source-name: ds0
              read-data-source-names: ds1
        load-balancers:
          #名称自定义，跟上边的loadBalancerName配置的值保持一致
          round-robin:
            type: ROUND_ROBIN #一共三种一种是 RANDOM（随机），一种是 ROUND_ROBIN（轮询），一种是 WEIGHT（权重）
      sharding:
        # 逻辑表配置（文件表 gpt_prompt_file）
        tables:
          gpt_prompt_file:
            actual-data-nodes: ds.gpt_prompt_file_$->{0..19}
            table-strategy:
              standard:
                sharding-column: login_name
                sharding-algorithm-name: gpt-prompt-file-hash-mod
            key-generate-strategy:
              column: id
              key-generator-name: snowflake

          # 逻辑表配置（任务表 gpt_prompt_record）
          gpt_prompt_record:
            actual-data-nodes: ds.gpt_prompt_record_$->{0..19}
            table-strategy:
              standard:
                sharding-column: login_name
                sharding-algorithm-name: gpt-prompt-record-hash-mod
            key-generate-strategy:
              column: id
              key-generator-name: snowflake

          # 逻辑表配置（日志表 gpt_event_log）
          gpt_event_log:
            actual-data-nodes: ds.gpt_event_log_$->{2024..2025}_$->{1..12}
            table-strategy:
              standard:
                sharding-column: create_time
                sharding-algorithm-name: time-sharding-algorithm
            key-generate-strategy:
              column: id
              key-generator-name: snowflake
          gpt_user_collect:
            actual-data-nodes: ds.gpt_user_collect_$->{0..19}
            table-strategy:
              standard:
                sharding-column: login_name
                sharding-algorithm-name: gpt-user_collect-hash-mod
            key-generate-strategy:
              column: id
              key-generator-name: snowflake

        # 分表算法配置
        sharding-algorithms:
          gpt-prompt-file-hash-mod:
            type: HASH_MOD
            props:
              sharding-count: 20

          gpt-prompt-record-hash-mod:
            type: HASH_MOD
            props:
              sharding-count: 20

          time-sharding-algorithm:
            type: CLASS_BASED
            props:
              strategy: standard
              algorithmClassName: com.ai.framework.shardingjdbc.TimeShardingAlgorithm

        # 绑定表避免跨表查询
        binding-tables: gpt_prompt_file, gpt_prompt_record

    # 分布式主键生成器
    default-key-generator: snowflake
    props:
      worker-id: 123
      max-tolerate-time-difference-milliseconds: 10
      max-vibration-offset: 1
      sql-show: true
    # 模式配置
    mode:
      type: Memory
  #  # 数据源配置
  #  datasource:
  #    type: com.alibaba.druid.pool.DruidDataSource
  #    driverClassName: com.mysql.cj.jdbc.Driver
  #    druid:
  #      # 主库数据源
  #      master:
  #        url: ***********************************************************************************************************************************************************************************
  #        username: dbmanager
  #        password: xiangHivebanana#8881
  #      # 从库数据源
  #      slave:
  #        # 从数据源开关/默认关闭
  #        enabled: false
  #        url:
  #        username:
  #        password:
  #      # 初始连接数
  #      initialSize: 5
  #      # 最小连接池数量
  #      minIdle: 10
  #      # 最大连接池数量
  #      maxActive: 20
  #      # 配置获取连接等待超时的时间
  #      maxWait: 60000
  #      # 配置连接超时时间
  #      connectTimeout: 30000
  #      # 配置网络超时时间
  #      socketTimeout: 60000
  #      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
  #      timeBetweenEvictionRunsMillis: 60000
  #      # 配置一个连接在池中最小生存的时间，单位是毫秒
  #      minEvictableIdleTimeMillis: 300000
  #      # 配置一个连接在池中最大生存的时间，单位是毫秒
  #      maxEvictableIdleTimeMillis: 900000
  #      # 配置检测连接是否有效
  #      validationQuery: SELECT 1 FROM DUAL
  #      testWhileIdle: true
  #      testOnBorrow: false
  #      testOnReturn: false
  #      webStatFilter:
  #        enabled: true
  #      statViewServlet:
  #        enabled: true
  #        # 设置白名单，不填则允许所有访问
  #        allow:
  #        url-pattern: /druid/*
  #        # 控制台管理用户名和密码
  #        login-username: admin
  #        login-password: testadmin
  #      filter:
  #        stat:
  #          enabled: true
  #          # 慢SQL记录
  #          log-slow-sql: true
  #          slow-sql-millis: 1000
  #          merge-sql: true
  #        wall:
  #          config:
  #            multi-statement-allow: true
  # feign配置
  feign:
    client:
      config:
        my-service: # Feign 客户端名称
          connectTimeout: 10000 # 连接超时时间
          readTimeout: 10000    # 读取超时时间
  data:
    mongodb:
      uri: mongodb://localhost:27017/piclumen-community?authSource=admin
      min-pool-size: 10
      max-pool-size: 100
      connect-timeout: 10000
      max-wait-time: 60000
      heartbeat-frequency: 10000
      max-idle-time: 60000
      write-concern:
        timeout: 5000
        acknowledged: true
      socket-timeout: 60000
      read-preference: primaryPreferred
      auto-index-creation: false
  mail:
    toUsers:
      - <EMAIL>
      - <EMAIL>
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 480

# MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.ai.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml
# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ai.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api


# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
apikey:
  "acfc0073-155a-4d30-aab6-8b718f8f5108"
backend:
  baseUrl:
    "http://127.0.0.1:8002/"

# 逻辑相关配置
logic:
  # 四川兴科蓉用友url
  scSincoYongYouUrl: http://coder.sinco-pharm.com:8687/u8sc/YonyouU8WebService.asmx
  # 成都兴科蓉用友url
  cdSincoYongYouUrl: http://coder.sinco-pharm.com:8687/u8cd/YonyouU8WebService.asmx
  # excel加密flag
  excelEncryptFlag: false

# 逻辑相关配置
comfy:
  server:
    maxRetry: 5
    emailReceiver:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    client-id: client_piclumen_create_stg
    client-secret: 0013423203gg
    client-code: piclumen
    remoteUrl: http://%s:58080/comfy-api/config/%s

tencent-cloud:
  storage:
    secret-id: "AKIDb9XjhRyaVMsBHSbaM1Yt593i9duxnUHd"
    secret-key: "IWxEa8X2TjsvnSKEpJbhZDejmeSL6e9V"
    region: na-siliconvalley
    bucket-name: testauth-1324066212
    bucket-name-old: piclumen-1324066212
    base-suffix: cos.na-siliconvalley.myqcloud.com
    accelerate-suffix: cos.accelerate.myqcloud.com
    accelerate-domain: https://uploads.piclumen.com
    base-domain: https://upload.piclumen.com
    mini-rule: imageMogr2/quality/60/thumbnail/100x


#home页运维账户
opex:
  loginName: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>

# 配置sendcloud信息
sendcloud:
  api:
    user: piclumen_api
    key: 39411ea65c526a551434263b62f72e4b
  Url: https://api.sendcloud.net/apiv2/mail/send

# MQ
rocketmq:
  piclumen:
    create:
      group: gid_piclumen_create_prestg
      tag: tag_piclumen_create_prestg
      topic: tp_piclumen_prestg

#PUSH
push:
  notification-config:
    apnsP8TeamId: Z3H8NYT2V9
    apnsP8KeyId: WN83374QLC
    apnsP8FilePath: notification/AuthKey_WN83374QLC.p8
    ## 测试环境地址 api.development.push.apple.com 正式环境地址 api.push.apple.com
    apnsEnvironment: api.development.push.apple.com
    apnsAppBundledId: com.piclumen
    fcmMessagingScope: https://www.googleapis.com/auth/firebase.messaging
    ## 注意  projects/这个地方是 projectId 需要从控制台获取 /message
    fcmIOSEndPoint: https://fcm.googleapis.com/v1/projects/piclumen-c034b/messages:send
    fcmIOSFilePath: notification/piclumen-c034b-firebase-adminsdk-m9az9-6dbe248524.json
    ## 注意  projects/这个地方是 projectId 需要从控制台获取 /message
    #fcmAndroidEndPoint=
    #fcmAndroidFilePath=

# ----------------------------------------------------------Midjourney API config ---------------------------------------------------------------------
# Midjourney API配置
#midjourney.api.base-url=https://api.ttapi.io
midjourney:
  api:
    base-url: https://api.ttapi.io
    api-key: 73cca588-d4f8-b59f-a188-7bbffef5b595
    balance-alarm-threshold: 10000