<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.GptUserMapper">

    <resultMap type="com.ai.admin.domain.GptUser" id="GptUserResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="microsoftId"    column="microsoft_id"    />
        <result property="appleId"    column="apple_id"    />
        <result property="facebookId"    column="facebook_id"    />
        <result property="googleId"    column="google_id"    />
        <result property="wechatOpenId"    column="wechat_open_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="userName"    column="user_name"    />
        <result property="roleId"    column="role_id"    />
        <result property="email"    column="email"    />
        <result property="sex"    column="sex"    />
        <result property="avatar"    column="avatar"    />
        <result property="password"    column="password"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="resetPasswordVcode"    column="reset_password_vcode"    />
        <result property="avatarName"    column="avatar_name"    />
        <result property="thumbnailAvatarName"    column="thumbnail_avatar_name"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="thumbnailAvatarUrl"    column="thumbnail_avatar_url"    />
        <result property="albumImgNum"    column="album_img_num"    />
        <result property="contactBlackListFlag"    column="contact_black_list_flag"    />
        <result property="registCountry"    column="regist_country"    />
        <result property="totalImgNum"    column="total_img_num"    />
        <result property="iosFcmToken"    column="ios_fcm_token"    />
        <result property="androidFcmToken"    column="android_fcm_token"    />
        <result property="localLang"    column="local_lang"    />
        <result property="systemRewardLumen"    column="system_reward_lumen"    />
    </resultMap>

    <sql id="selectGptUserVo">
        select id, dept_id, microsoft_id, apple_id, facebook_id, google_id, wechat_open_id, login_name, user_name, role_id, email, sex, avatar,
               password, del_flag, create_by, create_time, update_by, update_time, reset_password_vcode, avatar_name,total_img_num,
               thumbnail_avatar_name, avatar_url, thumbnail_avatar_url, album_img_num,black_list_flag,contact_black_list_flag,regist_country,introduction,
               used_size,total_size,vip_type,price_interval,vip_begin_time,vip_end_time,user_config, daily_lumens_time,daily_lumens ,use_daily_lumens,used_collect_num,
               ios_fcm_token,android_fcm_token,local_lang,system_reward_lumen
        from gpt_user
    </sql>

    <select id="selectGptUserList" parameterType="com.ai.admin.domain.GptUser" resultMap="GptUserResult">
        <include refid="selectGptUserVo"/>
        <where>
            <if test="googleId != null  and googleId != ''"> and google_id like concat('%', #{googleId}, '%')</if>
            <if test="loginName != null  and loginName != ''"> and login_name  = #{loginName}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="avatarName != null  and avatarName != ''"> and avatar_name like concat('%', #{avatarName}, '%')</if>
            <if test="thumbnailAvatarName != null  and thumbnailAvatarName != ''"> and thumbnail_avatar_name like concat('%', #{thumbnailAvatarName}, '%')</if>
            <if test="thumbnailAvatarUrl != null  and thumbnailAvatarUrl != ''"> and thumbnail_avatar_url = #{thumbnailAvatarUrl}</if>
            <if test="albumImgNum != null "> and album_img_num = #{albumImgNum}</if>
            <if test="blackListFlag != null ">
                <if test="blackListFlag==true">
                    and black_list_flag = true
                </if>
                <if test="blackListFlag==false">
                    and (black_list_flag = false or black_list_flag is null)
                </if>
             </if>
            <if test="contactBlackListFlag != null ">
                <if test="contactBlackListFlag==true">
                    and contact_black_list_flag = true
                </if>
                <if test="contactBlackListFlag==false">
                    and (contact_black_list_flag = false or contact_black_list_flag is null)
                </if>
            </if>
            <if test="registCountry != null  and registCountry != ''"> and regist_country = #{registCountry}</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
            <if test="vipType != null  and vipType != ''"> and vip_type = #{vipType}</if>
            <if test="priceInterval != null  and priceInterval != ''"> and price_interval = #{priceInterval}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectGptUserById" parameterType="Long" resultMap="GptUserResult">
        <include refid="selectGptUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptUser" parameterType="com.ai.admin.domain.GptUser" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="microsoftId != null">microsoft_id,</if>
            <if test="appleId != null">apple_id,</if>
            <if test="facebookId != null">facebook_id,</if>
            <if test="googleId != null">google_id,</if>
            <if test="wechatOpenId != null">wechat_open_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="userName != null">user_name,</if>
            <if test="roleId != null">role_id,</if>
            <if test="email != null">email,</if>
            <if test="sex != null">sex,</if>
            <if test="avatar != null">avatar,</if>
            <if test="password != null">password,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="pwdUpdateDate != null">pwd_update_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="permission != null">permission,</if>
            <if test="invitedBy != null">invited_by,</if>
            <if test="balance != null">balance,</if>
            <if test="gainedCount != null">gained_count,</if>
            <if test="usedCount != null">used_count,</if>
            <if test="chargeCount != null">charge_count,</if>
            <if test="resetPasswordVcode != null">reset_password_vcode,</if>
            <if test="contextCount != null">context_count,</if>
            <if test="contextMaxToken != null">context_max_token,</if>
            <if test="usedWordsCount != null">used_words_count,</if>
            <if test="vipValidStartTime != null">vip_valid_start_time,</if>
            <if test="vipValidEndTime != null">vip_valid_end_time,</if>
            <if test="gainedTokenCount != null">gained_token_count,</if>
            <if test="usedTokenCount != null">used_token_count,</if>
            <if test="avatarName != null">avatar_name,</if>
            <if test="thumbnailAvatarName != null">thumbnail_avatar_name,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="thumbnailAvatarUrl != null">thumbnail_avatar_url,</if>
            <if test="albumImgNum != null">album_img_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="microsoftId != null">#{microsoftId},</if>
            <if test="appleId != null">#{appleId},</if>
            <if test="facebookId != null">#{facebookId},</if>
            <if test="googleId != null">#{googleId},</if>
            <if test="wechatOpenId != null">#{wechatOpenId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="userName != null">#{userName},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="email != null">#{email},</if>
            <if test="sex != null">#{sex},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="password != null">#{password},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="pwdUpdateDate != null">#{pwdUpdateDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="permission != null">#{permission},</if>
            <if test="invitedBy != null">#{invitedBy},</if>
            <if test="balance != null">#{balance},</if>
            <if test="gainedCount != null">#{gainedCount},</if>
            <if test="usedCount != null">#{usedCount},</if>
            <if test="chargeCount != null">#{chargeCount},</if>
            <if test="resetPasswordVcode != null">#{resetPasswordVcode},</if>
            <if test="contextCount != null">#{contextCount},</if>
            <if test="contextMaxToken != null">#{contextMaxToken},</if>
            <if test="usedWordsCount != null">#{usedWordsCount},</if>
            <if test="vipValidStartTime != null">#{vipValidStartTime},</if>
            <if test="vipValidEndTime != null">#{vipValidEndTime},</if>
            <if test="gainedTokenCount != null">#{gainedTokenCount},</if>
            <if test="usedTokenCount != null">#{usedTokenCount},</if>
            <if test="avatarName != null">#{avatarName},</if>
            <if test="thumbnailAvatarName != null">#{thumbnailAvatarName},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="thumbnailAvatarUrl != null">#{thumbnailAvatarUrl},</if>
            <if test="albumImgNum != null">#{albumImgNum},</if>
        </trim>
    </insert>

    <update id="updateGptUser" parameterType="com.ai.admin.domain.GptUser">
        update gpt_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="microsoftId != null">microsoft_id = #{microsoftId},</if>
            <if test="appleId != null">apple_id = #{appleId},</if>
            <if test="facebookId != null">facebook_id = #{facebookId},</if>
            <if test="googleId != null">google_id = #{googleId},</if>
            <if test="wechatOpenId != null">wechat_open_id = #{wechatOpenId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="email != null">email = #{email},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="pwdUpdateDate != null">pwd_update_date = #{pwdUpdateDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="permission != null">permission = #{permission},</if>
            <if test="invitedBy != null">invited_by = #{invitedBy},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="gainedCount != null">gained_count = #{gainedCount},</if>
            <if test="usedCount != null">used_count = #{usedCount},</if>
            <if test="chargeCount != null">charge_count = #{chargeCount},</if>
            <if test="resetPasswordVcode != null">reset_password_vcode = #{resetPasswordVcode},</if>
            <if test="contextCount != null">context_count = #{contextCount},</if>
            <if test="contextMaxToken != null">context_max_token = #{contextMaxToken},</if>
            <if test="usedWordsCount != null">used_words_count = #{usedWordsCount},</if>
            <if test="vipValidStartTime != null">vip_valid_start_time = #{vipValidStartTime},</if>
            <if test="vipValidEndTime != null">vip_valid_end_time = #{vipValidEndTime},</if>
            <if test="gainedTokenCount != null">gained_token_count = #{gainedTokenCount},</if>
            <if test="usedTokenCount != null">used_token_count = #{usedTokenCount},</if>
            <if test="avatarName != null">avatar_name = #{avatarName},</if>
            <if test="thumbnailAvatarName != null">thumbnail_avatar_name = #{thumbnailAvatarName},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="thumbnailAvatarUrl != null">thumbnail_avatar_url = #{thumbnailAvatarUrl},</if>
            <if test="albumImgNum != null">album_img_num = #{albumImgNum},</if>
            <if test="blackListFlag != null">black_list_flag = #{blackListFlag},</if>
            <if test="contactBlackListFlag != null">contact_black_list_flag = #{contactBlackListFlag},</if>

        </trim>
        where id = #{id}
    </update>
 
    <delete id="deleteGptUserById" parameterType="Long">
        delete from gpt_user where id = #{id}
    </delete>

     <delete id="deleteGptUserByIds" parameterType="String">
        delete from gpt_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserIdByLoginNames" parameterType="java.util.List" resultType="java.lang.String">
        SELECT id from gpt_user where 1 = 1
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
    </select>

    <select id="getUserNums" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(*) from gpt_user where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
    </select>

    <select id="selectVipUserList"  parameterType="com.ai.admin.domain.dto.VipUserDto" resultType="com.ai.admin.domain.vo.VipUserVo">
        select   id,login_name,user_name,email,avatar_url,thumbnail_avatar_url,regist_country,used_size,total_size,vip_type,price_interval,vip_begin_time,vip_end_time,user_config, daily_lumens_time,daily_lumens ,use_daily_lumens,used_collect_num  from  gpt_user
        where vip_type  != 'basic'
        <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
        <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        <if test="vipType != null  and vipType != ''"> and vip_type = #{vipType}</if>
        <if test="priceInterval != null  and priceInterval != ''"> and price_interval = #{priceInterval}</if>
        <if test="params !=null and  params.beginVipBeginTime != null and params.beginVipBeginTime != '' and params.endVipBeginTime != null and params.endVipBeginTime != ''"> and vip_begin_time between #{params.beginVipBeginTime} and #{params.endVipBeginTime}</if>
    </select>

    <update id="batchCollectionUpdateUsers">
        <foreach collection="users" item="user" separator=";">
            UPDATE gpt_user
            SET used_collect_num = #{user.usedCollectNum}
            WHERE login_name = #{user.loginName}
        </foreach>
    </update>

    <update id="batchUpdateUserVipInfo">
        <foreach collection="list" item="item" separator=";">
            UPDATE gpt_user
            SET
            vip_type = #{item.vipType},
            price_interval = #{item.priceInterval},
            vip_end_time = #{item.vipEndTime}
            WHERE id = #{item.userId}
        </foreach>
    </update>

    <select id="sumTotalImgNum" resultType="java.lang.Long">
        select sum(total_img_num) from gpt_user
    </select>

<!--    <update id="batchUpdateUserSystemRewardLumen">-->
<!--        <foreach collection="list" item="item" separator=";">-->
<!--            UPDATE gpt_user-->
<!--            SET system_reward_lumen = COALESCE(system_reward_lumen, 0) + 50-->
<!--            WHERE id = #{item.id}-->
<!--        </foreach>-->
<!--    </update>-->
</mapper>