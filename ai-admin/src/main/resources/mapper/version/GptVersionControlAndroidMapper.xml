<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.version.mapper.GptVersionControlAndroidMapper">
    
    <resultMap type="GptVersionControlAndroid" id="GptVersionControlAndroidResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="isCurrent"    column="is_current"    />
        <result property="upgradeType"    column="upgrade_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGptVersionControlAndroidVo">
        select id, version, is_current, upgrade_type, create_time, update_time, create_by, update_by from gpt_version_control_android
    </sql>

    <select id="selectGptVersionControlAndroidList" parameterType="GptVersionControlAndroid" resultMap="GptVersionControlAndroidResult">
        <include refid="selectGptVersionControlAndroidVo"/>
        <where>  
            <if test="isCurrent != null "> and is_current = #{isCurrent}</if>
            <if test="upgradeType != null "> and upgrade_type = #{upgradeType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptVersionControlAndroidById" parameterType="Long" resultMap="GptVersionControlAndroidResult">
        <include refid="selectGptVersionControlAndroidVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptVersionControlAndroid" parameterType="GptVersionControlAndroid" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_version_control_android
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="version != null">version,</if>
            <if test="isCurrent != null">is_current,</if>
            <if test="upgradeType != null">upgrade_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="version != null">#{version},</if>
            <if test="isCurrent != null">#{isCurrent},</if>
            <if test="upgradeType != null">#{upgradeType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateGptVersionControlAndroid" parameterType="GptVersionControlAndroid">
        update gpt_version_control_android
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="isCurrent != null">is_current = #{isCurrent},</if>
            <if test="upgradeType != null">upgrade_type = #{upgradeType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptVersionControlAndroidById" parameterType="Long">
        delete from gpt_version_control_android where id = #{id}
    </delete>

    <delete id="deleteGptVersionControlAndroidByIds" parameterType="String">
        delete from gpt_version_control_android where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>