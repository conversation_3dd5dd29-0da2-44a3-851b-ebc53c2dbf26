<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.GptOpsInfoMapper">
    
    <resultMap type="GptOpsInfo" id="GptOpsInfoResult">
        <result property="id"    column="id"    />
        <result property="email"    column="email"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="userName"    column="user_name"    />
        <result property="opsFlag"    column="ops_flag"    />
        <result property="sendOpsInfo"    column="send_ops_info"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectGptOpsInfoVo">
        select id, email, phone_number, user_name, ops_flag, send_ops_info, create_by, create_time, update_by, update_time from gpt_ops_info
    </sql>

    <select id="selectGptOpsInfoList" parameterType="GptOpsInfo" resultMap="GptOpsInfoResult">
        <include refid="selectGptOpsInfoVo"/>
        <where>  
            <if test="email != null  and email != ''"> and email like concat('%', #{email}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="opsFlag != null "> and ops_flag = #{opsFlag}</if>
            <if test="sendOpsInfo != null "> and send_ops_info = #{sendOpsInfo}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptOpsInfoById" parameterType="Long" resultMap="GptOpsInfoResult">
        <include refid="selectGptOpsInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptOpsInfo" parameterType="GptOpsInfo" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_ops_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="email != null">email,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="userName != null">user_name,</if>
            <if test="opsFlag != null">ops_flag,</if>
            <if test="sendOpsInfo != null">send_ops_info,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="email != null">#{email},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="userName != null">#{userName},</if>
            <if test="opsFlag != null">#{opsFlag},</if>
            <if test="sendOpsInfo != null">#{sendOpsInfo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateGptOpsInfo" parameterType="GptOpsInfo">
        update gpt_ops_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="email != null">email = #{email},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="opsFlag != null">ops_flag = #{opsFlag},</if>
            <if test="sendOpsInfo != null">send_ops_info = #{sendOpsInfo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptOpsInfoById" parameterType="Long">
        delete from gpt_ops_info where id = #{id}
    </delete>

    <delete id="deleteGptOpsInfoByIds" parameterType="String">
        delete from gpt_ops_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>