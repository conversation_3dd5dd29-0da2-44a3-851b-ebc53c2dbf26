<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.tracking.mapper.KpiLumenPayCostMapper">
    
    <resultMap type="com.ai.tracking.domain.KpiLumenPayCost" id="KpiLumenPayCostResult">
        <result property="id"    column="id"    />
        <result property="date"    column="date"    />
        <result property="newVipUserNum"    column="new_vip_user_num"    />
        <result property="validVipUserNum"    column="valid_vip_user_num"    />
        <result property="lumenUsedUpVipUserNum"    column="lumen_used_up_vip_user_num"    />
        <result property="lumenUsedUpRate"    column="lumen_used_up_rate"    />
        <result property="standardNewVipUserNum"    column="standard_new_vip_user_num"    />
        <result property="standardValidVipUserNum"    column="standard_valid_vip_user_num"    />
        <result property="standardLumenUsedUpVipUserNum"    column="standard_lumen_used_up_vip_user_num"    />
        <result property="standardLumenUsedUpRate"    column="standard_lumen_used_up_rate"    />
        <result property="proNewVipUserNum"    column="pro_new_vip_user_num"    />
        <result property="proValidVipUserNum"    column="pro_valid_vip_user_num"    />
        <result property="proLumenUsedUpVipUserNum"    column="pro_lumen_used_up_vip_user_num"    />
        <result property="proLumenUsedUpRate"    column="pro_lumen_used_up_rate"    />
        <result property="newRechargeUserLumenNum"    column="new_recharge_user_lumen_num"    />
        <result property="newRechargeLumenNum"    column="new_recharge_lumen_num"    />
        <result property="newRechargeAvgLumenNum"    column="new_recharge_avg_lumen_num"    />
        <result property="validRechargeUserLumenNum"    column="valid_recharge_user_lumen_num"    />
        <result property="validRechargeLumenNum"    column="valid_recharge_lumen_num"    />
        <result property="validRechargeCostLumenNum"    column="valid_recharge_cost_lumen_num"    />
        <result property="validRechargeCostLumenRate"    column="valid_recharge_cost_lumen_rate"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKpiLumenPayCostVo">
        select id, date, new_vip_user_num, valid_vip_user_num, lumen_used_up_vip_user_num, lumen_used_up_rate, standard_new_vip_user_num, standard_valid_vip_user_num, standard_lumen_used_up_vip_user_num, standard_lumen_used_up_rate, pro_new_vip_user_num, pro_valid_vip_user_num, pro_lumen_used_up_vip_user_num, pro_lumen_used_up_rate, new_recharge_user_lumen_num, new_recharge_lumen_num, new_recharge_avg_lumen_num, valid_recharge_user_lumen_num, valid_recharge_lumen_num, valid_recharge_cost_lumen_num, valid_recharge_cost_lumen_rate, create_by, create_time, update_by, update_time from kpi_lumen_pay_cost
    </sql>

    <select id="selectKpiLumenPayCostList" parameterType="com.ai.tracking.domain.KpiLumenPayCost" resultMap="KpiLumenPayCostResult">
        <include refid="selectKpiLumenPayCostVo"/>
        <where>  
            <if test="params !=null and  params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''"> and date between #{params.beginDate} and #{params.endDate}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectKpiLumenPayCostById" parameterType="Long" resultMap="KpiLumenPayCostResult">
        <include refid="selectKpiLumenPayCostVo"/>
        where id = #{id}
    </select>

    <insert id="insertKpiLumenPayCost" parameterType="com.ai.tracking.domain.KpiLumenPayCost" useGeneratedKeys="true" keyProperty="id">
        insert into kpi_lumen_pay_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="date != null">date,</if>
            <if test="newVipUserNum != null">new_vip_user_num,</if>
            <if test="validVipUserNum != null">valid_vip_user_num,</if>
            <if test="lumenUsedUpVipUserNum != null">lumen_used_up_vip_user_num,</if>
            <if test="lumenUsedUpRate != null">lumen_used_up_rate,</if>
            <if test="standardNewVipUserNum != null">standard_new_vip_user_num,</if>
            <if test="standardValidVipUserNum != null">standard_valid_vip_user_num,</if>
            <if test="standardLumenUsedUpVipUserNum != null">standard_lumen_used_up_vip_user_num,</if>
            <if test="standardLumenUsedUpRate != null">standard_lumen_used_up_rate,</if>
            <if test="proNewVipUserNum != null">pro_new_vip_user_num,</if>
            <if test="proValidVipUserNum != null">pro_valid_vip_user_num,</if>
            <if test="proLumenUsedUpVipUserNum != null">pro_lumen_used_up_vip_user_num,</if>
            <if test="proLumenUsedUpRate != null">pro_lumen_used_up_rate,</if>
            <if test="newRechargeUserLumenNum != null">new_recharge_user_lumen_num,</if>
            <if test="newRechargeLumenNum != null">new_recharge_lumen_num,</if>
            <if test="newRechargeAvgLumenNum != null">new_recharge_avg_lumen_num,</if>
            <if test="validRechargeUserLumenNum != null">valid_recharge_user_lumen_num,</if>
            <if test="validRechargeLumenNum != null">valid_recharge_lumen_num,</if>
            <if test="validRechargeCostLumenNum != null">valid_recharge_cost_lumen_num,</if>
            <if test="validRechargeCostLumenRate != null">valid_recharge_cost_lumen_rate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="date != null">#{date},</if>
            <if test="newVipUserNum != null">#{newVipUserNum},</if>
            <if test="validVipUserNum != null">#{validVipUserNum},</if>
            <if test="lumenUsedUpVipUserNum != null">#{lumenUsedUpVipUserNum},</if>
            <if test="lumenUsedUpRate != null">#{lumenUsedUpRate},</if>
            <if test="standardNewVipUserNum != null">#{standardNewVipUserNum},</if>
            <if test="standardValidVipUserNum != null">#{standardValidVipUserNum},</if>
            <if test="standardLumenUsedUpVipUserNum != null">#{standardLumenUsedUpVipUserNum},</if>
            <if test="standardLumenUsedUpRate != null">#{standardLumenUsedUpRate},</if>
            <if test="proNewVipUserNum != null">#{proNewVipUserNum},</if>
            <if test="proValidVipUserNum != null">#{proValidVipUserNum},</if>
            <if test="proLumenUsedUpVipUserNum != null">#{proLumenUsedUpVipUserNum},</if>
            <if test="proLumenUsedUpRate != null">#{proLumenUsedUpRate},</if>
            <if test="newRechargeUserLumenNum != null">#{newRechargeUserLumenNum},</if>
            <if test="newRechargeLumenNum != null">#{newRechargeLumenNum},</if>
            <if test="newRechargeAvgLumenNum != null">#{newRechargeAvgLumenNum},</if>
            <if test="validRechargeUserLumenNum != null">#{validRechargeUserLumenNum},</if>
            <if test="validRechargeLumenNum != null">#{validRechargeLumenNum},</if>
            <if test="validRechargeCostLumenNum != null">#{validRechargeCostLumenNum},</if>
            <if test="validRechargeCostLumenRate != null">#{validRechargeCostLumenRate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKpiLumenPayCost" parameterType="com.ai.tracking.domain.KpiLumenPayCost">
        update kpi_lumen_pay_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="date != null">date = #{date},</if>
            <if test="newVipUserNum != null">new_vip_user_num = #{newVipUserNum},</if>
            <if test="validVipUserNum != null">valid_vip_user_num = #{validVipUserNum},</if>
            <if test="lumenUsedUpVipUserNum != null">lumen_used_up_vip_user_num = #{lumenUsedUpVipUserNum},</if>
            <if test="lumenUsedUpRate != null">lumen_used_up_rate = #{lumenUsedUpRate},</if>
            <if test="standardNewVipUserNum != null">standard_new_vip_user_num = #{standardNewVipUserNum},</if>
            <if test="standardValidVipUserNum != null">standard_valid_vip_user_num = #{standardValidVipUserNum},</if>
            <if test="standardLumenUsedUpVipUserNum != null">standard_lumen_used_up_vip_user_num = #{standardLumenUsedUpVipUserNum},</if>
            <if test="standardLumenUsedUpRate != null">standard_lumen_used_up_rate = #{standardLumenUsedUpRate},</if>
            <if test="proNewVipUserNum != null">pro_new_vip_user_num = #{proNewVipUserNum},</if>
            <if test="proValidVipUserNum != null">pro_valid_vip_user_num = #{proValidVipUserNum},</if>
            <if test="proLumenUsedUpVipUserNum != null">pro_lumen_used_up_vip_user_num = #{proLumenUsedUpVipUserNum},</if>
            <if test="proLumenUsedUpRate != null">pro_lumen_used_up_rate = #{proLumenUsedUpRate},</if>
            <if test="newRechargeUserLumenNum != null">new_recharge_user_lumen_num = #{newRechargeUserLumenNum},</if>
            <if test="newRechargeLumenNum != null">new_recharge_lumen_num = #{newRechargeLumenNum},</if>
            <if test="newRechargeAvgLumenNum != null">new_recharge_avg_lumen_num = #{newRechargeAvgLumenNum},</if>
            <if test="validRechargeUserLumenNum != null">valid_recharge_user_lumen_num = #{validRechargeUserLumenNum},</if>
            <if test="validRechargeLumenNum != null">valid_recharge_lumen_num = #{validRechargeLumenNum},</if>
            <if test="validRechargeCostLumenNum != null">valid_recharge_cost_lumen_num = #{validRechargeCostLumenNum},</if>
            <if test="validRechargeCostLumenRate != null">valid_recharge_cost_lumen_rate = #{validRechargeCostLumenRate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKpiLumenPayCostById" parameterType="Long">
        delete from kpi_lumen_pay_cost where id = #{id}
    </delete>

    <delete id="deleteKpiLumenPayCostByIds" parameterType="String">
        delete from kpi_lumen_pay_cost where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>