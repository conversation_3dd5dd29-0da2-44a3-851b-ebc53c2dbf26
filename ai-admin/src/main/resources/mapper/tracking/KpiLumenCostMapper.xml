<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.tracking.mapper.KpiLumenCostMapper">
    
    <resultMap type="com.ai.tracking.domain.KpiLumenCost" id="KpiLumenCostResult">
        <result property="id"    column="id"    />
        <result property="date"    column="date"    />
        <result property="totalVipNum"    column="total_vip_num"    />
        <result property="totalVipLumenNum"    column="total_vip_lumen_num"    />
        <result property="totalVipCostLumenNum"    column="total_vip_cost_lumen_num"    />
        <result property="totalRechargeNum"    column="total_recharge_num"    />
        <result property="totalRechargeLumenNum"    column="total_recharge_lumen_num"    />
        <result property="totalRechargeCostLumenNum"    column="total_recharge_cost_lumen_num"    />
        <result property="totalGiftNum"    column="total_gift_num"    />
        <result property="totalGiftLumenNum"    column="total_gift_lumen_num"    />
        <result property="totalGiftCostLumenNum"    column="total_gift_cost_lumen_num"    />
        <result property="platform"    column="platform"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKpiLumenCostVo">
        select id, date, total_vip_num, total_vip_lumen_num, total_vip_cost_lumen_num, total_recharge_num, total_recharge_lumen_num, total_recharge_cost_lumen_num, total_gift_num, total_gift_lumen_num, total_gift_cost_lumen_num, platform, create_by, create_time, update_by, update_time from kpi_lumen_cost
    </sql>

    <select id="selectKpiLumenCostList" parameterType="com.ai.tracking.domain.KpiLumenCost" resultMap="KpiLumenCostResult">
        <include refid="selectKpiLumenCostVo"/>
        <where>  
            <if test="params !=null and  params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''"> and date between #{params.beginDate} and #{params.endDate}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectKpiLumenCostById" parameterType="Long" resultMap="KpiLumenCostResult">
        <include refid="selectKpiLumenCostVo"/>
        where id = #{id}
    </select>

    <insert id="insertKpiLumenCost" parameterType="com.ai.tracking.domain.KpiLumenCost" useGeneratedKeys="true" keyProperty="id">
        insert into kpi_lumen_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="date != null">date,</if>
            <if test="totalVipNum != null">total_vip_num,</if>
            <if test="totalVipLumenNum != null">total_vip_lumen_num,</if>
            <if test="totalVipCostLumenNum != null">total_vip_cost_lumen_num,</if>
            <if test="totalRechargeNum != null">total_recharge_num,</if>
            <if test="totalRechargeLumenNum != null">total_recharge_lumen_num,</if>
            <if test="totalRechargeCostLumenNum != null">total_recharge_cost_lumen_num,</if>
            <if test="totalGiftNum != null">total_gift_num,</if>
            <if test="totalGiftLumenNum != null">total_gift_lumen_num,</if>
            <if test="totalGiftCostLumenNum != null">total_gift_cost_lumen_num,</if>
            <if test="platform != null">platform,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="date != null">#{date},</if>
            <if test="totalVipNum != null">#{totalVipNum},</if>
            <if test="totalVipLumenNum != null">#{totalVipLumenNum},</if>
            <if test="totalVipCostLumenNum != null">#{totalVipCostLumenNum},</if>
            <if test="totalRechargeNum != null">#{totalRechargeNum},</if>
            <if test="totalRechargeLumenNum != null">#{totalRechargeLumenNum},</if>
            <if test="totalRechargeCostLumenNum != null">#{totalRechargeCostLumenNum},</if>
            <if test="totalGiftNum != null">#{totalGiftNum},</if>
            <if test="totalGiftLumenNum != null">#{totalGiftLumenNum},</if>
            <if test="totalGiftCostLumenNum != null">#{totalGiftCostLumenNum},</if>
            <if test="platform != null">#{platform},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKpiLumenCost" parameterType="com.ai.tracking.domain.KpiLumenCost">
        update kpi_lumen_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="date != null">date = #{date},</if>
            <if test="totalVipNum != null">total_vip_num = #{totalVipNum},</if>
            <if test="totalVipLumenNum != null">total_vip_lumen_num = #{totalVipLumenNum},</if>
            <if test="totalVipCostLumenNum != null">total_vip_cost_lumen_num = #{totalVipCostLumenNum},</if>
            <if test="totalRechargeNum != null">total_recharge_num = #{totalRechargeNum},</if>
            <if test="totalRechargeLumenNum != null">total_recharge_lumen_num = #{totalRechargeLumenNum},</if>
            <if test="totalRechargeCostLumenNum != null">total_recharge_cost_lumen_num = #{totalRechargeCostLumenNum},</if>
            <if test="totalGiftNum != null">total_gift_num = #{totalGiftNum},</if>
            <if test="totalGiftLumenNum != null">total_gift_lumen_num = #{totalGiftLumenNum},</if>
            <if test="totalGiftCostLumenNum != null">total_gift_cost_lumen_num = #{totalGiftCostLumenNum},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKpiLumenCostById" parameterType="Long">
        delete from kpi_lumen_cost where id = #{id}
    </delete>

    <delete id="deleteKpiLumenCostByIds" parameterType="String">
        delete from kpi_lumen_cost where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>