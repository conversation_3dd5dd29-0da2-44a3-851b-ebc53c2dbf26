<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.tracking.mapper.KpiDayAmountMapper">
    
    <resultMap type="com.ai.tracking.domain.KpiDayAmount" id="KpiDayAmountResult">
        <result property="id"    column="id"    />
        <result property="countDate"    column="count_date"    />
        <result property="country"    column="country"    />
        <result property="srcCurrency"    column="src_currency"    />
        <result property="currency"    column="currency"    />
        <result property="number"    column="number"    />
        <result property="amount"    column="amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKpiDayAmountVo">
        select id, count_date, country, src_currency, currency, number, amount, create_by, create_time, update_by, update_time from kpi_day_amount
    </sql>

    <select id="selectKpiDayAmountList" parameterType="com.ai.tracking.domain.KpiDayAmount" resultMap="KpiDayAmountResult">
        <include refid="selectKpiDayAmountVo"/>
        <where>  
            <if test="params !=null and  params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''"> and count_date between #{params.beginDate} and #{params.endDate}</if>
            <if test="countDate != null "> and count_date = #{countDate}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="srcCurrency != null  and srcCurrency != ''"> and src_currency = #{srcCurrency}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="number != null "> and number = #{number}</if>
            <if test="amount != null "> and amount = #{amount}</if>
        </where>
        order by count_date desc, create_time desc
    </select>
    
    <select id="selectKpiDayAmountById" parameterType="Long" resultMap="KpiDayAmountResult">
        <include refid="selectKpiDayAmountVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKpiDayAmount" parameterType="com.ai.tracking.domain.KpiDayAmount" useGeneratedKeys="true" keyProperty="id">
        insert into kpi_day_amount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="countDate != null">count_date,</if>
            <if test="country != null">country,</if>
            <if test="srcCurrency != null">src_currency,</if>
            <if test="currency != null">currency,</if>
            <if test="number != null">number,</if>
            <if test="amount != null">amount,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="countDate != null">#{countDate},</if>
            <if test="country != null">#{country},</if>
            <if test="srcCurrency != null">#{srcCurrency},</if>
            <if test="currency != null">#{currency},</if>
            <if test="number != null">#{number},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKpiDayAmount" parameterType="com.ai.tracking.domain.KpiDayAmount">
        update kpi_day_amount
        <trim prefix="SET" suffixOverrides=",">
            <if test="countDate != null">count_date = #{countDate},</if>
            <if test="country != null">country = #{country},</if>
            <if test="srcCurrency != null">src_currency = #{srcCurrency},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="number != null">number = #{number},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKpiDayAmountById" parameterType="Long">
        delete from kpi_day_amount where id = #{id}
    </delete>

    <delete id="deleteKpiDayAmountByIds" parameterType="String">
        delete from kpi_day_amount where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
