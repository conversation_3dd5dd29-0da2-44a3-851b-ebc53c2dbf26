<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.GptPromptFileMapper">
    
    <resultMap type="com.ai.admin.domain.GptPromptFile" id="GptPromptFileResult">
        <result property="id"    column="id"    />
        <result property="loginName"    column="login_name"    />
        <result property="promptId"    column="prompt_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="thumbnailName"    column="thumbnail_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="thumbnailUrl"    column="thumbnail_url"    />
        <result property="sensitiveMessage"    column="sensitive_message"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="likeNums"    column="like_nums"    />
        <result property="isPublic"    column="is_public"    />
        <result property="rejectionContent"    column="rejection_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="del"    column="del"    />
    </resultMap>

    <sql id="selectGptPromptFileVo">
        select id, login_name, prompt_id, file_name, thumbnail_name, file_url, thumbnail_url, sensitive_message, width, height,is_public ,rejection_content,like_nums, create_time, update_time, create_by, update_by, del from gpt_prompt_file
    </sql>

    <select id="selectGptPromptFileList" parameterType="com.ai.admin.domain.GptPromptFile" resultMap="GptPromptFileResult">
        <include refid="selectGptPromptFileVo"/>
        <where>
            <if test="id !=null">
                <if test="isNext == 1">
                    and  #{id} > id
                </if>
                <if test="isNext == 0">
                    and id > #{id}
                </if>
            </if>
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="promptId != null  and promptId != ''"> and prompt_id = #{promptId}</if>
            <if test="isPublic != null "> and is_public = #{isPublic}</if>

        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptPromptFileById" parameterType="Long" resultMap="GptPromptFileResult">
        <include refid="selectGptPromptFileVo"/>
        where id = #{id} and login_name = #{loginName}
    </select>

    <insert id="insertGptPromptFile" parameterType="com.ai.admin.domain.GptPromptFile" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_prompt_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loginName != null">login_name,</if>
            <if test="promptId != null">prompt_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="thumbnailName != null">thumbnail_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="thumbnailUrl != null">thumbnail_url,</if>
            <if test="sensitiveMessage != null">sensitive_message,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="likeNums != null">like_nums,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="del != null">del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loginName != null">#{loginName},</if>
            <if test="promptId != null">#{promptId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="thumbnailName != null">#{thumbnailName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="thumbnailUrl != null">#{thumbnailUrl},</if>
            <if test="sensitiveMessage != null">#{sensitiveMessage},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="likeNums != null">#{likeNums},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="del != null">#{del},</if>
         </trim>
    </insert>

    <update id="updateGptPromptFile" parameterType="com.ai.admin.domain.GptPromptFile">
        update gpt_prompt_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="promptId != null">prompt_id = #{promptId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="thumbnailName != null">thumbnail_name = #{thumbnailName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="thumbnailUrl != null">thumbnail_url = #{thumbnailUrl},</if>
            <if test="sensitiveMessage != null">sensitive_message = #{sensitiveMessage},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="likeNums != null">like_nums = #{likeNums},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="del != null">del = #{del},</if>
        </trim>
        where id = #{id} and login_name = #{loginName}
    </update>

    <delete id="deleteGptPromptFileById" parameterType="Long">
        delete from gpt_prompt_file where id = #{id}
    </delete>

    <delete id="deleteGptPromptFileByIds" parameterType="String">
        delete from gpt_prompt_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getPromptFileRanking" parameterType="com.ai.admin.domain.vo.PromptFileRanking">
        SELECT COUNT(id) AS num, login_name
        FROM gpt_prompt_file
        WHERE 1=1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY login_name
        ORDER BY num desc
        LIMIT 30;
    </select>

    <select id="queryFileListByCondition" resultType="com.ai.admin.domain.GptPromptFile">
        select id,
               login_name,
               prompt_Id,
               file_url,
               thumbnail_url,
               high_thumbnail_url,
               create_time
        from ${param.dbName}
        where 
          id &gt; #{param.lastId}
          and create_time &lt; #{param.startQueryTime}
          and like_nums = 0
          and is_public = 0
          and collect_nums =0
          and (origin_create &lt;&gt; 'customUpload' or  origin_create is null)
        order by id asc
        limit #{param.limitSize}
    </select>
    <insert id="insertTempIds">
        insert INTO ${temDbName} (id)
        select id from
        ${dbName}
        where
        prompt_id in
        <foreach item="id" collection="promptIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        and like_nums = 0
        and is_public = 0
        and collect_nums =0
    </insert>
    <select id="queryFileByDbNameWithPromptIds" resultType="com.ai.admin.domain.GptPromptFile">
        select id,
               login_name,
               prompt_id
        from ${dbName}
        where
          prompt_id in
            <foreach item="id" collection="promptIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        and like_nums = 0
        and is_public = 0
        and collect_nums =0
    </select>

    <delete id="deleteGptPromptFileByIdsWithDbName" parameterType="String">
        delete from ${dbName} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getTaskAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
        select AVG(TIMESTAMPDIFF(SECOND, create_time, gen_end_time)) AS avg_date_diff from gpt_prompt_record
        where prompt_id is not null and gen_end_time is not null
        <if test="startDate != null and '' != startDate">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and #{endDate} > create_time
        </if>
        <if test="fastHour != null">
           and fast_hour = #{fastHour}
        </if>
    </select>

    <select id="getCreatePictureNums" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(id) as nums  from gpt_prompt_file where prompt_id is not null
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
    </select>

    <select id="getUserAvgCratePictureNums" parameterType="java.util.List" resultType="java.lang.Long">
        select avg(num) as nums from (
        SELECT count(id) as num  FROM gpt_prompt_file where prompt_id is not null
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY login_name) t
    </select>

    <select id="getUserMaxCratePictureNums" parameterType="java.util.List" resultType="java.lang.Long">
        select max(num) as nums from (
        SELECT count(id) as num  FROM gpt_prompt_file where prompt_id is not null
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY login_name) t
    </select>

    <select id="getAvgOnePicTime" resultType="java.lang.Double">
        select avg(TIMESTAMPDIFF(SECOND, create_time, gen_end_time)/batch_size) from gpt_prompt_record    where prompt_id is not null and gen_end_time is not null
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="fastHour != null">
            and fast_hour = #{fastHour}
        </if>
    </select>

    <select id="queryDeleteFileDataBatch" resultType="com.ai.admin.domain.GptPromptFile">
        select id, file_url, thumbnail_url, high_thumbnail_url, collect_nums
        from ${fileDbNum} where
            create_time <![CDATA[<]]> #{startTime}
        <if test="endTime != null">
            and create_time <![CDATA[>]]> #{endTime}
        </if>
        and collect_nums = 0
        order by id desc
        limit ${limit}
    </select>
    <delete id="physicRemoveDeleteDataBatch">
        delete from ${fileDbNum}
               where del = 1
        and id in
       <foreach item="id" collection="ids" open="(" separator="," close=")">
           #{id}
       </foreach>
    </delete>

    <select id="queryDeleteFileDataBatchWithVip" resultType="com.ai.admin.domain.GptPromptFile">
        SELECT
            pf.id,
            pf.login_name,
            pf.file_url,
            pf.high_thumbnail_url,
            pf.thumbnail_url,
            pf.mini_thumbnail_url,
            pf.high_mini_url,
            pf.collect_nums
        FROM
        gpt_user u
        JOIN ${fileDbNum} pf ON u.login_name = pf.login_name
        where (pf.collect_nums = 0 and u.vip_end_time is null and (u.vip_type is null or u.vip_type='basic') and pf.create_time &lt; #{startTime} )
        or (pf.collect_nums = 0 and u.vip_end_time is not null and u.vip_end_time &lt; UNIX_TIMESTAMP(NOW() - INTERVAL #{vipExpireDays} DAY) and pf.create_time &lt; #{startTime} )
        order by pf.id asc
        limit ${limit}
    </select>

    <update id="batchRejectMultiTable">
        <foreach collection="batchList" item="batch" separator=";">
            UPDATE ${batch.tableName}
            SET is_public = #{batch.isPublic},
            rejection_content = #{batch.rejectionContent}
            WHERE login_name IN
            <foreach collection="batch.loginNames" item="loginName" open="(" separator="," close=")">
                #{loginName}
            </foreach>
            AND id IN
            <foreach collection="batch.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </foreach>
    </update>
</mapper>