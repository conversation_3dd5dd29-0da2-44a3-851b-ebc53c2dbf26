<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.task.mapper.GptLbFeaturesMapper">
    
    <resultMap type="GptLbFeatures" id="GptLbFeaturesResult">
        <result property="id"    column="id"    />
        <result property="featureName"    column="feature_name"    />
        <result property="featureGroup"    column="feature_group"    />
        <result property="mark"    column="mark"    />
        <result property="featureStatus"    column="feature_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectGptLbFeaturesVo">
        select id, feature_name, feature_group, mark, feature_status, create_by, create_time, update_by, update_time from gpt_lb_features
    </sql>

    <select id="selectGptLbFeaturesList" parameterType="GptLbFeatures" resultMap="GptLbFeaturesResult">
        <include refid="selectGptLbFeaturesVo"/>
        <where>  
            <if test="featureName != null  and featureName != ''"> and feature_name like concat('%', #{featureName}, '%')</if>
            <if test="mark != null  and mark != ''"> and mark like concat('%', #{mark}, '%')</if>
            <if test="featureStatus != null "> and feature_status = #{featureStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptLbFeaturesById" parameterType="Long" resultMap="GptLbFeaturesResult">
        <include refid="selectGptLbFeaturesVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptLbFeatures" parameterType="GptLbFeatures" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_lb_features
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="featureName != null">feature_name,</if>
            <if test="featureGroup != null">feature_group,</if>
            <if test="mark != null">mark,</if>
            <if test="featureStatus != null">feature_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="featureName != null">#{featureName},</if>
            <if test="featureGroup != null">#{featureGroup},</if>
            <if test="mark != null">#{mark},</if>
            <if test="featureStatus != null">#{featureStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateGptLbFeatures" parameterType="GptLbFeatures">
        update gpt_lb_features
        <trim prefix="SET" suffixOverrides=",">
            <if test="featureName != null">feature_name = #{featureName},</if>
            <if test="featureGroup != null">feature_group = #{featureGroup},</if>
            <if test="mark != null">mark = #{mark},</if>
            <if test="featureStatus != null">feature_status = #{featureStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptLbFeaturesById" parameterType="Long">
        delete from gpt_lb_features where id = #{id}
    </delete>

    <delete id="deleteGptLbFeaturesByIds" parameterType="String">
        delete from gpt_lb_features where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDistinctFeatureGroups" resultType="string">
        SELECT DISTINCT feature_group FROM gpt_lb_features
    </select>
</mapper>