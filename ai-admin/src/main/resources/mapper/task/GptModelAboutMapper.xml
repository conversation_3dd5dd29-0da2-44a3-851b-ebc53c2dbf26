<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.task.mapper.GptModelAboutMapper">

    <resultMap type="com.ai.task.domain.GptModelAbout" id="GptModelAboutResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelDisplay"    column="model_display"    />
        <result property="modelType"    column="model_type"    />
        <result property="defaultHdFixDenoise"    column="default_hd_fix_denoise"    />
        <result property="modelAvatar"    column="model_avatar"    />
        <result property="modelDesc"    column="model_desc"    />
        <result property="modelOrder"    column="model_order"    />
        <result property="platform"    column="platform"    />
        <result property="defaultConfig"    column="default_config"    />
        <result property="supportStyleList"    column="support_style_list"    />
        <result property="modelOriginType"    column="model_origin_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGptModelAboutVo">
        select id, model_id, model_display, model_type, default_hd_fix_denoise, model_avatar, model_desc, model_order,android_order,ios_order, platform, default_config, support_style_list, model_origin_type, create_time, update_time, create_by, update_by from gpt_model_about
    </sql>

    <select id="selectGptModelAboutList" parameterType="com.ai.task.domain.GptModelAbout" resultMap="GptModelAboutResult">
        <include refid="selectGptModelAboutVo"/>
        <where>
            <if test="modelId != null  and modelId != ''"> and model_id = #{modelId}</if>
            <if test="modelDisplay != null  and modelDisplay != ''"> and model_display = #{modelDisplay}</if>
            <if test="modelType != null  and modelType != ''"> and model_type = #{modelType}</if>
            <if test="modelOrder != null "> and model_order = #{modelOrder}</if>
            <if test="platform != null  and platform != ''"> and platform like concat('%', #{platform}, '%')</if>
            <if test="modelOriginType != null "> and model_origin_type = #{modelOriginType}</if>
        </where>
        order by create_time desc
    </select>

</mapper>