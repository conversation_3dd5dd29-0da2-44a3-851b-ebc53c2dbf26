<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.AdConfigMapper">
    
    <resultMap type="com.ai.operation.domain.AdConfig" id="AdConfigResult">
        <result property="id"    column="id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="enable"    column="enable"    />
        <result property="quietPeriod"    column="quiet_period"    />
        <result property="adScene"    column="ad_scene"    />
        <result property="adId"    column="ad_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="dayLoadTimes"    column="day_load_times"    />
        <result property="dayShowTimes"    column="day_show_times"    />
        <result property="dayClickTimes"    column="day_click_times"    />
        <result property="preload"    column="preload"    />
        <result property="preReqRetry"    column="pre_req_retry"    />
        <result property="preReqMinInterval"    column="pre_req_min_interval"    />
        <result property="showTimeInterval"    column="show_time_interval"    />
        <result property="adType"    column="ad_type"    />
        <result property="platform"    column="platform"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAdConfigVo">
        select id, channel_id, enable, quiet_period, ad_scene, ad_id, slot_id, day_load_times, day_show_times, day_click_times, preload, pre_req_retry, pre_req_min_interval, show_time_interval, ad_type, platform, remark, create_by, create_time, update_by, update_time from ad_config
    </sql>

    <select id="selectAdConfigList" parameterType="com.ai.operation.domain.AdConfig" resultMap="AdConfigResult">
        <include refid="selectAdConfigVo"/>
        <where>  
            <if test="channelId != null  and channelId != ''"> and channel_id = #{channelId}</if>
            <if test="enable != null "> and enable = #{enable}</if>
            <if test="adScene != null  and adScene != ''"> and ad_scene = #{adScene}</if>
            <if test="adId != null  and adId != ''"> and ad_id = #{adId}</if>
            <if test="slotId != null  and slotId != ''"> and slot_id = #{slotId}</if>
            <if test="preload != null "> and preload = #{preload}</if>
            <if test="preReqRetry != null "> and pre_req_retry = #{preReqRetry}</if>
            <if test="adType != null  and adType != ''"> and ad_type = #{adType}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAdConfigById" parameterType="Long" resultMap="AdConfigResult">
        <include refid="selectAdConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAdConfig" parameterType="com.ai.operation.domain.AdConfig" useGeneratedKeys="true" keyProperty="id">
        insert into ad_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelId != null and channelId != ''">channel_id,</if>
            <if test="enable != null">enable,</if>
            <if test="quietPeriod != null">quiet_period,</if>
            <if test="adScene != null">ad_scene,</if>
            <if test="adId != null">ad_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="dayLoadTimes != null">day_load_times,</if>
            <if test="dayShowTimes != null">day_show_times,</if>
            <if test="dayClickTimes != null">day_click_times,</if>
            <if test="preload != null">preload,</if>
            <if test="preReqRetry != null">pre_req_retry,</if>
            <if test="preReqMinInterval != null">pre_req_min_interval,</if>
            <if test="showTimeInterval != null">show_time_interval,</if>
            <if test="adType != null">ad_type,</if>
            <if test="platform != null">platform,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelId != null and channelId != ''">#{channelId},</if>
            <if test="enable != null">#{enable},</if>
            <if test="quietPeriod != null">#{quietPeriod},</if>
            <if test="adScene != null">#{adScene},</if>
            <if test="adId != null">#{adId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="dayLoadTimes != null">#{dayLoadTimes},</if>
            <if test="dayShowTimes != null">#{dayShowTimes},</if>
            <if test="dayClickTimes != null">#{dayClickTimes},</if>
            <if test="preload != null">#{preload},</if>
            <if test="preReqRetry != null">#{preReqRetry},</if>
            <if test="preReqMinInterval != null">#{preReqMinInterval},</if>
            <if test="showTimeInterval != null">#{showTimeInterval},</if>
            <if test="adType != null">#{adType},</if>
            <if test="platform != null">#{platform},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAdConfig" parameterType="com.ai.operation.domain.AdConfig">
        update ad_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelId != null and channelId != ''">channel_id = #{channelId},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="quietPeriod != null">quiet_period = #{quietPeriod},</if>
            <if test="adScene != null">ad_scene = #{adScene},</if>
            <if test="adId != null">ad_id = #{adId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="dayLoadTimes != null">day_load_times = #{dayLoadTimes},</if>
            <if test="dayShowTimes != null">day_show_times = #{dayShowTimes},</if>
            <if test="dayClickTimes != null">day_click_times = #{dayClickTimes},</if>
            <if test="preload != null">preload = #{preload},</if>
            <if test="preReqRetry != null">pre_req_retry = #{preReqRetry},</if>
            <if test="preReqMinInterval != null">pre_req_min_interval = #{preReqMinInterval},</if>
            <if test="showTimeInterval != null">show_time_interval = #{showTimeInterval},</if>
            <if test="adType != null">ad_type = #{adType},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdConfigById" parameterType="Long">
        delete from ad_config where id = #{id}
    </delete>

    <delete id="deleteAdConfigByIds" parameterType="String">
        delete from ad_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>