<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.BannerImgIosMapper">
    
    <resultMap type="com.ai.operation.domain.BannerImgIos" id="BannerImgIosResult">
        <result property="id"    column="id"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="sort"    column="sort"    />
        <result property="jumpType"    column="jump_type"    />
        <result property="jumpId"    column="jump_id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBannerImgIosVo">
        select id, img_url, sort, jump_type, jump_id, is_deleted, status, create_by, update_by, create_time, update_time from banner_img_ios
    </sql>

    <select id="selectBannerImgIosList" parameterType="com.ai.operation.domain.BannerImgIos" resultMap="BannerImgIosResult">
        select
        bis.id, bis.img_url, bis.sort, bis.jump_type, bis.jump_id, bis.is_deleted,cm.title,
        bis.status, bis.create_by, bis.update_by, bis.create_time, bis.update_time
        from banner_img_ios bis left join  comm_activity cm on cm.id =  bis.jump_id
        <where>
            bis.is_deleted = 0
            <if test="jumpType != null "> and bis.jump_type = #{jumpType}</if>
            <if test="status != null "> and bis.status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectBannerImgIosById" parameterType="Long" resultMap="BannerImgIosResult">
        <include refid="selectBannerImgIosVo"/>
        where id = #{id}
    </select>

    <insert id="insertBannerImgIos" parameterType="com.ai.operation.domain.BannerImgIos" useGeneratedKeys="true" keyProperty="id">
        insert into banner_img_ios
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imgUrl != null">img_url,</if>
            <if test="sort != null">sort,</if>
            <if test="jumpType != null">jump_type,</if>
            <if test="jumpId != null">jump_id,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="jumpType != null">#{jumpType},</if>
            <if test="jumpId != null">#{jumpId},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBannerImgIos" parameterType="com.ai.operation.domain.BannerImgIos">
        update banner_img_ios
        <trim prefix="SET" suffixOverrides=",">
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="jumpType != null">jump_type = #{jumpType},</if>
            <if test="jumpId != null">jump_id = #{jumpId},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBannerImgIosById" parameterType="Long">
        delete from banner_img_ios where id = #{id}
    </delete>

    <delete id="deleteBannerImgIosByIds" parameterType="String">
        delete from banner_img_ios where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>