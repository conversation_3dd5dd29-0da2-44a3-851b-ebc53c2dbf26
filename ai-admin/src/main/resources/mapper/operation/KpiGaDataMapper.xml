<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.KpiGaDataMapper">
    
    <resultMap type="com.ai.operation.domain.KpiGaData" id="KpiGaDataResult">
        <result property="id"    column="id"    />
        <result property="date"    column="date"    />
        <result property="property"    column="property"    />
        <result property="platform"    column="platform"    />
        <result property="dau"    column="dau"    />
        <result property="wau"    column="wau"    />
        <result property="mau"    column="mau"    />
        <result property="usaDau"    column="usa_dau"    />
        <result property="usaWau"    column="usa_wau"    />
        <result property="usaMau"    column="usa_mau"    />
        <result property="todayPageViews"    column="today_page_views"    />
        <result property="weekPageViews"    column="week_page_views"    />
        <result property="monthPageViews"    column="month_page_views"    />
        <result property="todayNewUsers"    column="today_new_users"    />
        <result property="weekNewUsers"    column="week_new_users"    />
        <result property="monthNewUsers"    column="month_new_users"    />
        <result property="session"    column="session"    />
        <result property="download"    column="download"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKpiGaDataVo">
        select id, date, property, platform, dau, wau, mau, usa_dau, usa_wau, usa_mau, today_page_views, week_page_views, month_page_views, today_new_users, week_new_users, month_new_users, session, download, create_by, create_time, update_by, update_time from kpi_ga_data
    </sql>

    <select id="selectKpiGaDataList" parameterType="com.ai.operation.domain.KpiGaData" resultMap="KpiGaDataResult">
        <include refid="selectKpiGaDataVo"/>
        <where>  
            <if test="date != null "> and date = #{date}</if>
            <if test="property != null  and property != ''"> and property = #{property}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectKpiGaDataById" parameterType="Long" resultMap="KpiGaDataResult">
        <include refid="selectKpiGaDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertKpiGaData" parameterType="com.ai.operation.domain.KpiGaData" useGeneratedKeys="true" keyProperty="id">
        insert into kpi_ga_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="date != null">date,</if>
            <if test="property != null">property,</if>
            <if test="platform != null">platform,</if>
            <if test="dau != null">dau,</if>
            <if test="wau != null">wau,</if>
            <if test="mau != null">mau,</if>
            <if test="usaDau != null">usa_dau,</if>
            <if test="usaWau != null">usa_wau,</if>
            <if test="usaMau != null">usa_mau,</if>
            <if test="todayPageViews != null">today_page_views,</if>
            <if test="weekPageViews != null">week_page_views,</if>
            <if test="monthPageViews != null">month_page_views,</if>
            <if test="todayNewUsers != null">today_new_users,</if>
            <if test="weekNewUsers != null">week_new_users,</if>
            <if test="monthNewUsers != null">month_new_users,</if>
            <if test="session != null">session,</if>
            <if test="download != null">download,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="date != null">#{date},</if>
            <if test="property != null">#{property},</if>
            <if test="platform != null">#{platform},</if>
            <if test="dau != null">#{dau},</if>
            <if test="wau != null">#{wau},</if>
            <if test="mau != null">#{mau},</if>
            <if test="usaDau != null">#{usaDau},</if>
            <if test="usaWau != null">#{usaWau},</if>
            <if test="usaMau != null">#{usaMau},</if>
            <if test="todayPageViews != null">#{todayPageViews},</if>
            <if test="weekPageViews != null">#{weekPageViews},</if>
            <if test="monthPageViews != null">#{monthPageViews},</if>
            <if test="todayNewUsers != null">#{todayNewUsers},</if>
            <if test="weekNewUsers != null">#{weekNewUsers},</if>
            <if test="monthNewUsers != null">#{monthNewUsers},</if>
            <if test="session != null">#{session},</if>
            <if test="download != null">#{download},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKpiGaData" parameterType="com.ai.operation.domain.KpiGaData">
        update kpi_ga_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="date != null">date = #{date},</if>
            <if test="property != null">property = #{property},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="dau != null">dau = #{dau},</if>
            <if test="wau != null">wau = #{wau},</if>
            <if test="mau != null">mau = #{mau},</if>
            <if test="usaDau != null">usa_dau = #{usaDau},</if>
            <if test="usaWau != null">usa_wau = #{usaWau},</if>
            <if test="usaMau != null">usa_mau = #{usaMau},</if>
            <if test="todayPageViews != null">today_page_views = #{todayPageViews},</if>
            <if test="weekPageViews != null">week_page_views = #{weekPageViews},</if>
            <if test="monthPageViews != null">month_page_views = #{monthPageViews},</if>
            <if test="todayNewUsers != null">today_new_users = #{todayNewUsers},</if>
            <if test="weekNewUsers != null">week_new_users = #{weekNewUsers},</if>
            <if test="monthNewUsers != null">month_new_users = #{monthNewUsers},</if>
            <if test="session != null">session = #{session},</if>
            <if test="download != null">download = #{download},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKpiGaDataById" parameterType="Long">
        delete from kpi_ga_data where id = #{id}
    </delete>

    <delete id="deleteKpiGaDataByIds" parameterType="String">
        delete from kpi_ga_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>