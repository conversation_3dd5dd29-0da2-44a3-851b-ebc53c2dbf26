<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.GptSysUpdateMapper">
    
    <resultMap type="com.ai.operation.domain.GptSysUpdate" id="GptSysUpdateResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="introduction"    column="introduction"    />
        <result property="details"    column="details"    />
        <result property="platform"    column="platform"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="publish"    column="publish"    />
        <result property="publishTime"    column="publish_time"    />
    </resultMap>

    <sql id="selectGptSysUpdateVo">
        select id, title, introduction, platform, details, create_time, update_time, create_by, update_by, publish, publish_time from gpt_sys_update
    </sql>

    <select id="selectGptSysUpdateList" parameterType="com.ai.operation.domain.GptSysUpdate" resultMap="GptSysUpdateResult">
        <include refid="selectGptSysUpdateVo"/>
        <where>  
            <if test="title != null  and title != ''"> and (introduction like concat('%',#{title},'%') or title like concat('%',#{title},'%'))</if>
            <if test="publish != null "> and publish = #{publish}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="params !=null and  params.beginPublishTime != null and params.beginPublishTime != '' and params.endPublishTime != null and params.endPublishTime != ''"> and publish_time between #{params.beginPublishTime} and #{params.endPublishTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptSysUpdateById" parameterType="Long" resultMap="GptSysUpdateResult">
        <include refid="selectGptSysUpdateVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptSysUpdate" parameterType="com.ai.operation.domain.GptSysUpdate">
        insert into gpt_sys_update
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="introduction != null">introduction,</if>
            <if test="platform != null">platform,</if>
            <if test="details != null">details,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="publish != null">publish,</if>
            <if test="publishTime != null">publish_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="platform != null">#{platform},</if>
            <if test="details != null">#{details},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="publish != null">#{publish},</if>
            <if test="publishTime != null">#{publishTime},</if>
         </trim>
    </insert>

    <update id="updateGptSysUpdate" parameterType="com.ai.operation.domain.GptSysUpdate">
        update gpt_sys_update
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="details != null">details = #{details},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="publish != null">publish = #{publish},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptSysUpdateById" parameterType="Long">
        delete from gpt_sys_update where id = #{id}
    </delete>

    <delete id="deleteGptSysUpdateByIds" parameterType="String">
        delete from gpt_sys_update where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>