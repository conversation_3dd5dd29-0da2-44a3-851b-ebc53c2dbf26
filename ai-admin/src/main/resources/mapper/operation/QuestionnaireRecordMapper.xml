<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.QuestionnaireRecordMapper">

    <resultMap type="com.ai.operation.domain.QuestionnaireRecord" id="QuestionnaireRecordResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="introduction"    column="introduction"    />
        <result property="details"    column="details"    />
        <result property="platform"    column="platform"    />
        <result property="publish"    column="publish"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="del"    column="del"    />
    </resultMap>

    <sql id="selectQuestionnaireRecordVo">
        select id, title, introduction ,details, platform, publish, start_time, end_time, create_time, update_time, create_by, update_by, del from questionnaire_record
    </sql>

    <select id="selectQuestionnaireRecordList" parameterType="com.ai.operation.domain.QuestionnaireRecord" resultMap="QuestionnaireRecordResult">
        <include refid="selectQuestionnaireRecordVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="introduction != null  and introduction != ''"> and introduction like concat('%', #{introduction}, '%')</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="publish != null "> and publish = #{publish}</if>
            <if test="params !=null and  params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime} and #{params.endStartTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectQuestionnaireRecordById" parameterType="Long" resultMap="QuestionnaireRecordResult">
        <include refid="selectQuestionnaireRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertQuestionnaireRecord" parameterType="com.ai.operation.domain.QuestionnaireRecord">
        insert into questionnaire_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="introduction != null">introduction,</if>
            <if test="details != null">details,</if>
            <if test="platform != null">platform,</if>
            <if test="publish != null">publish,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="del != null">del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="details != null">#{details},</if>
            <if test="platform != null">#{platform},</if>
            <if test="publish != null">#{publish},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="del != null">#{del},</if>
        </trim>
    </insert>

    <update id="updateQuestionnaireRecord" parameterType="com.ai.operation.domain.QuestionnaireRecord">
        update questionnaire_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="details != null">details = #{details},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="publish != null">publish = #{publish},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="del != null">del = #{del},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuestionnaireRecordById" parameterType="Long">
        delete from questionnaire_record where id = #{id}
    </delete>

    <delete id="deleteQuestionnaireRecordByIds" parameterType="String">
        delete from questionnaire_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>