<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.GptPlatformActivityMapper">
    
    <resultMap type="com.ai.operation.domain.GptPlatformActivity" id="GptPlatformActivityResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="introduction"    column="introduction"    />
        <result property="linkAddress"    column="link_address"    />
        <result property="details"    column="details"    />
        <result property="userType"    column="user_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="publish"    column="publish"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="platform"    column="platform"    />

    </resultMap>

    <sql id="selectGptPlatformActivityVo">
        select id, title, introduction, link_address, user_type,details, create_time, update_time, create_by, update_by, publish, publish_time,platform from gpt_platform_activity
    </sql>

    <select id="selectGptPlatformActivityList" parameterType="com.ai.operation.domain.GptPlatformActivity" resultMap="GptPlatformActivityResult">
        <include refid="selectGptPlatformActivityVo"/>
        <where>  
            <if test="title != null  and title != ''"> and (title like concat('%', #{title}, '%') or introduction like concat('%', #{title}, '%'))</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="publish != null "> and publish = #{publish}</if>
            <if test="platform != null  and platform != ''"> and platform like concat('%', #{platform}, '%')</if>
            <if test="params !=null and  params.beginPublishTime != null and params.beginPublishTime != '' and params.endPublishTime != null and params.endPublishTime != ''"> and publish_time between #{params.beginPublishTime} and #{params.endPublishTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptPlatformActivityById" parameterType="Long" resultMap="GptPlatformActivityResult">
        <include refid="selectGptPlatformActivityVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptPlatformActivity" parameterType="com.ai.operation.domain.GptPlatformActivity">
        insert into gpt_platform_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="introduction != null">introduction,</if>
            <if test="linkAddress != null">link_address,</if>
            <if test="userType != null">user_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="publish != null">publish,</if>
            <if test="publishTime != null">publish_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="linkAddress != null">#{linkAddress},</if>
            <if test="userType != null">#{userType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="publish != null">#{publish},</if>
            <if test="publishTime != null">#{publishTime},</if>
         </trim>
    </insert>

    <update id="updateGptPlatformActivity" parameterType="com.ai.operation.domain.GptPlatformActivity">
        update gpt_platform_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="linkAddress != null">link_address = #{linkAddress},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="details != null">details = #{details},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="publish != null">publish = #{publish},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptPlatformActivityById" parameterType="Long">
        delete from gpt_platform_activity where id = #{id}
    </delete>

    <delete id="deleteGptPlatformActivityByIds" parameterType="String">
        delete from gpt_platform_activity where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUserTypeCount" resultType="com.ai.operation.domain.vo.UserTypeCountVo" >
        select count(*) as number,user_type,platform from gpt_platform_activity where publish = true group by user_type,platform
    </select>
</mapper>