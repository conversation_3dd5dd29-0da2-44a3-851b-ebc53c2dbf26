<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.StripeScheduleRecordMapper">
    
    <resultMap type="com.ai.orders.domain.StripeScheduleRecord" id="StripeScheduleRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="subscriptionId"    column="subscription_id"    />
        <result property="subscriptionScheduleId"    column="subscription_schedule_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="priceId"    column="price_id"    />
        <result property="scheduleStatus"    column="schedule_status"    />
        <result property="subInterval"    column="sub_interval"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="cancelledAt"    column="cancelled_at"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectStripeScheduleRecordVo">
        select id, user_id, login_name, subscription_id, subscription_schedule_id, customer_id, price_id, schedule_status, sub_interval, start_date, end_date, cancelled_at, create_time, update_time, create_by, update_by from stripe_schedule_record
    </sql>

    <select id="selectStripeScheduleRecordList" parameterType="com.ai.orders.domain.StripeScheduleRecord" resultMap="StripeScheduleRecordResult">
        <include refid="selectStripeScheduleRecordVo"/>
        <where>  
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="subscriptionId != null  and subscriptionId != ''"> and subscription_id = #{subscriptionId}</if>
            <if test="subscriptionScheduleId != null  and subscriptionScheduleId != ''"> and subscription_schedule_id = #{subscriptionScheduleId}</if>
            <if test="scheduleStatus != null  and scheduleStatus != ''"> and schedule_status = #{scheduleStatus}</if>
            <if test="subInterval != null  and subInterval != ''"> and sub_interval = #{subInterval}</if>
            <if test="params !=null and params.beginStartDate != null and params.beginStartDate != '' and params.endStartDate != null and params.endStartDate != ''"> and start_date between #{params.beginStartDate} and #{params.endStartDate}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectStripeScheduleRecordById" parameterType="Long" resultMap="StripeScheduleRecordResult">
        <include refid="selectStripeScheduleRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertStripeScheduleRecord" parameterType="com.ai.orders.domain.StripeScheduleRecord">
        insert into stripe_schedule_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="subscriptionId != null and subscriptionId != ''">subscription_id,</if>
            <if test="subscriptionScheduleId != null and subscriptionScheduleId != ''">subscription_schedule_id,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="priceId != null and priceId != ''">price_id,</if>
            <if test="scheduleStatus != null">schedule_status,</if>
            <if test="subInterval != null and subInterval != ''">sub_interval,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="cancelledAt != null">cancelled_at,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="subscriptionId != null and subscriptionId != ''">#{subscriptionId},</if>
            <if test="subscriptionScheduleId != null and subscriptionScheduleId != ''">#{subscriptionScheduleId},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="priceId != null and priceId != ''">#{priceId},</if>
            <if test="scheduleStatus != null">#{scheduleStatus},</if>
            <if test="subInterval != null and subInterval != ''">#{subInterval},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="cancelledAt != null">#{cancelledAt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateStripeScheduleRecord" parameterType="com.ai.orders.domain.StripeScheduleRecord">
        update stripe_schedule_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="subscriptionId != null and subscriptionId != ''">subscription_id = #{subscriptionId},</if>
            <if test="subscriptionScheduleId != null and subscriptionScheduleId != ''">subscription_schedule_id = #{subscriptionScheduleId},</if>
            <if test="customerId != null and customerId != ''">customer_id = #{customerId},</if>
            <if test="priceId != null and priceId != ''">price_id = #{priceId},</if>
            <if test="scheduleStatus != null">schedule_status = #{scheduleStatus},</if>
            <if test="subInterval != null and subInterval != ''">sub_interval = #{subInterval},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="cancelledAt != null">cancelled_at = #{cancelledAt},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStripeScheduleRecordById" parameterType="Long">
        delete from stripe_schedule_record where id = #{id}
    </delete>

    <delete id="deleteStripeScheduleRecordByIds" parameterType="String">
        delete from stripe_schedule_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>