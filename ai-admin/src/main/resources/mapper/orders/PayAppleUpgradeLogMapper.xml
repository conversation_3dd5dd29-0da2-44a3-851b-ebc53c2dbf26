<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.PayAppleUpgradeLogMapper">
    
    <resultMap type="com.ai.orders.domain.PayAppleUpgradeLog" id="PayAppleUpgradeLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="productId"    column="product_id"    />
        <result property="originProductId"    column="origin_product_id"    />
        <result property="originalTransactionId"    column="original_transaction_id"    />
        <result property="triggerTransactionId"    column="trigger_transaction_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectPayAppleUpgradeLogVo">
        select id, user_id, login_name, product_id, origin_product_id, original_transaction_id, trigger_transaction_id, create_time, update_time, create_by, update_by from pay_apple_upgrade_log
    </sql>

    <select id="selectPayAppleUpgradeLogList" parameterType="com.ai.orders.domain.PayAppleUpgradeLog" resultMap="PayAppleUpgradeLogResult">
        <include refid="selectPayAppleUpgradeLogVo"/>
        <where>  
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
            <if test="originProductId != null  and originProductId != ''"> and origin_product_id = #{originProductId}</if>
            <if test="originalTransactionId != null  and originalTransactionId != ''"> and original_transaction_id = #{originalTransactionId}</if>
            <if test="triggerTransactionId != null  and triggerTransactionId != ''"> and trigger_transaction_id = #{triggerTransactionId}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPayAppleUpgradeLogById" parameterType="Long" resultMap="PayAppleUpgradeLogResult">
        <include refid="selectPayAppleUpgradeLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertPayAppleUpgradeLog" parameterType="com.ai.orders.domain.PayAppleUpgradeLog">
        insert into pay_apple_upgrade_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="productId != null">product_id,</if>
            <if test="originProductId != null">origin_product_id,</if>
            <if test="originalTransactionId != null">original_transaction_id,</if>
            <if test="triggerTransactionId != null">trigger_transaction_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="productId != null">#{productId},</if>
            <if test="originProductId != null">#{originProductId},</if>
            <if test="originalTransactionId != null">#{originalTransactionId},</if>
            <if test="triggerTransactionId != null">#{triggerTransactionId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePayAppleUpgradeLog" parameterType="com.ai.orders.domain.PayAppleUpgradeLog">
        update pay_apple_upgrade_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="originProductId != null">origin_product_id = #{originProductId},</if>
            <if test="originalTransactionId != null">original_transaction_id = #{originalTransactionId},</if>
            <if test="triggerTransactionId != null">trigger_transaction_id = #{triggerTransactionId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayAppleUpgradeLogById" parameterType="Long">
        delete from pay_apple_upgrade_log where id = #{id}
    </delete>

    <delete id="deletePayAppleUpgradeLogByIds" parameterType="String">
        delete from pay_apple_upgrade_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>