<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.PayAppleRestoreLogMapper">
    
    <resultMap type="com.ai.orders.domain.PayAppleRestoreLog" id="PayAppleRestoreLogResult">
        <result property="id"    column="id"    />
        <result property="fromUserId"    column="from_user_id"    />
        <result property="fromLoginName"    column="from_login_name"    />
        <result property="toUserId"    column="to_user_id"    />
        <result property="toLoginName"    column="to_login_name"    />
        <result property="originalTransactionId"    column="original_transaction_id"    />
        <result property="triggerTransactionId"    column="trigger_transaction_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectPayAppleRestoreLogVo">
        select id, from_user_id, from_login_name, to_user_id, to_login_name, original_transaction_id, trigger_transaction_id, create_time, update_time, create_by, update_by from pay_apple_restore_log
    </sql>

    <select id="selectPayAppleRestoreLogList" parameterType="com.ai.orders.domain.PayAppleRestoreLog" resultMap="PayAppleRestoreLogResult">
        <include refid="selectPayAppleRestoreLogVo"/>
        <where>  
            <if test="fromLoginName != null  and fromLoginName != ''"> and from_login_name = #{fromLoginName}</if>
            <if test="toLoginName != null  and toLoginName != ''"> and to_login_name = #{toLoginName}</if>
            <if test="originalTransactionId != null  and originalTransactionId != ''"> and original_transaction_id = #{originalTransactionId}</if>
            <if test="triggerTransactionId != null  and triggerTransactionId != ''"> and trigger_transaction_id = #{triggerTransactionId}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPayAppleRestoreLogById" parameterType="Long" resultMap="PayAppleRestoreLogResult">
        <include refid="selectPayAppleRestoreLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertPayAppleRestoreLog" parameterType="com.ai.orders.domain.PayAppleRestoreLog">
        insert into pay_apple_restore_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="fromLoginName != null">from_login_name,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="toLoginName != null">to_login_name,</if>
            <if test="originalTransactionId != null">original_transaction_id,</if>
            <if test="triggerTransactionId != null">trigger_transaction_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="fromLoginName != null">#{fromLoginName},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="toLoginName != null">#{toLoginName},</if>
            <if test="originalTransactionId != null">#{originalTransactionId},</if>
            <if test="triggerTransactionId != null">#{triggerTransactionId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePayAppleRestoreLog" parameterType="com.ai.orders.domain.PayAppleRestoreLog">
        update pay_apple_restore_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
            <if test="fromLoginName != null">from_login_name = #{fromLoginName},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="toLoginName != null">to_login_name = #{toLoginName},</if>
            <if test="originalTransactionId != null">original_transaction_id = #{originalTransactionId},</if>
            <if test="triggerTransactionId != null">trigger_transaction_id = #{triggerTransactionId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayAppleRestoreLogById" parameterType="Long">
        delete from pay_apple_restore_log where id = #{id}
    </delete>

    <delete id="deletePayAppleRestoreLogByIds" parameterType="String">
        delete from pay_apple_restore_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>