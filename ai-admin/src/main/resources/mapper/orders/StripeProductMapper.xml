<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.StripeProductMapper">
    
    <resultMap type="com.ai.orders.domain.StripeProduct" id="StripeProductResult">
        <result property="id"    column="id"    />
        <result property="stripeProductId"    column="stripe_product_id"    />
        <result property="stripePriceId"    column="stripe_price_id"    />
        <result property="planLevel"    column="plan_level"    />
        <result property="lumen"    column="lumen"    />
        <result property="productType"    column="product_type"    />
        <result property="priceInterval"    column="price_interval"    />
        <result property="vipLevel"    column="vip_level"    />
        <result property="mark"    column="mark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectStripeProductVo">
        select id, stripe_product_id, stripe_price_id, plan_level, lumen, product_type, price_interval, vip_level, mark, create_time, update_time, create_by, update_by from stripe_product
    </sql>

    <select id="selectStripeProductList" parameterType="com.ai.orders.domain.StripeProduct" resultMap="StripeProductResult">
        <include refid="selectStripeProductVo"/>
        <where>  
            <if test="stripeProductId != null  and stripeProductId != ''"> and stripe_product_id = #{stripeProductId}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="priceInterval != null  and priceInterval != ''"> and price_interval = #{priceInterval}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectStripeProductById" parameterType="Long" resultMap="StripeProductResult">
        <include refid="selectStripeProductVo"/>
        where id = #{id}
    </select>

    <insert id="insertStripeProduct" parameterType="com.ai.orders.domain.StripeProduct" useGeneratedKeys="true" keyProperty="id">
        insert into stripe_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stripeProductId != null and stripeProductId != ''">stripe_product_id,</if>
            <if test="stripePriceId != null and stripePriceId != ''">stripe_price_id,</if>
            <if test="planLevel != null">plan_level,</if>
            <if test="lumen != null">lumen,</if>
            <if test="productType != null and productType != ''">product_type,</if>
            <if test="priceInterval != null">price_interval,</if>
            <if test="vipLevel != null">vip_level,</if>
            <if test="mark != null">mark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stripeProductId != null and stripeProductId != ''">#{stripeProductId},</if>
            <if test="stripePriceId != null and stripePriceId != ''">#{stripePriceId},</if>
            <if test="planLevel != null">#{planLevel},</if>
            <if test="lumen != null">#{lumen},</if>
            <if test="productType != null and productType != ''">#{productType},</if>
            <if test="priceInterval != null">#{priceInterval},</if>
            <if test="vipLevel != null">#{vipLevel},</if>
            <if test="mark != null">#{mark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateStripeProduct" parameterType="com.ai.orders.domain.StripeProduct">
        update stripe_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="stripeProductId != null and stripeProductId != ''">stripe_product_id = #{stripeProductId},</if>
            <if test="stripePriceId != null and stripePriceId != ''">stripe_price_id = #{stripePriceId},</if>
            <if test="planLevel != null">plan_level = #{planLevel},</if>
            <if test="lumen != null">lumen = #{lumen},</if>
            <if test="productType != null and productType != ''">product_type = #{productType},</if>
            <if test="priceInterval != null">price_interval = #{priceInterval},</if>
            <if test="vipLevel != null">vip_level = #{vipLevel},</if>
            <if test="mark != null">mark = #{mark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStripeProductById" parameterType="Long">
        delete from stripe_product where id = #{id}
    </delete>

    <delete id="deleteStripeProductByIds" parameterType="String">
        delete from stripe_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>