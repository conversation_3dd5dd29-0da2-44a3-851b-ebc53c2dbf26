<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.SysLumenAdminOperateLogMapper">

    <resultMap type="com.ai.orders.domain.SysLumenAdminOperateLog" id="SysLumenAdminOperateLogResult">
        <result property="id"    column="id"    />
        <result property="lumenRecordId"    column="lumen_record_id"    />
        <result property="operName"    column="oper_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="beforeChangeLumen"    column="before_change_lumen"    />
        <result property="afterChangeLumen"    column="after_change_lumen"    />
        <result property="lumen"    column="lumen"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysLumenAdminOperateLogVo">
        select id, lumen_record_id, oper_name, dept_name, before_change_lumen, after_change_lumen, lumen, type, create_by, create_time, update_by, update_time from sys_lumen_admin_operate_log
    </sql>

    <select id="selectSysLumenAdminOperateLogList" parameterType="com.ai.orders.domain.SysLumenAdminOperateLog" resultMap="SysLumenAdminOperateLogResult">
        <include refid="selectSysLumenAdminOperateLogVo"/>
        <where>
            <if test="operName != null  and operName != ''"> and oper_name like concat('%', #{operName}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSysLumenAdminOperateLogById" parameterType="Long" resultMap="SysLumenAdminOperateLogResult">
        <include refid="selectSysLumenAdminOperateLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysLumenAdminOperateLog" parameterType="com.ai.orders.domain.SysLumenAdminOperateLog">
        insert into sys_lumen_admin_operate_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lumenRecordId != null">lumen_record_id,</if>
            <if test="operName != null">oper_name,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="beforeChangeLumen != null">before_change_lumen,</if>
            <if test="afterChangeLumen != null">after_change_lumen,</if>
            <if test="lumen != null">lumen,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lumenRecordId != null">#{lumenRecordId},</if>
            <if test="operName != null">#{operName},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="beforeChangeLumen != null">#{beforeChangeLumen},</if>
            <if test="afterChangeLumen != null">#{afterChangeLumen},</if>
            <if test="lumen != null">#{lumen},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateSysLumenAdminOperateLog" parameterType="com.ai.orders.domain.SysLumenAdminOperateLog">
        update sys_lumen_admin_operate_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="lumenRecordId != null">lumen_record_id = #{lumenRecordId},</if>
            <if test="operName != null">oper_name = #{operName},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="beforeChangeLumen != null">before_change_lumen = #{beforeChangeLumen},</if>
            <if test="afterChangeLumen != null">after_change_lumen = #{afterChangeLumen},</if>
            <if test="lumen != null">lumen = #{lumen},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysLumenAdminOperateLogById" parameterType="Long">
        delete from sys_lumen_admin_operate_log where id = #{id}
    </delete>

    <delete id="deleteSysLumenAdminOperateLogByIds" parameterType="String">
        delete from sys_lumen_admin_operate_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>