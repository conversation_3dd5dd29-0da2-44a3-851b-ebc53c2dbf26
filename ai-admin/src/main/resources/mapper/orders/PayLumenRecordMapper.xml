<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.PayLumenRecordMapper">
    
    <resultMap type="com.ai.orders.domain.PayLumenRecord" id="PayLumenRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="currentPeriodEnd"    column="current_period_end"    />
        <result property="currentPeriodStart"    column="current_period_start"    />
        <result property="type"    column="type"    />
        <result property="logicPeriodEnd"    column="logic_period_end"    />
        <result property="logicPeriodStart"    column="logic_period_start"    />
        <result property="lumenQty"    column="lumen_qty"    />
        <result property="lumenLeftQty"    column="lumen_left_qty"    />
        <result property="mixed"    column="mixed"    />
        <result property="invalid"    column="invalid"    />
        <result property="originalTransactionId"    column="original_transaction_id"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="vipPlatForm"    column="vip_plat_form"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectPayLumenRecordVo">
        select id, user_id, login_name, customer_id, current_period_end, current_period_start,type,logic_period_end, logic_period_start, lumen_qty, lumen_left_qty, mixed, invalid,transaction_id,original_transaction_id,vip_plat_form, update_time,create_time, create_by, update_by from pay_lumen_record
    </sql>

    <select id="selectPayLumenRecordList" parameterType="com.ai.orders.domain.PayLumenRecord" resultMap="PayLumenRecordResult">
        <include refid="selectPayLumenRecordVo"/>
        <where>  
            <if test="loginName != null  and loginName != ''"> and login_name like concat('%', #{loginName}, '%')</if>
            <if test="originalTransactionId != null  and originalTransactionId != ''"> and original_transaction_id = #{originalTransactionId}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="currentPeriodStart != null "> and current_period_start = #{currentPeriodStart}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="invalid != null "> and invalid = #{invalid}</if>
            <if test="vipPlatForm != null  and vipPlatForm != ''"> and vip_plat_form = #{vipPlatForm}</if>

        </where>
        order by create_time desc
    </select>
    
    <select id="selectPayLumenRecordById" parameterType="Long" resultMap="PayLumenRecordResult">
        <include refid="selectPayLumenRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertPayLumenRecord" parameterType="com.ai.orders.domain.PayLumenRecord">
        insert into pay_lumen_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="currentPeriodEnd != null">current_period_end,</if>
            <if test="currentPeriodStart != null">current_period_start,</if>
            <if test="type != null">type,</if>
            <if test="logicPeriodEnd != null">logic_period_end,</if>
            <if test="logicPeriodStart != null">logic_period_start,</if>
            <if test="lumenQty != null">lumen_qty,</if>
            <if test="lumenLeftQty != null">lumen_left_qty,</if>
            <if test="mixed != null">mixed,</if>
            <if test="invalid != null">invalid,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="currentPeriodEnd != null">#{currentPeriodEnd},</if>
            <if test="currentPeriodStart != null">#{currentPeriodStart},</if>
            <if test="type != null">#{type},</if>
            <if test="logicPeriodEnd != null">#{logicPeriodEnd},</if>
            <if test="logicPeriodStart != null">#{logicPeriodStart},</if>
            <if test="lumenQty != null">#{lumenQty},</if>
            <if test="lumenLeftQty != null">#{lumenLeftQty},</if>
            <if test="mixed != null">#{mixed},</if>
            <if test="invalid != null">#{invalid},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePayLumenRecord" parameterType="com.ai.orders.domain.PayLumenRecord">
        update pay_lumen_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="currentPeriodEnd != null">current_period_end = #{currentPeriodEnd},</if>
            <if test="currentPeriodStart != null">current_period_start = #{currentPeriodStart},</if>
            <if test="type != null">type = #{type},</if>
            <if test="logicPeriodEnd != null">logic_period_end = #{logicPeriodEnd},</if>
            <if test="logicPeriodStart != null">logic_period_start = #{logicPeriodStart},</if>
            <if test="lumenQty != null">lumen_qty = #{lumenQty},</if>
            <if test="lumenLeftQty != null">lumen_left_qty = #{lumenLeftQty},</if>
            <if test="mixed != null">mixed = #{mixed},</if>
            <if test="invalid != null">invalid = #{invalid},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayLumenRecordById" parameterType="Long">
        delete from pay_lumen_record where id = #{id}
    </delete>

    <delete id="deletePayLumenRecordByIds" parameterType="String">
        delete from pay_lumen_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="statisticsLumenCost" resultType="com.ai.orders.domain.vo.LumenCostStatisticsVo">
        SELECT vip_plat_form,type,sum(lumen_qty) as lumen_qty,sum(lumen_left_qty) as lumen_left_qty,count(distinct login_name)  as userNum
        FROM pay_lumen_record
        WHERE
            UNIX_TIMESTAMP(#{queryStartDate}) >= current_period_start
          AND current_period_end >= UNIX_TIMESTAMP(#{queryEndDate})
        group by type,vip_plat_form
    </select>

    <select id="statisticsLumenQtyNum" resultType="Long">
        select count(distinct login_name) num
        from pay_lumen_record where create_time >=#{queryStartDate}
        and #{queryEndDate} > create_time and  type=2
        <if test="lumenQtyList != null and lumenQtyList.size() > 0">
        and lumen_qty in
        <foreach collection="lumenQtyList" item="qty" open="(" separator="," close=")">
            #{qty}
        </foreach>
    </if>
    </select>

    <select id="statisticsValidVipUserNum" resultType="Long">
        SELECT count(distinct  login_name)
        FROM subscription_current
        WHERE
            UNIX_TIMESTAMP(#{queryStartDate}) >= vip_begin_time
          AND vip_end_time >= UNIX_TIMESTAMP(#{queryEndDate}) and  plan_level = #{planLevel}
    </select>

    <select  id="statisticsUsedUpVipUserNum" resultType="Long">
        select count(distinct  login_name) from
        (
        select   login_name  from pay_lumen_record WHERE
            UNIX_TIMESTAMP(#{queryStartDate}) >= current_period_start
           AND current_period_end >= UNIX_TIMESTAMP(#{queryEndDate})
        and type=2
        <if test="lumenQtyList != null and lumenQtyList.size() > 0">
        and lumen_qty in
        <foreach collection="lumenQtyList" item="qty" open="(" separator="," close=")">
            #{qty}
        </foreach>
        </if>
        group by login_name
        having sum(lumen_left_qty)=0
        )k;
    </select>

    <select id="statisticsSumLumen" resultType="com.ai.orders.domain.vo.SumUsedLumenVo" >
        select count(distinct login_name) userNum,ifnull(sum(lumen_qty),0) as sumLumenQty,ifnull(sum(lumen_left_qty),0) as sumLumenLeftQty from pay_lumen_record WHERE
            UNIX_TIMESTAMP(#{queryStartDate}) >= current_period_start
          AND current_period_end >= UNIX_TIMESTAMP(#{queryEndDate})
        and type=1;
    </select>

    <select id="statisticsNewNum" resultType="com.ai.orders.domain.vo.NewNumVo">
        select  count(distinct login_name) userNum,count(id) payNum
        from pay_lumen_record where create_time >=#{queryStartDate}
                                and #{queryEndDate} > create_time and  type=1
    </select>

</mapper>