<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.EventLogMapper">

    <select id="getUseOperateNumsByTime"  parameterType="java.util.List" resultType="java.lang.Long">
        select count(*) from (
        select count(DISTINCT  user_id) as num from gpt_event_log where user_id is not null
        <if test="startDate != null">
            and create_time >= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            and create_time <![CDATA[ < ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        group by user_id
        ) t
    </select>

    <select id="getUserIdByTime" resultType="java.lang.Long">
        select DISTINCT  user_id  from gpt_event_log where user_id is not null and  user_id!=-1
        <if test="startDate != null">
            and create_time >= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            and create_time <![CDATA[ < ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        group by user_id
    </select>


</mapper>
