<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!--[if !mso]><!-->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <!--<![endif]-->
  <title>Subject: We've Got Your Message at PicLumen</title>

  <style type="text/css">
    /* 重置样式 */
    body,
    table,
    td,
    div,
    p,
    a {
      margin: 0;
      padding: 0;
      -webkit-font-smoothing: antialiased;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      font-family: Arial, sans-serif;
    }

    img {
      border: 0;
      line-height: 100%;
      outline: none;
      text-decoration: none;
      -ms-interpolation-mode: bicubic;
      display: block;
    }

    @media screen and (max-width: 375px) {
      .mobile-shell {
        width: 100% !important;
        min-width: 100% !important;
      }

      .mobile-padding {
        padding: 20px !important;
      }

      .mobile-hero-text {
        font-size: 24px !important;
        line-height: 32px !important;
      }
    }
  </style>
</head>

<body style="margin: 0; padding: 0; -webkit-font-smoothing: antialiased;background-color: #000;">

  <!-- 使用VML背景图片 for Outlook -->
  <!--[if gte mso 9]>
<v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
    <v:fill type="tile" src="https://img.piclumen.com/normal/20250225/16/aa1138d1-d23a-40db-98a6-2b636a95454f.webp" />
</v:background>
<![endif]-->

  <!-- 主容器表格 -->
  <table width="100%" border="0" cellspacing="0" cellpadding="0"
    background="https://img.piclumen.com/normal/20250225/16/aa1138d1-d23a-40db-98a6-2b636a95454f.webp"
    style="background-image: url('https://img.piclumen.com/normal/20250225/16/aa1138d1-d23a-40db-98a6-2b636a95454f.webp'); background-position: center top; background-repeat: no-repeat; background-size: cover; max-width: 375px;margin: auto;">
    <tr>
      <td align="center" valign="top">
        <!-- 半透明叠加层，用于确保内容可读性 -->
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="center" valign="top">
              <!-- 内容容器 -->
              <table width="375" border="0" cellspacing="0" cellpadding="0" class="mobile-shell">
                <!-- Logo部分 -->
                <tr>
                  <td class="mobile-padding" style="padding: 40px 30px 20px 30px;">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td align="center"> </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- 欢迎标题 -->
                <tr>
                  <td class="mobile-padding" style="padding: 50px 30px 20px 30px;">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td align="left" class="mobile-hero-text"
                          style="color: #ffffff; font-size: 20px; font-weight: bold; ">
                          Dear PicLumen User,
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- 欢迎文本 -->
                <tr>
                  <td class="mobile-padding" style="padding: 0 30px; ">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td align="left" style="color: #ffffff; font-size: 14px; line-height: 18px;">
                          Thank you for your continued support and trust in our website.
                          We have received your request to export all image generation
                          prompt histories. In order to help you better regenerate these
                          images, we have compiled the parameters and prompts
                          you used during generation and attached them to this email for
                          your reference.
                          <br>
                          <br>
                          How to use:
                          <br>
                          Simply copy the prompt-params from the attachment file to the
                          create page's prompt input field and press "Load Params" button
                          on our website to start regenerating your images.
                          <br>
                          <br>
                          If you
                          encounter any issues or need further assistance, please feel free to
                          contact our customer support team. We will be at your service!
                          <br>
                          <br>
                          Thank you once again for your understanding. We wish you a pleasant experience!
                          <br>
                          <br>
                          Best regards,
                          <br>
                          The PicLumen Team
                        </td>
                      </tr>
                      <tr>
                        <td align="left"
                          style="color: #ffffff; font-size: 14px; line-height: 18px;padding:24px 0 0 0 ;">

                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- 图片画廊 -->
                <tr>
                  <td class="mobile-padding" style="padding:150px 50px;">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td>
                          <!-- <img src="gallery.jpg" width="375"
                            style="width: 100%; max-width: 375px; height: auto; display: block;" alt="AI Art Gallery" /> -->
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- 页脚 -->
                <tr>
                  <td style="padding: 40px;">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td align="center" style="color: #fff; font-size: 12px; padding:10px 20px;">
                          PicLumen.com | Pic the Future with AI
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>

</html>