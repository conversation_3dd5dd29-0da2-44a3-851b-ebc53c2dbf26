逻辑新增联系我们:"/api/contacts/create"
查询联系我们列表:"/api/contacts/list"
获取支付记录详情:"/api/user-pay-record"
获取当前用户支付记录:"/api/user-pay-record/list"
根据平台获取当前用户支付记录:"/api/user-pay-record/list/platform"
分页查询当前用户支付记录展示:"/api/user-pay-record/page-search-display"
查询用户能首充:"/api/vip/cant-first-gift"
查询用户的点数信息(兼容移动端后续删除):"/api/vip/lumens-message"
查询用户的点数信息:"/api/vip/lumens-message-detail"
查询用户的充值点数信息:"/api/vip/lumens-recharge"
查询vip的标准信息:"/api/vip/resource-list"
查询用户能否试用:"/api/vip/trail-flag"
用户账号删除:"/api/user/account-deletion"
用户注册(手机端):"/api/user/app-register"
Apple登录:"/api/user/apple/login"
校验用户登录名称是否已经存在:"/api/user/check-login-name"
校验用户昵称是否已经存在:"/api/user/check-user-name"
查询用户详情:"/api/user/info"
登录:"/api/user/login"
第三方登录，目前支持Google:"/api/user/login-oauth"
Google OAuth2.0登录:"/api/user/login-oauth-with-code"
退出登录:"/api/user/logout"
查询用户lumen记录:"/api/user/lumen-change-record"
用户注册:"/api/user/register"
校验用户注册的验证码是否正确:"/api/user/register-check-vCode"
用户注册，发送验证码:"/api/user/register-send-code"
校验用户忘记密码的验证码是否正确:"/api/user/reset-check-vCode"
重置密码:"/api/user/reset-password"
忘记密码，发送验证码:"/api/user/reset-password-send-code"
查询用户信息(包含社区和lumen币):"/api/user/select-user-detail"
修改用户信息:"/api/user/update-user-info"
上传用户头像:"/api/user/upload-user-avatar"
查询用户当前可用lumen点数:"/api/user/user-current-lumen"
新增用户预载任务:"/api/task/add-task-queue"
批量排队任务流程:"/api/task/batch-process-task"
取消任务:"/api/task/cancelTask"
删除用户预载任务:"/api/task/delete-task-queue"
预载任务生图调用:"/api/task/execute-task"
排队任务流程:"/api/task/processTask"
获取用户预载任务:"/api/task/select-task-queue"
customerCallback:"/api/stripe/webhook/customer"
生成账单:"/api/stripe/pay/billing"
取消订阅自动续费:"/api/stripe/pay/cancel-future"
取消还未生效的未来订阅:"/api/stripe/pay/cancelSubscriptionSchedule"
改变未来订阅:"/api/stripe/pay/changeSubscription"
发票和payment状态校对:"/api/stripe/pay/checkInvoiceAndPayment"
购买或订阅产品:"/api/stripe/pay/create-payment"
getSubscription:"/api/stripe/pay/fixVip"
获取还未生效的未来订阅:"/api/stripe/pay/getSubscriptionSchedule"
恢复订阅自动续费:"/api/stripe/pay/uncancel"
升级订阅:"/api/stripe/pay/upgradeSubscription"
批量去背景获取临时COS token:"/api/rmbg/batch-rmbg-tmp-auth"
删除生成的图片:"/api/rmbg/rmbg-del"
查询当前用户生成的图片:"/api/rmbg/rmbg-history"
查询状态:"/api/rmbg/rmbg-status"
保存去背景用户上传:"/api/rmbg/saveRmbg"
提交问卷并领取lumen:"/api/questionnaire/execute-answer"
查询问卷的内容:"/api/questionnaire/select-questionnaire-detail"
webhook:"/api/paypal/webhook"
取消订阅:"/api/paypal/cancel"
购买或订阅产品:"/api/paypal/create-payment"
查询即将生效的订阅:"/api/paypal/query-valid-not-handle-subscriptions"
升级-降级订阅:"/api/paypal/upgrade-downgrade"
保存支付来源日志:"/api/pay/source/log/add"
吉卜力风格转换接口:"/api/open/img-to-ghibli"
图片反推提示词:"/api/open/img-to-text"
翻译接口:"/api/open/translate-to-language"
mj图片生成:"/api/midjourney/create"
生成图像:"/api/midjourney/imagine"
Prompt效验:"/api/midjourney/prompt-check"
Midjourney回调处理:"/api/midjourney/callback/webhook"
PUSH打开消息上报:"/api/message/push-open-report"
用户拉起广告:"/api/lumen-task/begin-watch-ad"
完成加入社区或者分享图片的任务:"/api/lumen-task/finish-tasks"
用户完成观看广告任务:"/api/lumen-task/finish-watch-ad"
获取lumen任务领取日志:"/api/lumen-task/get-task-reward-log"
领取对应任务的lumen币:"/api/lumen-task/receive-task-reward"
获取lumen任务完成列表:"/api/lumen-task/select-tasks"
获取用户观看广告任务详情:"/api/lumen-task/watch-ad-task-detail"
用户点赞:"/api/img/add-like"
用户举报:"/api/img/add-report"
用户批量删除图片:"/api/img/batch-deletes-img"
未登录分页查询:"/api/img/common-explore-list"
删除某张图片:"/api/img/delete"
分页查询Home页图片:"/api/img/explore-list"
返回社区的示例图:"/api/img/feed"
查询当前用户生成的图片:"/api/img/gen-history/history-list"
查询具体图片信息:"/api/img/img-detail"
公开图片:"/api/img/public-img"
今日能公开图片剩余数量:"/api/img/public-img-surplus-nums"
随机查询图片:"/api/img/random-list"
用户取消点赞:"/api/img/reduce-like"
查询当前用户某个任务的图片:"/api/img/select-file-record"
查询用户当前生图统计信息:"/api/img/statistics-img-nums"
删除个人图片控制:"/api/img-control/delete"
提取图片的内容控制:"/api/img-control/extract"
保存个人图片控制:"/api/img-control/save"
查询个人图片控制:"/api/img-control/select-list"
图片结果回调:"/api/gen/call-back"
图片生成:"/api/gen/create"
图片编辑:"/api/gen/edit"
扩图:"/api/gen/enlarge-image"
图片高清修复:"/api/gen/hires-fix"
单个任务完成情况查询:"/api/gen/history"
局部重绘(new):"/api/gen/inpaint"
线稿上色:"/api/gen/lineRecolor"
局部重绘:"/api/gen/local-redraw"
获取模型列表:"/api/gen/model/list"
用户未完成任务查询:"/api/gen/not-finish-task"
通用生图"/api/gen/prompt"
获取一个随机提示词:"/api/gen/random-prompt"
图片去背景:"/api/gen/remove-background"
用户未完成任务查询:"/api/gen/task-queue"
Flux图片生成:"/api/flux/create"
Flux Kontext Pro图像生成:"/api/flux/kontext-pro"
获取Flux任务结果:"/api/flux/result"
checkUserCanUpload:"/api/custom-file/check-can-upload"
保存用户上传:"/api/custom-file/save"
获取广告配置:"/api/common/ad-config"
返回android版本号:"/api/common/androidVersion"
callback:"/api/common/callback"
用户导出生图入参excel:"/api/common/genInfo-export"
获取系统时间:"/api/common/getSysTime"
hello:"/api/common/hello"
返回ios版本号:"/api/common/iosVersion"
statisticsCreatePicture:"/api/common/statistics"
获取停服通知时间:"/api/common/suspension-time"
翻译，中文翻译为英文:"/api/common/translate"
返回接口版本号:"/api/common/version"
新增社区用户:"/api/comm-user/add-user"
查询社区用户信息:"/api/comm-user/select-user"
对社区评论进行举报:"/api/comm-report/report-comment"
处理全部已读消息:"/api/comm-message/deal-read-all"
处理已读消息:"/api/comm-message/deal-read-message"
获取公告通知已读未读的信息:"/api/comm-message/select-activity-message"
获取评论已读未读的信息:"/api/comm-message/select-comment-message"
获取点赞已读未读的信息:"/api/comm-message/select-like-message"
获取用户未读信息数:"/api/comm-message/select-message-nums"
获取平台消息已读未读的信息:"/api/comm-message/select-platform-message"
获取系统更新已读未读的信息:"/api/comm-message/select-sysUpdate-message"
对社区评论进行点赞:"/api/comm-like/add-like-comment"
对社区图片进行点赞:"/api/comm-like/add-like-file"
对社区评论取消点赞:"/api/comm-like/reduce-like-comment"
对社区图片取消点赞:"/api/comm-like/reduce-like-file"
获取社区图片点赞列表:"/api/comm-like/select-like"
获取社区首页banner图片:"/api/comm-img/banner-img-list"
获取社区首页banner图片(ios):"/api/comm-img/banner-img-list-ios"
分页查询社区图片（未登录）:"/api/comm-img/common-page-search"
删除社区图片:"/api/comm-img/delete"
删除社区图片(批量):"/api/comm-img/delete-batch"
查询社区图片详情:"/api/comm-img/img-detail"
查询社区用户图片详情:"/api/comm-img/img-particular"
个人主页社区图片:"/api/comm-img/page-personal-search"
分页查询社区图片:"/api/comm-img/page-search"
分页查询社区图片v2:"/api/comm-img/page-search-v2"
分享或者remix:"/api/comm-img/share-remix"
修改图片prompt可见性:"/api/comm-img/view-change"
修改图片prompt可见性(批量):"/api/comm-img/view-change-batch"
对社区图片进行举报:"/api/comm-img-report/report-img"
关注用户:"/api/comm-follow/add-follow"
查询当前用户是否被关注:"/api/comm-follow/judge-follow"
取消关注用户:"/api/comm-follow/reduce-follow"
获取用户关注或者粉丝列表:"/api/comm-follow/select-follow"
获取用户关注或者粉丝列表V2:"/api/comm-follow/select-followV2"
对社区评论进行评论:"/api/comm-comment/add-comment-comment"
对社区图片进行评论:"/api/comm-comment/add-comment-file"
删除评论的评论:"/api/comm-comment/delete-comment-comment"
删除图片的评论:"/api/comm-comment/delete-comment-file"
获取社区评论列表:"/api/comm-comment/select-comment"
拉黑用户:"/api/comm-blacklist/add-blacklist"
移除拉黑的用户:"/api/comm-blacklist/reduce-blacklist"
获取拉黑用户列表:"/api/comm-blacklist/select-blacklist"
获取活动详情:"/api/comm-activity/activity-detail"
获取活动列表:"/api/comm-activity/activity-list"
删除社区投稿图片:"/api/comm-activity/delete-activity-img"
查询没有发布社区的历史图片:"/api/comm-activity/not-publish-img-list"
分页查询参赛图片:"/api/comm-activity/page-search"
获取投稿中活动列表:"/api/comm-activity/post-list"
获取奖章列表:"/api/comm-activity/prize-list"
用户活动图片投稿:"/api/comm-activity/public-activity-img"
查询用户某活动投稿图片:"/api/comm-activity/user-activity-posts-list"
分页查询获奖图片:"/api/comm-activity/win-search"
用户收藏:"/api/collect/add"
用户批量收藏:"/api/collect/add-batch"
新增收藏夹:"/api/collect/add-classify"
用户收藏夹列表:"/api/collect/classify-list"
用户删除收藏夹:"/api/collect/delete-classify"
分页查询收藏页图片(web端新接口):"/api/collect/history-collect-list"
分页查询收藏页图片:"/api/collect/list"
用户移到收藏:"/api/collect/move"
用户批量移到收藏:"/api/collect/move-batch"
用户取消收藏:"/api/collect/reduce"
用户取消批量收藏:"/api/collect/reduce-batch"
用户批量取消收藏并删除:"/api/collect/reduce-delete-batch"
重命名收藏夹或修改收藏夹:"/api/collect/rename-classify"
设置封面:"/api/collect/set-cover"
更新用户收藏量:"/api/collect/update-user-collect-num"
获取临时COS token:"/api/bucket/tmp-auth"
创建苹果用户关系:"/api/apple-pay/create-apple-relation"
获取产品:"/api/apple-pay/product"
restore 会员切换:"/api/apple-pay/restore"
v2 验证payload:"/api/apple-pay/validateV2"
在相册查询当前用户上传的图片:"/api/album/album-img-list"
在相册中删除某张用户图片:"/api/album/delete-album-img"
上传用户图片到相册:"/api/album/upload-album-img"
查询支付用所有激活vip信息详情:"/api/pay/common/all-pay-vip-info"
查询用户优惠状态和折扣价格列表:"/api/pay/common/user-promotion-status"
查询用户vip信息详情for pay:"/api/pay/common/vip-info-for-pay"
closeConsumer:"/api/mq/close-consumer"
查询当前用户生成的历史图片:"/api/history/history-img-list"
根据code查询优惠券信息:"/api/pay/coupon/query-by-code"