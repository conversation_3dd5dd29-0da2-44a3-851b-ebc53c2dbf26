package com.ai.operation.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 用户导出生图入参记录对象 gpt_export_gen_info
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_export_gen_info", description = "用户导出生图入参记录")
@TableName("gpt_export_gen_info")
public class GptExportGenInfo extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户名称 */
    @ApiModelProperty("用户名称")
    @Excel(name = "用户名称")
    private String loginName;

    /** 用户邮箱 */
    @ApiModelProperty("用户邮箱")
    @Excel(name = "用户邮箱")
    private String email;

    /** 是否已经导出发给用户： 0 ：否   1 ：是 */
    @ApiModelProperty("是否已经导出发给用户： 0 ：否   1 ：是")
    @Excel(name = "是否已经导出发给用户： 0 ：否   1 ：是")
    private Boolean exportFlag;

}
