package com.ai.operation.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 用户图片公开审核对象 gpt_public_file_review
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_public_file_review", description = "用户图片公开审核")
@TableName("gpt_public_file_review")
public class GptPublicFileReview extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** $column.columnComment */
    @ApiModelProperty("${comment}")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String promptId;

    /** 文件名称 */
    @ApiModelProperty("文件名称")
    @Excel(name = "文件名称")
    private String fileName;

    /** 状态(review(审核中),pass(通过),rejection(拒绝)) */
    @ApiModelProperty("状态(review(审核中),pass(通过),rejection(拒绝))")
    @Excel(name = "状态(review(审核中),pass(通过),rejection(拒绝))")
    private String reviewStatus;

    /** 是否展示0否1是 */
    @ApiModelProperty("是否展示0否1是")
    @Excel(name = "是否展示0否1是")
    private Long isDisplay;

    /** 拒绝类型（Violence(暴力) ，Pornography（色情）， Racial discrimination(种族歧视) ， Copyright infringement (版权)， Other（其他）） */
    @ApiModelProperty("拒绝类型")
    @Excel(name = "拒绝类型", readConverterExp = "V=iolence(暴力),，=Pornography（色情")
    private String rejectionType;

    /** 拒绝内容描述 */
    @ApiModelProperty("拒绝内容描述")
    @Excel(name = "拒绝内容描述")
    private String rejectionContent;

    /**
     * 文件路径
     */
    @ApiModelProperty("文件路径")
    private String fileUrl;

    /**
     * 缩略图路径
     */
    @ApiModelProperty("缩略图路径")
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    @ApiModelProperty("高清缩略图路径")
    private String highThumbnailUrl;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /**
     * 30% 高清图
     */
    private String highMiniUrl;

    /**
     * 简要
     */
    @ApiModelProperty("简要")
    private String  brief;

    /** 0:未删除 1：已删除 */
    private Integer del;

    /**
     * 公开类型：everyone ： 所有人可见  myself ： 自己可见  fullLikes : 满足20点赞后可见
     */
    private String publicType;

    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 图片结果宽度
     */
    private int realWidth;

    /** 图片结果高度 */
    private int realHeight;
    /**
     * 图片id
     */
    private String fileId;

    /**
     * 正向提示词
     */
    private String prompt;



    /**
     * 生图信息
     */
    private String genInfo;

    /**
     * 模型名称
     */
    private String modelDisplay;

    /**
     * 是否活动提交
     */
    @TableField(exist = false)
    private Boolean activityPosts;

    /**
     * 通过活动
     */
    @TableField(exist = false)
    private Boolean passActivity = true;

}
