package com.ai.operation.domain;

import java.io.Serializable;

import com.ai.common.annotation.Excel;
import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;

/**
 * 逻辑参数对象 logic_params
 * 
 * <AUTHOR>
 * @date 2023-12-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("logic_params")
public class LogicParams extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** key */
    @Excel(name = "key")
    private String keyName;

    /** value */
    @Excel(name = "value")
    private String valueData;


    /** 描述 */
    @ApiModelProperty("描述")
    @Excel(name = "描述")
    private String mark;

}
