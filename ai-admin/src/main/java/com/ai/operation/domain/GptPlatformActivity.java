package com.ai.operation.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

import java.time.LocalDateTime;

/**
 * 平台公告通知对象 gpt_platform_activity
 * 
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_platform_activity", description = "平台公告通知")
@TableName("gpt_platform_activity")
public class GptPlatformActivity extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 标题 */
    @ApiModelProperty("标题")
    @Excel(name = "标题")
    private String title;

    /** 简介 */
    @ApiModelProperty("简介")
    @Excel(name = "简介")
    private String introduction;

    /** 详情 */
    @ApiModelProperty("详情")
    @Excel(name = "详情")
    private String  details;

    /** 跳转地址 */
    @ApiModelProperty("跳转地址")
    @Excel(name = "跳转地址")
    private String linkAddress;

    /** 接收用户 */
    @ApiModelProperty("接收用户")
    @Excel(name = "接收用户")
    private String userType;

    /** 是否发布 */
    @ApiModelProperty("是否发布")
    @Excel(name = "是否发布")
    private Boolean publish;

    @ApiModelProperty("平台")
    @Excel(name = "平台")
    private String platform;

    /** 发布时间 */
    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

}
