package com.ai.operation.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * ios_社区首页banner图配置对象 banner_img_ios
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "banner_img_ios", description = "ios_社区首页banner图配置")
@TableName("banner_img_ios")
public class BannerImgIos extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 图片链接地址 */
    @ApiModelProperty("图片链接地址")
    @Excel(name = "图片链接地址")
    private String imgUrl;

    /** 图片顺序 */
    @ApiModelProperty("图片顺序")
    @Excel(name = "图片顺序")
    private Integer sort;

    /** 跳转类型 */
    @ApiModelProperty("跳转类型")
    @Excel(name = "跳转类型")
    private String jumpType;

    /** 跳转id */
    @ApiModelProperty("跳转id")
    @Excel(name = "跳转id")
    private Long jumpId;

    /** 是否删除：0-未删除，1-已删除 */
    private Boolean isDeleted;

    /** 图片状态 */
    @ApiModelProperty("图片状态")
    @Excel(name = "图片状态")
    private Boolean status;

    /** 跳转名称 */
    @ApiModelProperty("跳转名称")
    @Excel(name = "跳转名称")
    @TableField(exist = false)
    private String title;

}
