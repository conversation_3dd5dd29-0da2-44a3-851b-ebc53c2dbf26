package com.ai.operation.domain.dto;


import com.ai.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "社区活动奖项获得作品参数")
public class ActivityPrizeSelectionDto {
    /** 社区图片id */
    @ApiModelProperty("社区图片id")
    private String fileId;

    @ApiModelProperty("获得奖项")
    private Integer prizeLevel;

    @ApiModelProperty("活动id")
    private Long activityId;

    /** 奖牌名称 */
    @ApiModelProperty("奖牌名称")
    private String levelName;

    /** 图片链接 */
    @ApiModelProperty("图片链接")
    private String icon;
}
