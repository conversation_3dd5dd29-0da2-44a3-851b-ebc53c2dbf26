package com.ai.operation.domain.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "log_record_address")
public class RecordAddress {

    @Id
    private String id;

    /**
     * 标记id
     */
    private String markId;

    /**
     * 地址id
     */
    private String addressId;

    private String promptId;

    /**
     * 是否属于fastHour机制内的任务
     */
    private Boolean fastHour;
}