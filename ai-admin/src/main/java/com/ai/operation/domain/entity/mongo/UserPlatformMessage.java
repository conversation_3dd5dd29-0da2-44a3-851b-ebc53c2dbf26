package com.ai.operation.domain.entity.mongo;

import com.ai.operation.domain.entity.AccountInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "user_platform_message")
public class UserPlatformMessage {

    @Id
    private String id;

    /**
     * 用户信息
     */
    private AccountInfo accountInfo;

    /**
     * 标题信息
     */
    private String title;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 详情
     */
    private String details;

    /**
     * 图片id
     */
    private String fileId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 已读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;

    /**
     * 目标用户是否已读
     */
    private Boolean read;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /**
     * 数据库的消息id(做关联用)
     */
    private String messageId;

    /**
     * 消息通知类型
     * {@link com.ai.common.enums.NoticeMessType}
     * 私信类型: 1 : 精选  2 : 普通私信  3.会员升级通知  4.活动获奖通知   5.会员即将到期 6.会员已到期 7.试订阅即将到期并扣费
     */
    private Integer messType;

    /**
     * 会员类型：basic-非会员，standard-普通会员，pro-高级会员
     */
    private String planLevel;

    /**
     * 会员生效时间
     */
    private Long vipEndTime;

    /**
     * 社区活动名称
     */
    private String commActivityTitle;

    /**
     * 社区活动奖项名称
     */
    private String commActivityLevelName;


    /**
     * 时间区间：year, month
     */
    private String priceInterval;

    /**
     * 消息所属平台
     */
    private List<String> platform;
}
