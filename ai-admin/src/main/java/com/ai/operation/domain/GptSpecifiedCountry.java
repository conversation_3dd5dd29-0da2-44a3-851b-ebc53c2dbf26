package com.ai.operation.domain;

import com.ai.common.annotation.Excel;
import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 特殊国家配置对象 gpt_specified_country
 * 
 * <AUTHOR>
 * @date 2024-10-24
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_specified_country", description = "特殊国家配置")
@TableName("gpt_specified_country")
public class GptSpecifiedCountry extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 国家id */
    private Long id;

    /** 国家名称（英文） */
    @ApiModelProperty("国家名称(英)")
    @Excel(name = "国家名称(英)")
    private String country;

    /** 国家名称（中文名称） */
    @ApiModelProperty("国家名称(中)")
    @Excel(name = "国家名称(中)")
    private String countryName;

    /** 人均GDP */
    @ApiModelProperty("人均GDP")
    @Excel(name = "人均GDP")
    private String gdp;

    /** 0:未删除 1：已删除 */
    @ApiModelProperty("0:未删除 1：已删除")
    private Boolean del;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    @Excel(name = "优先级")
    private Integer priority;
}
