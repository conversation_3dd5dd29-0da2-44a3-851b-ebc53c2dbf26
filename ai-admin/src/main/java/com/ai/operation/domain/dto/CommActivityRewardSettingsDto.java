package com.ai.operation.domain.dto;

import com.ai.operation.domain.dto.CommActivityPrizeSettingDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "活动奖励设置参数")
public class CommActivityRewardSettingsDto {

    /** 活动id */
    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动活动标题")
    private String activityTitle;

    @ApiModelProperty("奖项")
    private List<CommActivityPrizeSettingDto> commActivityPrizeSettingDtoList;

}
