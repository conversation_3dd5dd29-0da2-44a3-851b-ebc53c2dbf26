package com.ai.operation.domain.dto;

import com.ai.operation.domain.dto.CommActivityGiftContentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "社区活动奖项设置参数")
public class CommActivityPrizeSettingDto {

    /** 获得奖项 */
    @ApiModelProperty("获得奖项")
    private Long prizeLevel;


    /** 获奖人数 */
    @ApiModelProperty("获奖人数")
    private Integer winnersNum;

    @ApiModelProperty("赠送内容")
    private List<CommActivityGiftContentDto> commActivityGiftContentDtoList;
}
