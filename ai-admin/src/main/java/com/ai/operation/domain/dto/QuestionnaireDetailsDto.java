package com.ai.operation.domain.dto;

import com.ai.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(description = "调查问卷-问卷数据")
public class QuestionnaireDetailsDto {

    @ApiModelProperty("问卷 ID")
    private String questionnaireId;

    @ApiModelProperty("问卷标题")
    private String title;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty("问题")
    private List<QuestionDto> questionList;

}
