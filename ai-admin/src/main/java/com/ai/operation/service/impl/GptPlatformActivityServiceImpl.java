package com.ai.operation.service.impl;

import java.util.*;

import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.NoticeUserType;
import com.ai.common.enums.Platform;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.domain.GptSysUpdate;
import com.ai.operation.domain.vo.UserTypeCountVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.GptPlatformActivityMapper;
import com.ai.operation.domain.GptPlatformActivity;
import com.ai.operation.service.IGptPlatformActivityService;

/**
 * 平台公告通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class GptPlatformActivityServiceImpl extends ServiceImpl<GptPlatformActivityMapper, GptPlatformActivity> implements IGptPlatformActivityService
{
    @Autowired
    private GptPlatformActivityMapper gptPlatformActivityMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;


    /**
     * 查询平台公告通知
     * 
     * @param id 平台公告通知主键
     * @return 平台公告通知
     */
    @Override
    public GptPlatformActivity selectGptPlatformActivityById(Long id)
    {
        return gptPlatformActivityMapper.selectGptPlatformActivityById(id);
    }

    /**
     * 查询平台公告通知列表
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 平台公告通知
     */
    @Override
    public List<GptPlatformActivity> selectGptPlatformActivityList(GptPlatformActivity gptPlatformActivity)
    {
        return gptPlatformActivityMapper.selectGptPlatformActivityList(gptPlatformActivity);
    }

    /**
     * 新增平台公告通知
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 结果
     */
    @Override
    public int insertGptPlatformActivity(GptPlatformActivity gptPlatformActivity)
    {
        gptPlatformActivity.setCreateTime(LocalDateTime.now());
        gptPlatformActivity.setPublish(Boolean.FALSE);
        return gptPlatformActivityMapper.insert(gptPlatformActivity);
    }

    /**
     * 修改平台公告通知
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 结果
     */
    @Override
    public int updateGptPlatformActivity(GptPlatformActivity gptPlatformActivity)
    {
        gptPlatformActivity.setUpdateTime(LocalDateTime.now());
        return gptPlatformActivityMapper.updateGptPlatformActivity(gptPlatformActivity);
    }

    /**
     * 批量删除平台公告通知
     * 
     * @param ids 需要删除的平台公告通知主键
     * @return 结果
     */
    @Override
    public int deleteGptPlatformActivityByIds(Long[] ids)
    {
        return gptPlatformActivityMapper.deleteGptPlatformActivityByIds(ids);
    }

    /**
     * 删除平台公告通知信息
     * 
     * @param id 平台公告通知主键
     * @return 结果
     */
    @Override
    public int deleteGptPlatformActivityById(Long id)
    {
        return gptPlatformActivityMapper.deleteGptPlatformActivityById(id);
    }

    /**
     * 发布平台公告通知信息
     *
     * @param id 系统更新消息主键
     * @return 结果
     */
    @Override
    public Boolean toPublish(Long id){
        // 查询相应的公告通知
        GptPlatformActivity gptPlatformActivity = gptPlatformActivityMapper.selectById(id);

        // 更新状态为已发布
        gptPlatformActivity.setPublish(Boolean.TRUE);
        gptPlatformActivity.setPublishTime(LocalDateTime.now());
        gptPlatformActivity.setUpdateTime(LocalDateTime.now());
        gptPlatformActivityMapper.updateById(gptPlatformActivity);

        // 更新redis中的数据
        updatePlatformActivityCache(gptPlatformActivity);
        return Boolean.TRUE;
    }

    /**
     * 刷新已发布的数据到redis
     *
     * @return 结果
     */
    @Override
    public Boolean refreshPublish() {
        List<UserTypeCountVo> counts = gptPlatformActivityMapper.selectUserTypeCount();

        // 用map存储每个缓存key累加数量
        Map<String, Integer> cacheMap = new HashMap<>();

        for (UserTypeCountVo vo : counts) {
            String userType = vo.getUserType();
            String platforms = vo.getPlatform(); // 可能是 "web,ios" 形式
            int number = vo.getNumber();

            if (Platform.isAllPlatformSelected(platforms)) {
                // 全部平台，更新无平台区分缓存
                if (NoticeUserType.vip.getValue().equals(userType)) {
                    cacheMap.merge(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS, number, Integer::sum);
                }else if (NoticeUserType.not_vip.getValue().equals(userType)) {
                    cacheMap.merge(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS, number, Integer::sum);
                }
                else  if (NoticeUserType.all.getValue().equals(userType)) {
                    cacheMap.merge(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS, number, Integer::sum);
                    cacheMap.merge(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS, number, Integer::sum);
                }
            } else {
                // 不是全部平台，拆分平台
                Set<String> platformSet = Arrays.stream(platforms.split(","))
                        .map(String::trim)
                        .map(String::toLowerCase)
                        .collect(Collectors.toSet());

                for (String platform : platformSet) {
                    String platformKey = platform.toUpperCase();

                    if (NoticeUserType.vip.getValue().equals(userType)) {
                        cacheMap.merge(LogicParamsCons.getCacheKey(platformKey, true), number, Integer::sum);
                    }
                    if (NoticeUserType.not_vip.getValue().equals(userType)) {
                        cacheMap.merge(LogicParamsCons.getCacheKey(platformKey, false), number, Integer::sum);
                    }
                    if (NoticeUserType.all.getValue().equals(userType)) {
                        cacheMap.merge(LogicParamsCons.getCacheKey(platformKey, true), number, Integer::sum);
                        cacheMap.merge(LogicParamsCons.getCacheKey(platformKey, false), number, Integer::sum);
                    }
                }

            }
        }

        // 写入所有具体缓存
        for (Map.Entry<String, Integer> entry : cacheMap.entrySet()) {
            redisCachePiclumen.setCacheObject(entry.getKey(), entry.getValue());
        }

        return Boolean.TRUE;
    }


    public void updatePlatformActivityCache(GptPlatformActivity gptPlatformActivity) {

        if (Platform.isAllPlatformSelected(gptPlatformActivity.getPlatform())){
            // 全部平台都拥有 加无平台消息
            if (NoticeUserType.vip.getValue().equals(gptPlatformActivity.getUserType())) {
                // VIP 平台公告通知缓存 +1
                redisCachePiclumen.increment(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS, 1L);

            } else if (NoticeUserType.not_vip.getValue().equals(gptPlatformActivity.getUserType())) {
                // 非VIP 平台公告通知缓存 +1
                redisCachePiclumen.increment(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

            } else if (NoticeUserType.all.getValue().equals(gptPlatformActivity.getUserType())) {
                // 全部：VIP 和 非VIP 缓存都 +1
                redisCachePiclumen.increment(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                redisCachePiclumen.increment(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
            }
        }else {
            Set<String> platformSet = Arrays.stream(gptPlatformActivity.getPlatform().split(","))
                    .map(String::trim)
                    .map(String::toLowerCase)
                    .collect(Collectors.toSet());

            if (platformSet.contains(Platform.WEB.getPlatformName())) {
                // 加web 端消息
                if (NoticeUserType.vip.getValue().equals(gptPlatformActivity.getUserType())) {
                    // VIP 平台公告通知缓存 +1
                    redisCachePiclumen.increment(LogicParamsCons.WEB_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

                } else if (NoticeUserType.not_vip.getValue().equals(gptPlatformActivity.getUserType())) {
                    // 非VIP 平台公告通知缓存 +1
                    redisCachePiclumen.increment(LogicParamsCons.WEB_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

                } else if (NoticeUserType.all.getValue().equals(gptPlatformActivity.getUserType())) {
                    // 全部：VIP 和 非VIP 缓存都 +1
                    redisCachePiclumen.increment(LogicParamsCons.WEB_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                    redisCachePiclumen.increment(LogicParamsCons.WEB_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                }
            }

            if (platformSet.contains(Platform.IOS.getPlatformName())) {
                // 加ios 端消息
                if (NoticeUserType.vip.getValue().equals(gptPlatformActivity.getUserType())) {
                    // VIP 平台公告通知缓存 +1
                    redisCachePiclumen.increment(LogicParamsCons.IOS_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

                } else if (NoticeUserType.not_vip.getValue().equals(gptPlatformActivity.getUserType())) {
                    // 非VIP 平台公告通知缓存 +1
                    redisCachePiclumen.increment(LogicParamsCons.IOS_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

                } else if (NoticeUserType.all.getValue().equals(gptPlatformActivity.getUserType())) {
                    // 全部：VIP 和 非VIP 缓存都 +1
                    redisCachePiclumen.increment(LogicParamsCons.IOS_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                    redisCachePiclumen.increment(LogicParamsCons.IOS_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                }
            }

            if (platformSet.contains(Platform.ANDROID.getPlatformName())) {
                // 加android 端消息
                if (NoticeUserType.vip.getValue().equals(gptPlatformActivity.getUserType())) {
                    // VIP 平台公告通知缓存 +1
                    redisCachePiclumen.increment(LogicParamsCons.ANDROID_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

                } else if (NoticeUserType.not_vip.getValue().equals(gptPlatformActivity.getUserType())) {
                    // 非VIP 平台公告通知缓存 +1
                    redisCachePiclumen.increment(LogicParamsCons.ANDROID_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);

                } else if (NoticeUserType.all.getValue().equals(gptPlatformActivity.getUserType())) {
                    // 全部：VIP 和 非VIP 缓存都 +1
                    redisCachePiclumen.increment(LogicParamsCons.ANDROID_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                    redisCachePiclumen.increment(LogicParamsCons.ANDROID_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1L);
                }
            }

        }

    }
}
