package com.ai.operation.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.CommActivityPrizeSettings;

/**
 * 社区活动信息奖励配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface ICommActivityPrizeSettingsService extends IService<CommActivityPrizeSettings> {
    /**
     * 查询社区活动信息奖励配置
     * 
     * @param id 社区活动信息奖励配置主键
     * @return 社区活动信息奖励配置
     */
     CommActivityPrizeSettings selectCommActivityPrizeSettingsById(Long id);

    /**
     * 查询社区活动信息奖励配置列表
     * 
     * @param commActivityPrizeSettings 社区活动信息奖励配置
     * @return 社区活动信息奖励配置集合
     */
     List<CommActivityPrizeSettings> selectCommActivityPrizeSettingsList(CommActivityPrizeSettings commActivityPrizeSettings);

    /**
     * 新增社区活动信息奖励配置
     * 
     * @param commActivityPrizeSettings 社区活动信息奖励配置
     * @return 结果
     */
     int insertCommActivityPrizeSettings(CommActivityPrizeSettings commActivityPrizeSettings);

    /**
     * 修改社区活动信息奖励配置
     * 
     * @param commActivityPrizeSettings 社区活动信息奖励配置
     * @return 结果
     */
    int updateCommActivityPrizeSettings(CommActivityPrizeSettings commActivityPrizeSettings);

    /**
     * 批量删除社区活动信息奖励配置
     * 
     * @param ids 需要删除的社区活动信息奖励配置主键集合
     * @return 结果
     */
    int deleteCommActivityPrizeSettingsByIds(Long[] ids);

    /**
     * 删除社区活动信息奖励配置信息
     * 
     * @param id 社区活动信息奖励配置主键
     * @return 结果
     */
     int deleteCommActivityPrizeSettingsById(Long id);
}
