package com.ai.operation.service;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.operation.controller.resp.BannerJumpIdTreeResp;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.BannerImgIos;

/**
 * ios_社区首页banner图配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface IBannerImgIosService extends IService<BannerImgIos> {
    /**
     * 查询ios_社区首页banner图配置
     * 
     * @param id ios_社区首页banner图配置主键
     * @return ios_社区首页banner图配置
     */
    BannerImgIos selectBannerImgIosById(Long id);

    /**
     * 查询ios_社区首页banner图配置列表
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return ios_社区首页banner图配置集合
     */
    List<BannerImgIos> selectBannerImgIosList(BannerImgIos bannerImgIos);

    /**
     * 新增ios_社区首页banner图配置
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return 结果
     */
    AjaxResult insertBannerImgIos(BannerImgIos bannerImgIos, LoginUser loginUser);

    /**
     * 修改ios_社区首页banner图配置
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return 结果
     */
    AjaxResult updateBannerImgIos(BannerImgIos bannerImgIos,LoginUser loginUser);

    /**
     * 批量删除ios_社区首页banner图配置
     * 
     * @param ids 需要删除的ios_社区首页banner图配置主键集合
     * @return 结果
     */
     int deleteBannerImgIosByIds(Long[] ids,LoginUser loginUser);

    /**
     * 删除ios_社区首页banner图配置信息
     * 
     * @param id ios_社区首页banner图配置主键
     * @return 结果
     */
      int deleteBannerImgIosById(Long id);

     List<BannerJumpIdTreeResp> getBannerJumpIdTree();


}
