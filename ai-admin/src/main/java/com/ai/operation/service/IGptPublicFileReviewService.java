package com.ai.operation.service;

import java.time.LocalDate;
import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.page.TableDataInfo;
import com.ai.operation.domain.dto.RejectInBatchesDto;
import com.ai.operation.domain.entity.CommFile;
import com.ai.operation.domain.vo.GptPublicFileReviewPageVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.GptPublicFileReview;

/**
 * 用户图片公开审核Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface IGptPublicFileReviewService extends IService<GptPublicFileReview> {
    /**
     * 查询用户图片公开审核
     * 
     * @param id 用户图片公开审核主键
     * @return 用户图片公开审核
     */
    public GptPublicFileReview selectGptPublicFileReviewById(Long id);

    /**
     * 查询用户图片公开审核列表
     * 
     * @param gptPublicFileReview 用户图片公开审核
     * @return 用户图片公开审核集合
     */
    public List<GptPublicFileReviewPageVo> selectGptPublicFileReviewList(GptPublicFileReview gptPublicFileReview);

    /**
     * 新增用户图片公开审核
     * 
     * @param gptPublicFileReview 用户图片公开审核
     * @return 结果
     */
    public int insertGptPublicFileReview(GptPublicFileReview gptPublicFileReview);

    /**
     * 修改用户图片公开审核
     * 
     * @param gptPublicFileReview 用户图片公开审核
     * @return 结果
     */
    AjaxResult updateGptPublicFileReview(GptPublicFileReview gptPublicFileReview);

    /**
     * 批量删除用户图片公开审核
     * 
     * @param ids 需要删除的用户图片公开审核主键集合
     * @return 结果
     */
    public int deleteGptPublicFileReviewByIds(Long[] ids);

    /**
     * 删除用户图片公开审核信息
     * 
     * @param id 用户图片公开审核主键
     * @return 结果
     */
    public int deleteGptPublicFileReviewById(Long id);


    /**
     * 批量通过
     *
     * @param ids 用户图片公开审核主键集合
     * @return 结果
     */
    AjaxResult  passInBatches(Long[] ids,Boolean featured,Boolean activity);

    /**
     * 批量拒绝
     *
     * @param rejectInBatchesDto 批量拒绝传入参数
     * @return 结果
     */
    AjaxResult  rejectInBatches(RejectInBatchesDto rejectInBatchesDto);

    TableDataInfo getRecentThreeDaysData(Integer page, Integer pageSize, String markFileId, Boolean isNext, LocalDate recordDate,Boolean featured,String loginName,String userName,String publicType,String id, Long activityId);


    /**
     * 设置图片为精选
     *
     * @param ids 用户图片公开审核主键集合
     * @return 结果
     */
    AjaxResult setFeatured(String[] ids);


    /**
     * 取消图片为精选
     *
     * @param ids 用户图片公开审核主键集合
     * @return 结果
     */
    AjaxResult cancelFeatured(String[] ids);

    /**
     * 删除社区图片
     *
     * @param ids 用户图片id 集合
     * @return 结果
     */
    AjaxResult deleteImgs(String[] ids);

    /**
     * 取消活动图片
     *
     * @param ids 用户图片id 集合
     * @return 结果
     */
    AjaxResult cancelActivityImgs(String[] ids);


}
