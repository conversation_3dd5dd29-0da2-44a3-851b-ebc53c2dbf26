package com.ai.operation.service.impl;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import com.ai.admin.service.InSiteMessageService;
import com.ai.common.constant.HttpStatus;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.*;
import com.ai.common.utils.DateUtils;
import com.ai.constants.LogicParamsCons;
import com.ai.enums.LumenChangeSourceEnum;
import com.ai.enums.LumenChangeTypeEnum;
import com.ai.operation.controller.req.ActivityRewardSelectionReq;
import com.ai.operation.controller.resp.*;
import com.ai.operation.domain.*;
import com.ai.operation.domain.dto.*;
import com.ai.operation.domain.entity.AccountInfo;
import com.ai.operation.domain.entity.CommFile;
import com.ai.operation.domain.entity.mongo.UserPlatformMessage;
import com.ai.operation.mapper.CommActivityPrizeSettingsMapper;
import com.ai.operation.service.ICommActivityRewardSelectionService;
import com.ai.operation.service.ICommActivityRewardSettingsService;
import com.ai.operation.service.LumenChangeRecordService;
import com.ai.orders.domain.PayLumenRecord;
import com.ai.orders.domain.SubscriptionCurrent;
import com.ai.orders.service.IPayLumenRecordService;
import com.ai.orders.service.ISubscriptionCurrentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.CommActivityMapper;
import com.ai.operation.service.ICommActivityService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 社区活动信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
@Slf4j
public class CommActivityServiceImpl extends ServiceImpl<CommActivityMapper, CommActivity> implements ICommActivityService
{
    @Autowired
    private CommActivityMapper commActivityMapper;

    @Autowired
    private ICommActivityRewardSettingsService commActivityRewardSettingsService;

    @Autowired
    private ICommActivityRewardSelectionService commActivityRewardSelectionService;
    
    @Autowired
    private CommActivityPrizeSettingsMapper commActivityPrizeSettingsMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private IPayLumenRecordService payLumenRecordService;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;

    @Autowired
    private ISubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    private InSiteMessageService inSiteMessageService;

    @Autowired
    private LumenChangeRecordService lumenChangeRecordService;

    /**
     * 查询社区活动信息
     * 
     * @param id 社区活动信息主键
     * @return 社区活动信息
     */
    @Override
    public CommActivity selectCommActivityById(Long id)
    {
        return commActivityMapper.selectCommActivityById(id);
    }

    /**
     * 查询社区活动信息列表
     * 
     * @param commActivity 社区活动信息
     * @return 社区活动信息
     */
    @Override
    public List<CommActivity> selectCommActivityList(CommActivity commActivity) {
        List<CommActivity> list = commActivityMapper.selectCommActivityList(commActivity);

        LocalDateTime now = LocalDateTime.now();

        for (CommActivity activity : list) {
            if (Boolean.FALSE.equals(activity.getPublish())) {
                // 未发布
                activity.setStatus(CommActivityStatus.init.getValue());
            } else if (now.isBefore(activity.getPostEndTime())) {
                // 已发布，投稿中
                activity.setStatus(CommActivityStatus.conduct.getValue());
            } else if (now.isBefore(activity.getEndTime())) {
                // 投稿结束但活动未结束，评奖中
                activity.setStatus(CommActivityStatus.awards.getValue());
            } else {
                // 活动结束
                activity.setStatus(CommActivityStatus.end.getValue());
            }
        }

        return list;
    }

    /**
     * 新增社区活动信息
     * 
     * @param commActivity 社区活动信息
     * @return 结果
     */
    @Override
    public int insertCommActivity(CommActivity commActivity)
    {
        commActivity.setCreateTime(LocalDateTime.now());
        commActivity.setEndTime(DateUtils.getMaxDateTime());
        return commActivityMapper.insert(commActivity);
    }

    /**
     * 修改社区活动信息
     * 
     * @param commActivity 社区活动信息
     * @return 结果
     */
    @Override
    public int updateCommActivity(CommActivity commActivity)
    {
        commActivity.setUpdateTime(LocalDateTime.now());
        return commActivityMapper.updateCommActivity(commActivity);
    }

    /**
     * 批量删除社区活动信息
     * 
     * @param ids 需要删除的社区活动信息主键
     * @return 结果
     */
    @Override
    public int deleteCommActivityByIds(Long[] ids)
    {
        return commActivityMapper.deleteCommActivityByIds(ids);
    }

    /**
     * 删除社区活动信息信息
     * 
     * @param id 社区活动信息主键
     * @return 结果
     */
    @Override
    public int deleteCommActivityById(Long id)
    {
        return commActivityMapper.deleteCommActivityById(id);
    }

    /**
     * 社区活动设置奖励
     *
     * @param commActivityRewardSettingsReq 活动奖励设置传入参数
     * @return 结果
     */
    @Override
    @Transactional
    public Boolean activityRewardSettings(CommActivityRewardSettingsDto commActivityRewardSettingsReq) {

        //  删除之前的奖励设置
        LambdaQueryWrapper<CommActivityRewardSettings> arw = new LambdaQueryWrapper<>();
        arw.eq(CommActivityRewardSettings::getActivityId, commActivityRewardSettingsReq.getActivityId());
        commActivityRewardSettingsService.remove(arw);

        List<CommActivityRewardSettings> commActivityRewardSettingsList = new ArrayList<>();
        for (CommActivityPrizeSettingDto activityPrizeSettingReq : commActivityRewardSettingsReq.getCommActivityPrizeSettingDtoList()) {
            for (CommActivityGiftContentDto commActivityGiftContentDto : activityPrizeSettingReq.getCommActivityGiftContentDtoList()) {
                CommActivityRewardSettings commActivityRewardSettings = new CommActivityRewardSettings();
                BeanUtils.copyProperties(commActivityGiftContentDto, commActivityRewardSettings);
                commActivityRewardSettings.setActivityId(commActivityRewardSettingsReq.getActivityId());
                commActivityRewardSettings.setPrizeLevel(activityPrizeSettingReq.getPrizeLevel());
                commActivityRewardSettings.setWinnersNum(activityPrizeSettingReq.getWinnersNum());
                commActivityRewardSettings.setCreateTime(LocalDateTime.now());
                commActivityRewardSettingsList.add(commActivityRewardSettings);
            }
        }

        return commActivityRewardSettingsService.saveBatch(commActivityRewardSettingsList);

    }

    @Override
    public CommActivityRewardSettingsDto getRewardSettingsByActivityId(Long activityId) {
        // 查询该活动下所有奖励记录
        LambdaQueryWrapper<CommActivityRewardSettings> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommActivityRewardSettings::getActivityId, activityId);
        List<CommActivityRewardSettings> settingsList = commActivityRewardSettingsService.list(queryWrapper);

        CommActivity commActivity = baseMapper.selectById(activityId);



        // 分组：按 prizeLevel 进行分组
        Map<Long, List<CommActivityRewardSettings>> groupedMap =
                settingsList.stream().collect(Collectors.groupingBy(CommActivityRewardSettings::getPrizeLevel));

        // 构建 CommActivityRewardSettingsReq 对象
        CommActivityRewardSettingsDto result = new CommActivityRewardSettingsDto();
        result.setActivityId(activityId);
        result.setActivityTitle(commActivity.getTitle());
        if (CollectionUtils.isEmpty(settingsList)) {
            result.setCommActivityPrizeSettingDtoList(new ArrayList<>());
            return result; // 或者 return new CommActivityRewardSettingsReq(); 根据需求
        }


        List<CommActivityPrizeSettingDto> prizeSettingReqList = new ArrayList<>();

        for (Map.Entry<Long, List<CommActivityRewardSettings>> entry : groupedMap.entrySet()) {
            Long prizeLevel = entry.getKey();
            List<CommActivityRewardSettings> groupList = entry.getValue();

            CommActivityPrizeSettingDto prizeSettingReq = new CommActivityPrizeSettingDto();
            prizeSettingReq.setPrizeLevel(prizeLevel);
            prizeSettingReq.setWinnersNum(groupList.get(0).getWinnersNum()); // 同一等级 winnersNum 相同

            // 奖品内容
            List<CommActivityGiftContentDto> giftContentReqList = groupList.stream().map(item -> {
                CommActivityGiftContentDto giftReq = new CommActivityGiftContentDto();
                BeanUtils.copyProperties(item, giftReq);
                return giftReq;
            }).collect(Collectors.toList());

            prizeSettingReq.setCommActivityGiftContentDtoList(giftContentReqList);
            prizeSettingReqList.add(prizeSettingReq);
        }

        result.setCommActivityPrizeSettingDtoList(prizeSettingReqList);
        return result;
    }

    @Override
    public List<ActivityPrizeTreeResp> getActivityPrizeTree() {
        LambdaQueryWrapper<CommActivityPrizeSettings> qpw = new LambdaQueryWrapper<>();
        qpw.orderByAsc(CommActivityPrizeSettings::getLevel);
        List<CommActivityPrizeSettings> commActivityPrizeSettings = commActivityPrizeSettingsMapper.selectList(qpw);
        List<ActivityPrizeTreeResp> activityPrizeTreeRespList = commActivityPrizeSettings.stream().map(item -> {
            ActivityPrizeTreeResp activityPrizeTreeResp = new ActivityPrizeTreeResp();
            activityPrizeTreeResp.setLabel(item.getLevelName());
            activityPrizeTreeResp.setValue(item.getLevel());
            activityPrizeTreeResp.setIcon(item.getIcon());
            return activityPrizeTreeResp;
        }).collect(Collectors.toList());
        return activityPrizeTreeRespList;
    }


    @Override
    public TableDataInfo getActivityPostsImgList( Integer pageSize,
                                                String markFileId, Boolean isNext,
                                                Long activityId,Integer lastFileLikeNums ) {
        // 校验页码和每页记录数的有效性
        if (pageSize == null || pageSize < 1) {
            throw new IllegalArgumentException("Page and pageSize must be greater than 0");
        }

        // 创建分页查询
        Query query = new Query();
        query.addCriteria(Criteria.where("activityId").is(activityId));

        if (StringUtils.isNotBlank(markFileId)) {
            if (isNext) {
//                query.addCriteria(Criteria.where("fileLikeNums").lt(lastFileLikeNums));
//                query.addCriteria(Criteria.where("id").lt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.DESC, "fileLikeNums"));
                query.with(Sort.by(Sort.Direction.DESC, "id"));
                // 添加复合边界条件
                query.addCriteria(new Criteria().orOperator(
                        // 条件 1：点赞数小于上一页最后一条记录的点赞数
                        Criteria.where("fileLikeNums").lt(lastFileLikeNums),
                        // 条件 2：点赞数相同，但 ID 小于上一页最后记录的 ID
                        new Criteria().andOperator(
                                Criteria.where("fileLikeNums").is(lastFileLikeNums),
                                Criteria.where("id").lt(new ObjectId(markFileId))
                        )
                ));
            } else {
//                query.addCriteria(Criteria.where("fileLikeNums").gt(lastFileLikeNums));
//                query.addCriteria(Criteria.where("id").gt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.ASC, "fileLikeNums"));
                query.with(Sort.by(Sort.Direction.ASC, "id"));
                // 添加复合边界条件
                query.addCriteria(new Criteria().orOperator(
                        // 条件 1：点赞数小于上一页最后一条记录的点赞数
                        Criteria.where("fileLikeNums").gt(lastFileLikeNums),
                        // 条件 2：点赞数相同，但 ID 小于上一页最后记录的 ID
                        new Criteria().andOperator(
                                Criteria.where("fileLikeNums").is(lastFileLikeNums),
                                Criteria.where("id").gt(new ObjectId(markFileId))
                        )
                ));
            }
        } else {
            query.with(Sort.by(Sort.Direction.DESC, "fileLikeNums"));
            query.with(Sort.by(Sort.Direction.DESC, "id"));
        }


        query.limit(pageSize);

        // 查询符合条件的数据
        List<CommFile> commFiles = mongoTemplate.find(query, CommFile.class);
        if (StringUtils.isNotBlank(markFileId) && !isNext) {
            Collections.reverse(commFiles);
        }
        List<ActivityPrizeSelectionDto> activityPrizeSelection = commActivityPrizeSettingsMapper.getActivityPrizeSelection(activityId);
        Map<String,ActivityPrizeSelectionDto> activityPrizeSelectionMap = activityPrizeSelection.stream().collect(Collectors.toMap(ActivityPrizeSelectionDto::getFileId, Function.identity()));
        List<CommSelectionFileResp> commSelectionFileRespList = new ArrayList<>();
        for (CommFile commFile :commFiles) {
            CommSelectionFileResp commSelectionFileResp = new CommSelectionFileResp();
            BeanUtils.copyProperties(commFile, commSelectionFileResp);
            commSelectionFileResp.setPrizeLevel(null);
            commSelectionFileResp.setFileId(commFile.getId());
            commSelectionFileResp.setMiniThumbnailUrl(StringUtils.isNotBlank(commFile.getMiniThumbnailUrl()) ? commFile.getMiniThumbnailUrl() :  commFile.getThumbnailUrl());
            commSelectionFileResp.setUserName(commFile.getAccountInfo().getUserName());
            commSelectionFileResp.setLoginName(commFile.getAccountInfo().getUserLoginName());
            commSelectionFileResp.setUserId(commFile.getAccountInfo().getUserId());
            ActivityPrizeSelectionDto activityPrizeSelectionDto = activityPrizeSelectionMap.get(commFile.getId());
            if (!Objects.isNull(activityPrizeSelectionDto)) {
                commSelectionFileResp.setPrizeLevel(activityPrizeSelectionDto.getPrizeLevel());
                commSelectionFileResp.setLevelName(activityPrizeSelectionDto.getLevelName());
                commSelectionFileResp.setIcon(activityPrizeSelectionDto.getIcon());
            }
            commSelectionFileRespList.add(commSelectionFileResp);
        }



        // 封装响应数据
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(commSelectionFileRespList);
        rspData.setTotal(commSelectionFileRespList.size());
        return rspData;
    }

    @Override
    public List<ActivityPrizeLevelResp> getActivityPrizeList(Long activityId) {
        List<ActivityPrizeLevelResp> activityPrizeList = commActivityPrizeSettingsMapper.getActivityPrizeList(activityId);
        return activityPrizeList;
    }

    @Override
    public AjaxResult setActivityPrizeLevel(ActivityRewardSelectionReq activityRewardSelectionReq) {

        Long activityId = activityRewardSelectionReq.getActivityId();

        // 查询该活动下该奖项的设置
        LambdaQueryWrapper<CommActivityRewardSettings> cpw = new LambdaQueryWrapper<>();
        cpw.eq(CommActivityRewardSettings::getActivityId, activityId);
        cpw.eq(CommActivityRewardSettings::getPrizeLevel, activityRewardSelectionReq.getPrizeLevel());
        cpw.last("limit 1");
        CommActivityRewardSettings rewardSettings = commActivityRewardSettingsService.getOne(cpw);

        if (rewardSettings == null) {
            return AjaxResult.error("此活动未设置该奖项的奖励");
        }

        Integer winnersNum = rewardSettings.getWinnersNum();

        // 获取当前该奖项已评选的记录数
        LambdaQueryWrapper<CommActivityRewardSelection> arw = new LambdaQueryWrapper<>();
        arw.eq(CommActivityRewardSelection::getActivityId, activityId);
        arw.eq(CommActivityRewardSelection::getPrizeLevel, activityRewardSelectionReq.getPrizeLevel());
        List<CommActivityRewardSelection> existingSelections = commActivityRewardSelectionService.list(arw);

        Integer alreadySelected = existingSelections.size();
        Integer remainingQuota = winnersNum - alreadySelected;

        // 获取已存在的作品的 fileId 列表
        List<String> existingFileIds = existingSelections.stream()
                .map(CommActivityRewardSelection::getFileId)
                .collect(Collectors.toList());

        // 过滤掉已经在数据库中存在的作品（根据 fileId 判断）
        List<ActivityRewardSelectionDto> filteredList = activityRewardSelectionReq.getActivityRewardSelectionDtoList().stream()
                .filter(dto -> !existingFileIds.contains(dto.getFileId())) // 排除已经评选过的作品
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)){
            return AjaxResult.error("所选作品已设置该奖项");
        }

        // 获取去重并过滤后的数量
        Integer filteredSize = filteredList.size();

        // 如果去重并过滤后的数量超出剩余的评选名额，提示用户重新选择
        if (filteredSize > remainingQuota) {
            return AjaxResult.error("所选作品数超过剩余可评选数量（剩余 " + remainingQuota + " 个名额），请重新选择");
        }

        // 转换为实体列表准备保存
        List<CommActivityRewardSelection> insertList = new ArrayList<>();
        for (ActivityRewardSelectionDto dto : filteredList) {
            CommActivityRewardSelection selection = new CommActivityRewardSelection();
            BeanUtils.copyProperties(dto, selection);
            selection.setActivityId(activityId);
            selection.setPrizeLevel(activityRewardSelectionReq.getPrizeLevel());
            selection.setCreateTime(LocalDateTime.now());
            insertList.add(selection);
        }

        // 批量保存
        commActivityRewardSelectionService.saveBatch(insertList);
        return AjaxResult.success("成功评选 " + insertList.size() + " 个作品");
    }

    @Override
    public Boolean toPublic(Long id){

        // 公开发布此活动
        LambdaUpdateWrapper<CommActivity> ucw = new LambdaUpdateWrapper<>();
        ucw.eq(CommActivity::getId, id);
        ucw.set(CommActivity::getPublish,Boolean.TRUE);
        ucw.set(CommActivity::getUpdateTime, LocalDateTime.now());
        Boolean result =  commActivityMapper.update(null, ucw) > 0;

        if (result){
            // 添加活动
            redisCachePiclumen.addOBjectDataToSet(LogicParamsCons.COMM_ACTIVITY_PUBLIC, id);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sendActivityPrize(Long id) {
        CommActivity commActivity = baseMapper.selectById(id);
        LocalDateTime now = LocalDateTime.now();
        Boolean inAwardPeriod = (now.isAfter(commActivity.getPostEndTime()) || now.isEqual(commActivity.getPostEndTime()))
                && now.isBefore(commActivity.getEndTime());
        if (!inAwardPeriod) {
            return AjaxResult.error("只能对评奖中 活动发送奖励");
        }

        // 查询评选与奖励设置
        List<CommActivityRewardSettings> settings = commActivityRewardSettingsService.list(
                new LambdaQueryWrapper<CommActivityRewardSettings>().eq(CommActivityRewardSettings::getActivityId, id));

        if (CollectionUtils.isEmpty(settings)) {
            return AjaxResult.error("此活动没设置奖励");
        }

        List<SendRewardSelectionDto> selections = commActivityRewardSelectionService.getRewardSelectionUser(id);
        if (CollectionUtils.isEmpty(selections)) {
            return AjaxResult.error("此活动没评选作品 或已经发放奖励");
        }


        Map<Long, List<CommActivityRewardSettings>> settingsMap = settings.stream()
                .collect(Collectors.groupingBy(CommActivityRewardSettings::getPrizeLevel));
        Map<Integer, CommActivityPrizeSettings> integerCommActivityPrizeSettingsMap = commActivityPrizeSettingsMapper.selectLevelNameMap();
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, CommFile.class);
        List<PayLumenRecord> lumenRecords = new ArrayList<>();
        List<SubscriptionCurrent> vipRecords = new ArrayList<>();
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        List<Long> userIdList = new ArrayList<>();
        List<UserPlatformMessage> userPlatformMessageList = new ArrayList<>();
        List<LumenChangeRecord>  lumenChangeRecords = new ArrayList<>();
        for (SendRewardSelectionDto selection : selections) {
            // 更新 MongoDB 奖项等级
            operations.updateOne(
                    Query.query(Criteria.where("id").is(selection.getFileId())),
                    new Update().set("prizeLevel", selection.getPrizeLevel())
            );

            UserPlatformMessage userPlatformMessage = new UserPlatformMessage();
            String loginName = selection.getLoginName();

            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setUserId(selection.getUserId());
            accountInfo.setUserLoginName(loginName);
            accountInfo.setUserName(selection.getUserName());
            accountInfo.setUserAvatarUrl(selection.getAvatarUrl());
            userPlatformMessage.setAccountInfo(accountInfo);

            CommActivityPrizeSettings commActivityPrizeSettings = integerCommActivityPrizeSettingsMap.get(selection.getPrizeLevel());
            String title = String.format("Congratulations! You've won %s.", commActivity.getTitle());
            String introduction = String.format("You did it! Your prize for %s is on the way!", commActivity.getTitle());
            userPlatformMessage.setTitle(title);
            userPlatformMessage.setIntroduction(introduction);
            userPlatformMessage.setFileId(selection.getFileId());
            userPlatformMessage.setMessType(NoticeMessType.winning.getValue());
            userPlatformMessage.setCommActivityTitle(commActivity.getTitle());
            userPlatformMessage.setMiniThumbnailUrl(selection.getMiniThumbnailUrl());
            userPlatformMessage.setCommActivityLevelName(commActivityPrizeSettings.getLevelName());
            userPlatformMessage.setCreateTime(LocalDateTime.now());
            userPlatformMessage.setRead(Boolean.FALSE);
            userPlatformMessage.setPlatform(NoticeMessType.winning.getPlatformInfo());
            userPlatformMessageList.add(userPlatformMessage);

            inSiteMessageService.dealRedisCache(NoticeMessType.winning, loginName);

            List<CommActivityRewardSettings> rewardSettings = settingsMap.get(selection.getPrizeLevel());
            if (CollectionUtils.isEmpty(rewardSettings)) continue;

            for (CommActivityRewardSettings rewardSetting : rewardSettings) {
                if (CommActivityRewardSettingsType.lumens.getValue().equals(rewardSetting.getType())) {
                    processLumenReward(selection, rewardSetting, lumenRecords, currentTimestamp);
                    Long happenTime = null;
                    LocalDateTime startTime = rewardSetting.getStartTime();
                    if (now.isAfter(startTime)) {
                        happenTime = DateUtils.getTimestamp(now);
                    } else {
                        happenTime = DateUtils.getTimestamp(startTime);
                    }
                     LumenChangeRecord lumenChangeRecord = lumenChangeRecordService.buildLumenChangeRecord(selection.getUserId(), selection.getLoginName(), Math.toIntExact(rewardSetting.getRewardNum()),
                            LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.CHALLENGE.getValue(), commActivity.getTitle(), null, rewardSetting.getId(), happenTime);
                    lumenChangeRecords.add(lumenChangeRecord);
                } else if (CommActivityRewardSettingsType.vip.getValue().equals(rewardSetting.getType())) {
                    processVipReward(selection, rewardSetting, vipRecords, currentTimestamp,userIdList);
                }
            }
        }

        // 批量保存
        if (!CollectionUtils.isEmpty(vipRecords)) {
            subscriptionCurrentService.saveBatch(vipRecords);
        }
        if (!CollectionUtils.isEmpty(lumenRecords)) {
            payLumenRecordService.saveBatch(lumenRecords);
        }
        if (!CollectionUtils.isEmpty(userIdList)){
            subscriptionCurrentService.updateUserVipStatusBatch(userIdList);
        }
        if (!CollectionUtils.isEmpty(userPlatformMessageList)){
            mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, UserPlatformMessage.class).insert(userPlatformMessageList).execute();
        }
        if (!CollectionUtils.isEmpty(lumenChangeRecords)){
            lumenChangeRecordService.batchInsertLumenChangeRecord(lumenChangeRecords);
        }

        operations.execute();

        // 更新活动状态
        commActivityMapper.update(null, new LambdaUpdateWrapper<CommActivity>()
                .eq(CommActivity::getId, id)
                .set(CommActivity::getEndTime, LocalDateTime.now())
                .set(CommActivity::getUpdateTime, LocalDateTime.now())
                .set(CommActivity::getReward,Boolean.TRUE)
        );

        // 更新评选表发送状态
        commActivityRewardSelectionService.update(null, new LambdaUpdateWrapper<CommActivityRewardSelection>()
                .eq(CommActivityRewardSelection::getActivityId, id)
                .set(CommActivityRewardSelection::getPublish, Boolean.TRUE)
                .set(CommActivityRewardSelection::getUpdateTime, LocalDateTime.now())
        );

        return AjaxResult.success();
    }

    private void processLumenReward(SendRewardSelectionDto selection, CommActivityRewardSettings setting,
                                    List<PayLumenRecord> records, Long now) {
        PayLumenRecord record = new PayLumenRecord();
        record.setUserId(selection.getUserId());
        record.setLoginName(selection.getLoginName());
        record.setVipPlatForm(VipPlatform.GIFT.getPlatformName());
        record.setCurrentPeriodStart(DateUtils.getTimestamp(setting.getStartTime()));
        record.setCurrentPeriodEnd(DateUtils.getTimestamp(setting.getEndTime()));
        record.setLogicPeriodStart(record.getCurrentPeriodStart());
        record.setLogicPeriodEnd(record.getCurrentPeriodEnd());
        record.setType(LumenType.gift.getValue());
        record.setLumenQty(setting.getRewardNum());
        record.setLumenLeftQty(setting.getRewardNum());
        record.setCreateTime(LocalDateTime.now());

        records.add(record);

        if (record.getLogicPeriodStart() < now && now < record.getLogicPeriodEnd()) {
            log.info("在当前订阅期限内，刷新 redis loginName: {}", record.getLoginName());
            resettingPersonalLumens(record.getLoginName());
        }
    }

    private void processVipReward(SendRewardSelectionDto selection, CommActivityRewardSettings setting,
                                  List<SubscriptionCurrent> records, Long now,List<Long> userIdList) {
        SubscriptionCurrent sc = new SubscriptionCurrent();
        sc.setUserId(selection.getUserId());
        sc.setLoginName(selection.getLoginName());
        sc.setPlanLevel(setting.getPlanLevel());
        sc.setPriceInterval(setting.getPriceInterval());
        sc.setCreateTime(LocalDateTime.now());

        String platform = VipPlatform.GIFT.getPlatformName();
        sc.setSubscriptionId(platform + "_" + com.ai.common.utils.StringUtils.generateRandomString(22));
        sc.setVipPlatform(platform);
        sc.setVipBeginTime(DateUtils.getTimestamp(setting.getStartTime()));
        sc.setVipEndTime(DateUtils.getTimestamp(setting.getEndTime()));
        sc.setAutoRenewStatus(0);
        sc.setInvalid(0);

        records.add(sc);

        if (sc.getVipBeginTime() < now && now < sc.getVipEndTime()) {
            userIdList.add(sc.getUserId());
//            subscriptionCurrentService.updateUserVipStatus(sc.getUserId());
        }
    }

    public void resettingPersonalLumens(String loginName) {
        if (com.alibaba.excel.util.StringUtils.isBlank(loginName)) {
            return;
        }

        log.info("开始重置用户:{} 有效的点数和使用的点数", loginName);
        try {
            //删除用户有效的点数
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_RECHARGE_TOTAL_LUMENS, loginName);
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_RECHARGE_USE_LUMENS, loginName);
            //删除用户使用的点数
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_VIP_TOTAL_LUMENS, loginName);
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_VIP_USE_LUMENS, loginName);
            log.info("结束重置用户:{} 有效的点数和使用的点数", loginName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Boolean cancelActivityPrizeLevel(List<String> fileIds){

        LambdaQueryWrapper<CommActivityRewardSelection> arw = new LambdaQueryWrapper<>();
        arw.in(CommActivityRewardSelection::getFileId,fileIds);
        commActivityRewardSelectionService.remove(arw);

        return true;
    }

    @Override
    public List<ActivityRewardListResp> getActivityRewardList(Long activityId) {

        List<ActivityRewardListResp> list = new ArrayList<>();

        // 查询评选设置表
        LambdaQueryWrapper<CommActivityRewardSelection> arw = new LambdaQueryWrapper<>();
        arw.eq(CommActivityRewardSelection::getActivityId,activityId);
        List<CommActivityRewardSelection> commActivityRewardSelections = commActivityRewardSelectionService.list(arw);

        // 查询奖励设置表
        LambdaQueryWrapper<CommActivityRewardSettings> ars = new LambdaQueryWrapper<>();
        ars.eq(CommActivityRewardSettings::getActivityId,activityId);
        List<CommActivityRewardSettings> commActivityRewardSettings = commActivityRewardSettingsService.list(ars);
        Map<Long,List<CommActivityRewardSettings>> map = commActivityRewardSettings.stream().collect(Collectors.groupingBy(CommActivityRewardSettings::getPrizeLevel));
        List<Long>  prizeLevels = commActivityRewardSettings.stream().map(CommActivityRewardSettings::getPrizeLevel).collect(Collectors.toList());

        // 查询奖项表
        LambdaQueryWrapper<CommActivityPrizeSettings> ap = new LambdaQueryWrapper<>();
        ap.in(CommActivityPrizeSettings::getLevel,prizeLevels);
        List<CommActivityPrizeSettings> commActivityPrizeSettings = commActivityPrizeSettingsMapper.selectList(ap);

        Map<Long,CommActivityPrizeSettings> prizeMap = commActivityPrizeSettings.stream().collect(Collectors.toMap(CommActivityPrizeSettings::getLevel,CommActivityPrizeSettings -> CommActivityPrizeSettings));

        for (CommActivityRewardSelection commActivityRewardSelection : commActivityRewardSelections) {
            ActivityRewardListResp activityRewardListResp = new ActivityRewardListResp();
            BeanUtils.copyProperties(commActivityRewardSelection, activityRewardListResp);
            CommActivityPrizeSettings prize = prizeMap.get(commActivityRewardSelection.getPrizeLevel());
            if(!Objects.isNull(prize)){
                activityRewardListResp.setLevelName(prize.getLevelName());
            }
            List<CommActivityRewardSettings>  rewardSettings= map.get(commActivityRewardSelection.getPrizeLevel());
            if (!CollectionUtils.isEmpty(rewardSettings)){
                List<CommActivityGiftContentDto>  commActivityGiftContentDtoList = new ArrayList<>();
                for(CommActivityRewardSettings activityRewardSettings:rewardSettings){
                    CommActivityGiftContentDto giftContentDto = new CommActivityGiftContentDto();
                    BeanUtils.copyProperties(activityRewardSettings, giftContentDto);
                    commActivityGiftContentDtoList.add(giftContentDto);
                }
                activityRewardListResp.setCommActivityGiftContentDtoList(commActivityGiftContentDtoList);
            }
            list.add(activityRewardListResp);
        }
        return list;
    }

    @Override
    public List<CommActivityPostingResp> getCommActivityPostingList() {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<CommActivity> qw = new LambdaQueryWrapper<>();
        qw.eq(CommActivity::getPublish, true);
        qw.le(CommActivity::getBeginTime, now);
        qw.select(CommActivity::getId, CommActivity::getTitle);

        List<CommActivity> commActivities = commActivityMapper.selectList(qw);

        List<CommActivityPostingResp> activityPostingRespList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(commActivities)) {
            for (CommActivity activity : commActivities) {
                CommActivityPostingResp activityResult = new CommActivityPostingResp();
                BeanUtils.copyProperties(activity, activityResult);
                activityPostingRespList.add(activityResult);
            }
        }

        return activityPostingRespList;
    }

}
