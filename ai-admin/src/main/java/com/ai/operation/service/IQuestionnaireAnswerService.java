package com.ai.operation.service;

import java.util.List;

import com.ai.operation.domain.vo.QuestionAnswerVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.QuestionnaireAnswer;

/**
 * 用户问卷调查回答Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IQuestionnaireAnswerService extends IService<QuestionnaireAnswer> {
    /**
     * 查询用户问卷调查回答
     * 
     * @param id 用户问卷调查回答主键
     * @return 用户问卷调查回答
     */
    QuestionnaireAnswer selectQuestionnaireAnswerById(Long id);

    /**
     * 查询用户问卷调查回答列表
     * 
     * @param questionnaireAnswer 用户问卷调查回答
     * @return 用户问卷调查回答集合
     */
    List<QuestionnaireAnswer> selectQuestionnaireAnswerList(QuestionnaireAnswer questionnaireAnswer);

    /**
     * 新增用户问卷调查回答
     * 
     * @param questionnaireAnswer 用户问卷调查回答
     * @return 结果
     */
    int insertQuestionnaireAnswer(QuestionnaireAnswer questionnaireAnswer);

    /**
     * 修改用户问卷调查回答
     * 
     * @param questionnaireAnswer 用户问卷调查回答
     * @return 结果
     */
    int updateQuestionnaireAnswer(QuestionnaireAnswer questionnaireAnswer);

    /**
     * 批量删除用户问卷调查回答
     * 
     * @param ids 需要删除的用户问卷调查回答主键集合
     * @return 结果
     */
    int deleteQuestionnaireAnswerByIds(Long[] ids);

    /**
     * 删除用户问卷调查回答信息
     * 
     * @param id 用户问卷调查回答主键
     * @return 结果
     */
     int deleteQuestionnaireAnswerById(Long id);


    /**
     * 用户 问题-回答-列表
     *
     * @param id 用户问卷调查回答主键
     * @return 结果
     */
     List<QuestionAnswerVo> getQuestionAnswerVoList(Long id);
}
