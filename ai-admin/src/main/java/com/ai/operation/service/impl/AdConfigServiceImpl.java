package com.ai.operation.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.domain.dto.AdConfigAdsDto;
import com.ai.operation.domain.dto.AdConfigDto;
import com.ai.operation.domain.dto.AdConfigPublicDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.AdConfigMapper;
import com.ai.operation.domain.AdConfig;
import com.ai.operation.service.IAdConfigService;

import javax.annotation.Resource;

/**
 * 广告配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class AdConfigServiceImpl extends ServiceImpl<AdConfigMapper, AdConfig> implements IAdConfigService
{
    @Autowired
    private AdConfigMapper adConfigMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;

    @Resource
    private ObjectMapper logicObjectMapper;

    /**
     * 查询广告配置
     * 
     * @param id 广告配置主键
     * @return 广告配置
     */
    @Override
    public AdConfig selectAdConfigById(Long id)
    {
        return adConfigMapper.selectAdConfigById(id);
    }

    /**
     * 查询广告配置列表
     * 
     * @param adConfig 广告配置
     * @return 广告配置
     */
    @Override
    public List<AdConfig> selectAdConfigList(AdConfig adConfig)
    {
        return adConfigMapper.selectAdConfigList(adConfig);
    }

    /**
     * 新增广告配置
     * 
     * @param adConfig 广告配置
     * @return 结果
     */
    @Override
    public int insertAdConfig(AdConfig adConfig)
    {
        adConfig.setCreateTime(LocalDateTime.now());
        return adConfigMapper.insertAdConfig(adConfig);
    }

    /**
     * 修改广告配置
     * 
     * @param adConfig 广告配置
     * @return 结果
     */
    @Override
    public int updateAdConfig(AdConfig adConfig)
    {
        adConfig.setUpdateTime(LocalDateTime.now());
        return adConfigMapper.updateAdConfig(adConfig);
    }

    /**
     * 批量删除广告配置
     * 
     * @param ids 需要删除的广告配置主键
     * @return 结果
     */
    @Override
    public int deleteAdConfigByIds(Long[] ids)
    {
        return adConfigMapper.deleteAdConfigByIds(ids);
    }

    /**
     * 删除广告配置信息
     * 
     * @param id 广告配置主键
     * @return 结果
     */
    @Override
    public int deleteAdConfigById(Long id)
    {
        return adConfigMapper.deleteAdConfigById(id);
    }

    @Override
    public AdConfigPublicDto getAdConfigPublic(){

        String resultString = redisCachePiclumen.stringGet(LogicParamsCons.AD_CONFIG_PUBLIC);
        AdConfigPublicDto adConfigPublicResp = new AdConfigPublicDto();
        try {
            adConfigPublicResp = logicObjectMapper.readValue(resultString, AdConfigPublicDto.class);
        }catch (JsonProcessingException e) {
            log.error("json转换异常", e);
        }catch (Exception e){
            log.error("其他错误", e);
        }
        return adConfigPublicResp;
    }

    @Override
    public void setAdConfigPublic(AdConfigPublicDto adConfigPublicResp){
        try {
            redisCachePiclumen.stringSet(LogicParamsCons.AD_CONFIG_PUBLIC, logicObjectMapper.writeValueAsString(adConfigPublicResp));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
     }

    @Override
    public AjaxResult refreshCache(){

        AdConfigDto adConfigDto = new AdConfigDto();
        String resultString = redisCachePiclumen.stringGet(LogicParamsCons.AD_CONFIG_PUBLIC);
        AdConfigPublicDto adConfigPublicResp = new AdConfigPublicDto();
        try {
            adConfigPublicResp = logicObjectMapper.readValue(resultString, AdConfigPublicDto.class);
        }catch (JsonProcessingException e) {
            log.error("json转换异常", e);
        }catch (Exception e){
            log.error("其他错误", e);
        }
        BeanUtils.copyProperties(adConfigPublicResp, adConfigDto);

        List<AdConfigAdsDto> ads = new ArrayList<>();

        List<AdConfig> adConfigs = adConfigMapper.selectList(new LambdaQueryWrapper<>());
        for (AdConfig adConfig : adConfigs) {
            AdConfigAdsDto adConfigAdsDto = new AdConfigAdsDto();
            BeanUtils.copyProperties(adConfig, adConfigAdsDto);
            ads.add(adConfigAdsDto);
        }
        adConfigDto.setAds(ads);
        try {
            redisCachePiclumen.setCacheObject(LogicParamsCons.AD_CONFIG_IOS, logicObjectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(adConfigDto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return AjaxResult.success();
    }



}
