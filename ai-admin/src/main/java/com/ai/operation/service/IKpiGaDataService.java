package com.ai.operation.service;

import java.util.List;

import com.ai.operation.domain.vo.GaDateVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.KpiGaData;

/**
 * GA数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface IKpiGaDataService extends IService<KpiGaData> {
    /**
     * 查询GA数据
     * 
     * @param id GA数据主键
     * @return GA数据
     */
    public KpiGaData selectKpiGaDataById(Long id);

    /**
     * 查询GA数据列表
     * 
     * @param kpiGaData GA数据
     * @return GA数据集合
     */
    public List<KpiGaData> selectKpiGaDataList(KpiGaData kpiGaData);

    /**
     * 新增GA数据
     * 
     * @param kpiGaData GA数据
     * @return 结果
     */
    public int insertKpiGaData(KpiGaData kpiGaData);

    /**
     * 修改GA数据
     * 
     * @param kpiGaData GA数据
     * @return 结果
     */
    public int updateKpiGaData(KpiGaData kpiGaData);

    /**
     * 批量删除GA数据
     * 
     * @param ids 需要删除的GA数据主键集合
     * @return 结果
     */
    public int deleteKpiGaDataByIds(Long[] ids);

    /**
     * 删除GA数据信息
     * 
     * @param id GA数据主键
     * @return 结果
     */
    public int deleteKpiGaDataById(Long id);


    /**
     * 返回ga 需要的每日数据
     *
     * @param date 日期
     * @return 结果
     */
    List<GaDateVo> selectGaDateList(String date);
}
