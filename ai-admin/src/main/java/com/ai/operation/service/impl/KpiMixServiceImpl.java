package com.ai.operation.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.KpiMixMapper;
import com.ai.operation.domain.KpiMix;
import com.ai.operation.service.IKpiMixService;

/**
 * Kpi汇总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class KpiMixServiceImpl extends ServiceImpl<KpiMixMapper, KpiMix> implements IKpiMixService
{
    @Autowired
    private KpiMixMapper kpiMixMapper;

    /**
     * 查询Kpi汇总
     * 
     * @param id Kpi汇总主键
     * @return Kpi汇总
     */
    @Override
    public KpiMix selectKpiMixById(Long id)
    {
        return kpiMixMapper.selectKpiMixById(id);
    }

    /**
     * 查询Kpi汇总列表
     * 
     * @param kpiMix Kpi汇总
     * @return Kpi汇总
     */
    @Override
    public List<KpiMix> selectKpiMixList(KpiMix kpiMix)
    {
        return kpiMixMapper.selectKpiMixList(kpiMix);
    }

    /**
     * 新增Kpi汇总
     * 
     * @param kpiMix Kpi汇总
     * @return 结果
     */
    @Override
    public int insertKpiMix(KpiMix kpiMix)
    {
        kpiMix.setCreateTime(LocalDateTime.now());
        return kpiMixMapper.insertKpiMix(kpiMix);
    }

    /**
     * 修改Kpi汇总
     * 
     * @param kpiMix Kpi汇总
     * @return 结果
     */
    @Override
    public int updateKpiMix(KpiMix kpiMix)
    {
        kpiMix.setUpdateTime(LocalDateTime.now());
        return kpiMixMapper.updateKpiMix(kpiMix);
    }

    /**
     * 批量删除Kpi汇总
     * 
     * @param ids 需要删除的Kpi汇总主键
     * @return 结果
     */
    @Override
    public int deleteKpiMixByIds(Long[] ids)
    {
        return kpiMixMapper.deleteKpiMixByIds(ids);
    }

    /**
     * 删除Kpi汇总信息
     * 
     * @param id Kpi汇总主键
     * @return 结果
     */
    @Override
    public int deleteKpiMixById(Long id)
    {
        return kpiMixMapper.deleteKpiMixById(id);
    }

    /**
     * 最近七天的kpi汇总
     *
     * @param type 1最近一周 2最近一月 3最近三月 4 最近14天 5全部
     * @return 结果
     */
    @Override
    public List<KpiMix> selectKpiMixWeek(Integer type) {
        switch (type){
            case  1:return kpiMixMapper.selectKpiMixWeek(7);
            case  2:return kpiMixMapper.selectKpiMixWeek(30);
            case  3:return kpiMixMapper.selectKpiMixWeek(90);
            case  4:return kpiMixMapper.selectKpiMixWeek(14);
            case  5:return kpiMixMapper.selectKpiMixWeek(null);
            default:return kpiMixMapper.selectKpiMixWeek(7);
        }
    }
}
