package com.ai.operation.service.impl;

import java.util.List;

import com.ai.admin.domain.BannerImg;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.BannerJumpType;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.controller.resp.BannerJumpIdTreeResp;
import com.ai.operation.domain.CommActivity;
import com.ai.operation.mapper.CommActivityMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.BannerImgIosMapper;
import com.ai.operation.domain.BannerImgIos;
import com.ai.operation.service.IBannerImgIosService;

/**
 * ios_社区首页banner图配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Service
@Slf4j
public class BannerImgIosServiceImpl extends ServiceImpl<BannerImgIosMapper, BannerImgIos> implements IBannerImgIosService
{
    @Autowired
    private BannerImgIosMapper bannerImgIosMapper;

    @Autowired
    private CommActivityMapper commActivityMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;
    /**
     * 查询ios_社区首页banner图配置
     * 
     * @param id ios_社区首页banner图配置主键
     * @return ios_社区首页banner图配置
     */
    @Override
    public BannerImgIos selectBannerImgIosById(Long id)
    {
        return bannerImgIosMapper.selectBannerImgIosById(id);
    }

    /**
     * 查询ios_社区首页banner图配置列表
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return ios_社区首页banner图配置
     */
    @Override
    public List<BannerImgIos> selectBannerImgIosList(BannerImgIos bannerImgIos)
    {
        return bannerImgIosMapper.selectBannerImgIosList(bannerImgIos);
    }

    /**
     * 新增ios_社区首页banner图配置
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return 结果
     */
    @Override
    public AjaxResult insertBannerImgIos(BannerImgIos bannerImgIos, LoginUser loginUser)
    {
        LambdaQueryWrapper<BannerImgIos>  biq = new LambdaQueryWrapper<>();
        biq.eq(BannerImgIos::getSort, bannerImgIos.getSort());
        biq.eq(BannerImgIos::getIsDeleted, Boolean.FALSE);
        Long aLong = bannerImgIosMapper.selectCount(biq);
        if (aLong >= 1){
            return  AjaxResult.error("此banner 权重排序已存在!");
        }

        bannerImgIos.setCreateTime(LocalDateTime.now());
        bannerImgIos.setCreateBy(loginUser.getUsername());
        bannerImgIos.setUpdateBy(loginUser.getUsername());
        Integer result = bannerImgIosMapper.insert(bannerImgIos);
        if (result > 0){
            delBannerIosCache();
        }
        return AjaxResult.success(result);

    }

    /**
     * 修改ios_社区首页banner图配置
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return 结果
     */
    @Override
    public AjaxResult updateBannerImgIos(BannerImgIos bannerImgIos,LoginUser loginUser)
    {
        LambdaQueryWrapper<BannerImgIos>  biq = new LambdaQueryWrapper<>();
        biq.eq(BannerImgIos::getSort, bannerImgIos.getSort());
        biq.ne(BannerImgIos::getId, bannerImgIos.getId());
        biq.eq(BannerImgIos::getIsDeleted, Boolean.FALSE);
        Long aLong = bannerImgIosMapper.selectCount(biq);
        if (aLong >= 1){
            return  AjaxResult.error("此banner 权重排序已存在!");
        }
        bannerImgIos.setUpdateTime(LocalDateTime.now());
        bannerImgIos.setUpdateBy(loginUser.getUsername());
        Integer result =  bannerImgIosMapper.updateById(bannerImgIos);
        if (result > 0){
            delBannerIosCache();
        }
        return AjaxResult.success(result);
    }

    /**
     * 批量删除ios_社区首页banner图配置
     * 
     * @param ids 需要删除的ios_社区首页banner图配置主键
     * @return 结果
     */
    @Override
    public int deleteBannerImgIosByIds(Long[] ids,LoginUser loginUser)
    {
        LambdaUpdateWrapper<BannerImgIos> biu = new LambdaUpdateWrapper<>();
        biu.in(BannerImgIos::getId, ids);
        biu.set(BannerImgIos::getIsDeleted,Boolean.TRUE);
        biu.set(BannerImgIos::getUpdateBy, loginUser.getUsername());
        Integer result = bannerImgIosMapper.update(null, biu);
        if (result > 0){
            delBannerIosCache();
        }
        return result;
    }

    /**
     * 删除ios_社区首页banner图配置信息
     * 
     * @param id ios_社区首页banner图配置主键
     * @return 结果
     */
    @Override
    public int deleteBannerImgIosById(Long id)
    {
        return bannerImgIosMapper.deleteBannerImgIosById(id);
    }

    @Override
    public List<BannerJumpIdTreeResp> getBannerJumpIdTree() {
        // 查询活动数据
        List<CommActivity> commActivities = commActivityMapper.selectList(null);

        // 构建活动类型的跳转数据
        List<BannerJumpIdTreeResp> activityList = commActivities.stream()
                .map(activity -> {
                    BannerJumpIdTreeResp resp = new BannerJumpIdTreeResp();
                    resp.setLabel(activity.getTitle());
                    resp.setValue(activity.getId());
                    resp.setJumpType(BannerJumpType.activity.getValue());
                    return resp;
                })
                .collect(Collectors.toList());

        // 如未来有其他类型跳转，可在此合并多个列表
        return activityList;
    }

    private void delBannerIosCache() {
        //删除缓存
        try {
            log.info("清空redis:{}",LogicParamsCons.BANNER_IMG_CACHE_KEY_IOS);
            redisCachePiclumen.deleteObject(LogicParamsCons.BANNER_IMG_CACHE_KEY_IOS);
        } catch (Exception e) {
            log.error("管理后台更改社区首页banner图(ios), 数据写入redis缓存异常", e);
        }
    }

}
