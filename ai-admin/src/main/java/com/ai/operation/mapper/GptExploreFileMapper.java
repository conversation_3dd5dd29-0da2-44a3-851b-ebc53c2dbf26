package com.ai.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.operation.domain.GptExploreFile;

/**
 * explore页随机展示Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
public interface GptExploreFileMapper extends BaseMapper<GptExploreFile> {
    /**
     * 查询explore页随机展示
     * 
     * @param id explore页随机展示主键
     * @return explore页随机展示
     */
    public GptExploreFile selectGptExploreFileById(Long id);

    /**
     * 查询explore页随机展示列表
     * 
     * @param gptExploreFile explore页随机展示
     * @return explore页随机展示集合
     */
    public List<GptExploreFile> selectGptExploreFileList(GptExploreFile gptExploreFile);

    /**
     * 新增explore页随机展示
     * 
     * @param gptExploreFile explore页随机展示
     * @return 结果
     */
    public int insertGptExploreFile(GptExploreFile gptExploreFile);

    /**
     * 修改explore页随机展示
     * 
     * @param gptExploreFile explore页随机展示
     * @return 结果
     */
    public int updateGptExploreFile(GptExploreFile gptExploreFile);

    /**
     * 删除explore页随机展示
     * 
     * @param id explore页随机展示主键
     * @return 结果
     */
    public int deleteGptExploreFileById(Long id);

    /**
     * 批量删除explore页随机展示
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptExploreFileByIds(Long[] ids);
}
