package com.ai.operation.mapper;

import java.util.List;

import com.ai.operation.domain.KpiMix;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.operation.domain.KpiDayMaxTaskSize;
import org.apache.ibatis.annotations.Param;
/**
 * 每日规则最大并发数Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface KpiDayMaxTaskSizeMapper extends BaseMapper<KpiDayMaxTaskSize> {
    /**
     * 查询每日规则最大并发数
     * 
     * @param id 每日规则最大并发数主键
     * @return 每日规则最大并发数
     */
    public KpiDayMaxTaskSize selectKpiDayMaxTaskSizeById(Long id);

    /**
     * 查询每日规则最大并发数列表
     * 
     * @param kpiDayMaxTaskSize 每日规则最大并发数
     * @return 每日规则最大并发数集合
     */
    public List<KpiDayMaxTaskSize> selectKpiDayMaxTaskSizeList(KpiDayMaxTaskSize kpiDayMaxTaskSize);

    /**
     * 新增每日规则最大并发数
     * 
     * @param kpiDayMaxTaskSize 每日规则最大并发数
     * @return 结果
     */
    public int insertKpiDayMaxTaskSize(KpiDayMaxTaskSize kpiDayMaxTaskSize);

    /**
     * 修改每日规则最大并发数
     * 
     * @param kpiDayMaxTaskSize 每日规则最大并发数
     * @return 结果
     */
    public int updateKpiDayMaxTaskSize(KpiDayMaxTaskSize kpiDayMaxTaskSize);

    /**
     * 删除每日规则最大并发数
     * 
     * @param id 每日规则最大并发数主键
     * @return 结果
     */
    public int deleteKpiDayMaxTaskSizeById(Long id);

    /**
     * 批量删除每日规则最大并发数
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKpiDayMaxTaskSizeByIds(Long[] ids);

    List<KpiDayMaxTaskSize>  getKpiDayMaxTaskSizeLast(@Param("day")Integer day, @Param("fair") Boolean fair);
}
