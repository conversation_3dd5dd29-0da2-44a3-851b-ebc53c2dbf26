package com.ai.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.operation.domain.BannerImgIos;

/**
 * ios_社区首页banner图配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface BannerImgIosMapper extends BaseMapper<BannerImgIos> {
    /**
     * 查询ios_社区首页banner图配置
     * 
     * @param id ios_社区首页banner图配置主键
     * @return ios_社区首页banner图配置
     */
   BannerImgIos selectBannerImgIosById(Long id);

    /**
     * 查询ios_社区首页banner图配置列表
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return ios_社区首页banner图配置集合
     */
    List<BannerImgIos> selectBannerImgIosList(BannerImgIos bannerImgIos);

    /**
     * 新增ios_社区首页banner图配置
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return 结果
     */
    int insertBannerImgIos(BannerImgIos bannerImgIos);

    /**
     * 修改ios_社区首页banner图配置
     * 
     * @param bannerImgIos ios_社区首页banner图配置
     * @return 结果
     */
    int updateBannerImgIos(BannerImgIos bannerImgIos);

    /**
     * 删除ios_社区首页banner图配置
     * 
     * @param id ios_社区首页banner图配置主键
     * @return 结果
     */
    int deleteBannerImgIosById(Long id);

    /**
     * 批量删除ios_社区首页banner图配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBannerImgIosByIds(Long[] ids);
}
