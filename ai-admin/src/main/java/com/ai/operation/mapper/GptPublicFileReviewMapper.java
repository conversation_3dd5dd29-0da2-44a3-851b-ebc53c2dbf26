package com.ai.operation.mapper;

import java.util.List;

import com.ai.operation.domain.vo.GptPublicFileReviewPageVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.operation.domain.GptPublicFileReview;

/**
 * 用户图片公开审核Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface GptPublicFileReviewMapper extends BaseMapper<GptPublicFileReview> {
    /**
     * 查询用户图片公开审核
     * 
     * @param id 用户图片公开审核主键
     * @return 用户图片公开审核
     */
    public GptPublicFileReview selectGptPublicFileReviewById(Long id);

    /**
     * 查询用户图片公开审核列表
     * 
     * @param gptPublicFileReview 用户图片公开审核
     * @return 用户图片公开审核集合
     */
     List<GptPublicFileReviewPageVo> selectGptPublicFileReviewList(GptPublicFileReview gptPublicFileReview);

    /**
     * 新增用户图片公开审核
     * 
     * @param gptPublicFileReview 用户图片公开审核
     * @return 结果
     */
    public int insertGptPublicFileReview(GptPublicFileReview gptPublicFileReview);

    /**
     * 修改用户图片公开审核
     * 
     * @param gptPublicFileReview 用户图片公开审核
     * @return 结果
     */
    public int updateGptPublicFileReview(GptPublicFileReview gptPublicFileReview);

    /**
     * 删除用户图片公开审核
     * 
     * @param id 用户图片公开审核主键
     * @return 结果
     */
    public int deleteGptPublicFileReviewById(Long id);

    /**
     * 批量删除用户图片公开审核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptPublicFileReviewByIds(Long[] ids);
}
