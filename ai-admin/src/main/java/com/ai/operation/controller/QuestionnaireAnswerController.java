package com.ai.operation.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.QuestionnaireAnswer;
import com.ai.operation.service.IQuestionnaireAnswerService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 用户问卷调查回答Controller
 * 
 * <AUTHOR>
 * @date 2025-03-20
 */
@RestController
@Api(value = "用户问卷调查回答控制器", tags = {"用户问卷调查回答管理"})
@RequestMapping("/operation/answer")
public class QuestionnaireAnswerController extends BaseController {
    @Autowired
    private IQuestionnaireAnswerService questionnaireAnswerService;

    /**
     * 查询用户问卷调查回答列表
     */
    @PreAuthorize("@ss.hasPermi('operation:answer:list')")
    @ApiOperation("查询用户问卷调查回答列表")
    @GetMapping("/list")
    public TableDataInfo list(QuestionnaireAnswer questionnaireAnswer) {
        startPage();
        List<QuestionnaireAnswer> list = questionnaireAnswerService.selectQuestionnaireAnswerList(questionnaireAnswer);
        return getDataTable(list);
    }

    /**
     * 导出用户问卷调查回答列表
     */
    @ApiOperation("导出用户问卷调查回答列表")
    @PreAuthorize("@ss.hasPermi('operation:answer:export')")
    @Log(title = "用户问卷调查回答", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuestionnaireAnswer questionnaireAnswer) {
        List<QuestionnaireAnswer> list = questionnaireAnswerService.selectQuestionnaireAnswerList(questionnaireAnswer);
        ExcelUtil<QuestionnaireAnswer> util = new ExcelUtil<QuestionnaireAnswer>(QuestionnaireAnswer.class);
        util.exportExcel(response, list, "用户问卷调查回答数据");
    }

    /**
     * 获取用户问卷调查回答详细信息
     */
    @ApiOperation("获取用户问卷调查回答详细信息")
    @PreAuthorize("@ss.hasPermi('operation:answer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(questionnaireAnswerService.selectQuestionnaireAnswerById(id));
    }

    /**
     * 新增用户问卷调查回答
     */
    @ApiOperation("新增用户问卷调查回答")
    @PreAuthorize("@ss.hasPermi('operation:answer:add')")
    @Log(title = "用户问卷调查回答", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuestionnaireAnswer questionnaireAnswer) {
        return toAjax(questionnaireAnswerService.insertQuestionnaireAnswer(questionnaireAnswer));
    }

    /**
     * 修改用户问卷调查回答
     */
    @ApiOperation("修改用户问卷调查回答")
    @PreAuthorize("@ss.hasPermi('operation:answer:edit')")
    @Log(title = "用户问卷调查回答", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionnaireAnswer questionnaireAnswer) {
        return toAjax(questionnaireAnswerService.updateQuestionnaireAnswer(questionnaireAnswer));
    }

    /**
     * 删除用户问卷调查回答
     */
    @ApiOperation("删除用户问卷调查回答")
    @PreAuthorize("@ss.hasPermi('operation:answer:remove')")
    @Log(title = "用户问卷调查回答", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(questionnaireAnswerService.deleteQuestionnaireAnswerByIds(ids));
    }


    /**
     * 用户 问题-回答-列表
     */
    @ApiOperation("用户 问题-回答-列表")
    @PreAuthorize("@ss.hasPermi('operation:answer:query')")
    @GetMapping(value = "/question-answer/{id}")
    public AjaxResult getQuestionAnswer(@PathVariable("id") Long id) {
        return success(questionnaireAnswerService.getQuestionAnswerVoList(id));
    }
}
