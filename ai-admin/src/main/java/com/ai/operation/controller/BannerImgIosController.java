package com.ai.operation.controller;

import java.util.List;

import com.ai.common.core.domain.model.LoginUser;
import com.ai.operation.controller.resp.BannerJumpIdTreeResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.BannerImgIos;
import com.ai.operation.service.IBannerImgIosService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * ios_社区首页banner图配置Controller
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@RestController
@Api(value = "ios_社区首页banner图配置控制器", tags = {"ios_社区首页banner图配置管理"})
@RequestMapping("/operation/iosBanner")
public class BannerImgIosController extends BaseController {
    @Autowired
    private IBannerImgIosService bannerImgIosService;

    /**
     * 查询ios_社区首页banner图配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:iosBanner:list')")
    @ApiOperation("查询ios_社区首页banner图配置列表")
    @GetMapping("/list")
    public TableDataInfo list(BannerImgIos bannerImgIos) {
        startPage();
        List<BannerImgIos> list = bannerImgIosService.selectBannerImgIosList(bannerImgIos);
        return getDataTable(list);
    }

    /**
     * 导出ios_社区首页banner图配置列表
     */
    @ApiOperation("导出ios_社区首页banner图配置列表")
    @PreAuthorize("@ss.hasPermi('operation:iosBanner:export')")
    @Log(title = "ios_社区首页banner图配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BannerImgIos bannerImgIos) {
        List<BannerImgIos> list = bannerImgIosService.selectBannerImgIosList(bannerImgIos);
        ExcelUtil<BannerImgIos> util = new ExcelUtil<BannerImgIos>(BannerImgIos.class);
        util.exportExcel(response, list, "ios_社区首页banner图配置数据");
    }

    /**
     * 获取ios_社区首页banner图配置详细信息
     */
    @ApiOperation("获取ios_社区首页banner图配置详细信息")
    @PreAuthorize("@ss.hasPermi('operation:iosBanner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bannerImgIosService.selectBannerImgIosById(id));
    }

    /**
     * 新增ios_社区首页banner图配置
     */
    @ApiOperation("新增ios_社区首页banner图配置")
    @PreAuthorize("@ss.hasPermi('operation:iosBanner:add')")
    @Log(title = "ios_社区首页banner图配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BannerImgIos bannerImgIos) {
        LoginUser loginUser = getLoginUser();
        return bannerImgIosService.insertBannerImgIos(bannerImgIos, loginUser);
    }

    /**
     * 修改ios_社区首页banner图配置
     */
    @ApiOperation("修改ios_社区首页banner图配置")
    @PreAuthorize("@ss.hasPermi('operation:iosBanner:edit')")
    @Log(title = "ios_社区首页banner图配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BannerImgIos bannerImgIos) {
        LoginUser loginUser = getLoginUser();
        return bannerImgIosService.updateBannerImgIos(bannerImgIos,loginUser);
    }

    /**
     * 删除ios_社区首页banner图配置
     */
    @ApiOperation("删除ios_社区首页banner图配置")
    @PreAuthorize("@ss.hasPermi('operation:iosBanner:remove')")
    @Log(title = "ios_社区首页banner图配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        LoginUser loginUser = getLoginUser();
        return toAjax(bannerImgIosService.deleteBannerImgIosByIds(ids,loginUser));
    }

    /**
     * 获取ios_社区首页banner图配置详细信息
     */
    @ApiOperation("获取ios_社区首页banner图配置详细信息")
    @GetMapping(value = "/get-jump_id_tree")
    public AjaxResult  getBannerJumpIdTree(){
        return success(bannerImgIosService.getBannerJumpIdTree());
    }
}
