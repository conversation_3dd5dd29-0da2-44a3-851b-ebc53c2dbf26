package com.ai.operation.controller.req;

import com.ai.operation.domain.dto.ActivityRewardSelectionDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "设置评选传入参数")
public class ActivityRewardSelectionReq {

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("获得奖项")
    private Long prizeLevel;

    @ApiModelProperty("评选参数列表")
    private List<ActivityRewardSelectionDto> activityRewardSelectionDtoList;

}
