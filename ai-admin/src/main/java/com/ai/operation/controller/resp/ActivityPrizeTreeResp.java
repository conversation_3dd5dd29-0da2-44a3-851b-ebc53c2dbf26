package com.ai.operation.controller.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "活动奖项树形返回参数")
public class ActivityPrizeTreeResp {

    @ApiModelProperty("label 对应 level_name")
    private String label;

    @ApiModelProperty("value 对应level")
    private Long value;

    @ApiModelProperty("图片链接")
    private String icon;
}
