package com.ai.operation.controller.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "活动奖项树形返回参数")
public class BannerJumpIdTreeResp {


    @ApiModelProperty("label 对应 对应数据的名称")
    private String label;

    @ApiModelProperty("value 对应jumpId")
    private Long value;

    /**
     *  {@link com.ai.common.enums.BannerJumpType}
     *
     */
    @ApiModelProperty("跳转类型 activity (活动)| tools(工具)")
    private String jumpType;
}
