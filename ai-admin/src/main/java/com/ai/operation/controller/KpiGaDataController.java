package com.ai.operation.controller;

import java.util.List;

import com.ai.operation.domain.vo.GaDateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.KpiGaData;
import com.ai.operation.service.IKpiGaDataService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * GA数据Controller
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
@RestController
@Api(value = "GA数据控制器", tags = {"GA数据管理"})
@RequestMapping("/operation/gaData")
public class KpiGaDataController extends BaseController {
    @Autowired
    private IKpiGaDataService kpiGaDataService;

    /**
     * 查询GA数据列表
     */
    @PreAuthorize("@ss.hasPermi('operation:gaData:list')")
    @ApiOperation("查询GA数据列表")
    @GetMapping("/list")
    public TableDataInfo list(KpiGaData kpiGaData) {
        startPage();
        List<KpiGaData> list = kpiGaDataService.selectKpiGaDataList(kpiGaData);
        return getDataTable(list);
    }

    /**
     * 导出GA数据列表
     */
    @ApiOperation("导出GA数据列表")
    @PreAuthorize("@ss.hasPermi('operation:gaData:export')")
    @Log(title = "GA数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiGaData kpiGaData) {
        List<KpiGaData> list = kpiGaDataService.selectKpiGaDataList(kpiGaData);
        ExcelUtil<KpiGaData> util = new ExcelUtil<KpiGaData>(KpiGaData.class);
        util.exportExcel(response, list, "GA数据数据");
    }

    /**
     * 获取GA数据详细信息
     */
    @ApiOperation("获取GA数据详细信息")
    @PreAuthorize("@ss.hasPermi('operation:gaData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kpiGaDataService.selectKpiGaDataById(id));
    }

    /**
     * 新增GA数据
     */
    @ApiOperation("新增GA数据")
    @PreAuthorize("@ss.hasPermi('operation:gaData:add')")
    @Log(title = "GA数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiGaData kpiGaData) {
        return toAjax(kpiGaDataService.insertKpiGaData(kpiGaData));
    }

    /**
     * 修改GA数据
     */
    @ApiOperation("修改GA数据")
    @PreAuthorize("@ss.hasPermi('operation:gaData:edit')")
    @Log(title = "GA数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiGaData kpiGaData) {
        return toAjax(kpiGaDataService.updateKpiGaData(kpiGaData));
    }

    /**
     * 删除GA数据
     */
    @ApiOperation("删除GA数据")
    @PreAuthorize("@ss.hasPermi('operation:gaData:remove')")
    @Log(title = "GA数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(kpiGaDataService.deleteKpiGaDataByIds(ids));
    }


    /**
     * 返回ga 需要的每日数据
     *
     * @param date 日期
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('operation:gaData:list')")
    @ApiOperation("返回ga 需要的每日数据")
    @GetMapping("/ga-data-list")
    public AjaxResult selectGaDateList(@RequestParam("date") String date){
        return success(kpiGaDataService.selectGaDateList(date));
    }
}
