package com.ai.operation.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ai.admin.domain.vo.ModelNumVo;
import com.ai.operation.domain.KpiDayMaxTaskSize;
import com.ai.quartz.task.StatisticsServiceTask;
import com.ai.task.domain.vo.DayMaxTaskSizeVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.*;

import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.KpiMix;
import com.ai.operation.service.IKpiMixService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * Kpi汇总Controller
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@Api(value = "Kpi汇总控制器", tags = {"Kpi汇总管理"})
@RequestMapping("/operation/mix")
public class KpiMixController extends BaseController {
    @Autowired
    private IKpiMixService kpiMixService;

    @Autowired
    private StatisticsServiceTask statisticsServiceTask;

    /**
     * 查询Kpi汇总列表
     */
    @PreAuthorize("@ss.hasPermi('operation:mix:list')")
    @ApiOperation("查询Kpi汇总列表")
    @GetMapping("/list")
    public TableDataInfo list(KpiMix kpiMix) {
        startPage();
        List<KpiMix> list = kpiMixService.selectKpiMixList(kpiMix);
        return getDataTable(list);
    }

    /**
     * 导出Kpi汇总列表
     */
    @ApiOperation("导出Kpi汇总列表")
    @PreAuthorize("@ss.hasPermi('operation:mix:export')")
    @Log(title = "Kpi汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiMix kpiMix) {
        List<KpiMix> list = kpiMixService.selectKpiMixList(kpiMix);
        ExcelUtil<KpiMix> util = new ExcelUtil<KpiMix>(KpiMix.class);
        util.exportExcel(response, list, "Kpi汇总数据");
    }

    /**
     * 获取Kpi汇总详细信息
     */
    @ApiOperation("获取Kpi汇总详细信息")
    @PreAuthorize("@ss.hasPermi('operation:mix:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kpiMixService.selectKpiMixById(id));
    }

    /**
     * 新增Kpi汇总
     */
    @ApiOperation("新增Kpi汇总")
    @PreAuthorize("@ss.hasPermi('operation:mix:add')")
    @Log(title = "Kpi汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiMix kpiMix) {
        return toAjax(kpiMixService.insertKpiMix(kpiMix));
    }

    /**
     * 修改Kpi汇总
     */
    @ApiOperation("修改Kpi汇总")
    @PreAuthorize("@ss.hasPermi('operation:mix:edit')")
    @Log(title = "Kpi汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiMix kpiMix) {
        return toAjax(kpiMixService.updateKpiMix(kpiMix));
    }

    /**
     * 删除Kpi汇总
     */
    @ApiOperation("删除Kpi汇总")
    @PreAuthorize("@ss.hasPermi('operation:mix:remove')")
    @Log(title = "Kpi汇总", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(kpiMixService.deleteKpiMixByIds(ids));
    }

    @ApiOperation("获取Kpi最新一条数据")
    @GetMapping("/getMixLast")
    public AjaxResult getMixList() {
        return success(kpiMixService.getOne(new LambdaQueryWrapper<KpiMix>().last("order by record_date desc limit 1")));
    }
 
    /**
     * 最近七天的kpi汇总
     *
     * @param type 1最近一周 2最近一月 3最近三月 4 最近14天
     * @return 结果
     */
    @ApiOperation("最近的kpi汇总")
    @GetMapping("/getKpiMixWeek/{type}")
    public AjaxResult selectKpiMixWeek(@PathVariable("type") Integer type) {
        return success(kpiMixService.selectKpiMixWeek(type));
    }

    @ApiOperation("kpi信息补全")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "日期(yyyy-MM-dd) 这是日期结束时间 如你要补全2024-10-01 的数据 需要传入2024-10-02", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0 发邮件和插入kpi 数据 1 只发邮件 2 只插入kpi数据", required = true, dataType = "Integer"),
    })
    @PreAuthorize("@ss.hasPermi('operation:mix:insert')")
    @GetMapping("/completeData")
    public AjaxResult completeData(@RequestParam("date") String date, @RequestParam("type") Integer type){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date dateResult = null;
        try {
            dateResult = formatter.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return error("日期格式错误");
        }
        statisticsServiceTask.statisticsCreatePictureChild(dateResult,type);
        return success();
    }

    @ApiOperation("mongodb日活数据补全")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "日期(yyyy-MM-dd) 这是日期结束时间", required = true, dataType = "String"),
    })
    @PreAuthorize("@ss.hasPermi('operation:mix:insert')")
    @GetMapping("/completeUserActivity")
    public AjaxResult completeUserActivity(@RequestParam("date") String date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date dateResult = null;
        try {
            dateResult = formatter.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return error("日期格式错误");
        }
        statisticsServiceTask.completeUserActivity(dateResult);
        return success();
    }

    @ApiOperation("返回每日最大公平非共阈值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "日期(yyyy-MM-dd) 这是日期结束时间", required = true, dataType = "String"),
    })
    @PreAuthorize("@ss.hasPermi('operation:mix:insert')")
    @GetMapping("/getMaxTaskSizeForDate")
    public AjaxResult getMaxTaskSizeForDate(@RequestParam("date") String date){
        Map<String, Long> maxTaskSizeForDate = statisticsServiceTask.getMaxTaskSizeForDate(date);
        return success(maxTaskSizeForDate);
    }

    @ApiOperation("返回每日最大公平非共阈列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "日期(yyyy-MM-dd) 这是日期结束时间", required = true, dataType = "String"),
    })
    @GetMapping("/getMaxTaskSizeForDateList")
    public AjaxResult getMaxTaskSizeForDateList(@RequestParam("date") String date){
        List<KpiDayMaxTaskSize> dayMaxTaskSize = statisticsServiceTask.getDayMaxTaskSize(date);
        return success(dayMaxTaskSize);
    }

    @GetMapping("/getModelNum")
    public AjaxResult getModelNum(@RequestParam("date") String date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date dateResult = null;
        try {
            dateResult = formatter.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return error("日期格式错误");
        }
        List<ModelNumVo> modelNum = statisticsServiceTask.getModelNum(dateResult);
        return success(modelNum);
    }

}
