package com.ai.operation.controller.resp;

import com.ai.operation.domain.dto.CommActivityGiftContentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "活动获奖列表")
public class ActivityRewardListResp {

    /** 用户名称 */
    @ApiModelProperty("用户名称")
    private String loginName;

    /** 图片链接 */
    @ApiModelProperty("图片链接")
    private String fileUrl;

    /** 点赞数量 */
    @ApiModelProperty("点赞数量")
    private Long likesNum;

    /** 评论数量 */
    @ApiModelProperty("评论数量")
    private Long commentNum;

    /** 获得奖项 */
    @ApiModelProperty("获得奖项")
    private Long prizeLevel;

    /** 奖牌名称 */
    @ApiModelProperty("奖牌名称")
    private String levelName;

    @ApiModelProperty("奖励内容")
    private List<CommActivityGiftContentDto>  commActivityGiftContentDtoList;
}
