package com.ai.operation.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.GptExploreFile;
import com.ai.operation.service.IGptExploreFileService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * explore页随机展示Controller
 * 
 * <AUTHOR>
 * @date 2024-09-28
 */
@RestController
@Api(value = "explore页随机展示控制器", tags = {"explore页随机展示管理"})
@RequestMapping("/operation/file")
public class GptExploreFileController extends BaseController {
    @Autowired
    private IGptExploreFileService gptExploreFileService;

    /**
     * 查询explore页随机展示列表
     */
    @PreAuthorize("@ss.hasPermi('operation:file:list')")
    @ApiOperation("查询explore页随机展示列表")
    @GetMapping("/list")
    public TableDataInfo list(GptExploreFile gptExploreFile) {
        startPage();
        List<GptExploreFile> list = gptExploreFileService.selectGptExploreFileList(gptExploreFile);
        return getDataTable(list);
    }

    /**
     * 导出explore页随机展示列表
     */
    @ApiOperation("导出explore页随机展示列表")
    @PreAuthorize("@ss.hasPermi('operation:file:export')")
    @Log(title = "explore页随机展示", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptExploreFile gptExploreFile) {
        List<GptExploreFile> list = gptExploreFileService.selectGptExploreFileList(gptExploreFile);
        ExcelUtil<GptExploreFile> util = new ExcelUtil<GptExploreFile>(GptExploreFile.class);
        util.exportExcel(response, list, "explore页随机展示数据");
    }

    /**
     * 获取explore页随机展示详细信息
     */
    @ApiOperation("获取explore页随机展示详细信息")
    @PreAuthorize("@ss.hasPermi('operation:file:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptExploreFileService.selectGptExploreFileById(id));
    }

    /**
     * 新增explore页随机展示
     */
    @ApiOperation("新增explore页随机展示")
    @PreAuthorize("@ss.hasPermi('operation:file:add')")
    @Log(title = "explore页随机展示", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptExploreFile gptExploreFile) {
        return toAjax(gptExploreFileService.insertGptExploreFile(gptExploreFile));
    }

    /**
     * 修改explore页随机展示
     */
    @ApiOperation("修改explore页随机展示")
    @PreAuthorize("@ss.hasPermi('operation:file:edit')")
    @Log(title = "explore页随机展示", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptExploreFile gptExploreFile) {
        return toAjax(gptExploreFileService.updateGptExploreFile(gptExploreFile));
    }

    /**
     * 删除explore页随机展示
     */
    @ApiOperation("删除explore页随机展示")
    @PreAuthorize("@ss.hasPermi('operation:file:remove')")
    @Log(title = "explore页随机展示", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptExploreFileService.deleteGptExploreFileByIds(ids));
    }
}
