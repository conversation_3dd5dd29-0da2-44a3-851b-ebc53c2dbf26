package com.ai.operation.controller.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "活动奖项评选返回参数")
public class ActivityPrizeLevelResp {


    /** 获得奖项 */
    @ApiModelProperty("获得奖项")
    private Long prizeLevel;

    @ApiModelProperty("获奖人数")
    private Integer winnersNum;

    @ApiModelProperty("奖牌名称")
    private String levelName;

    @ApiModelProperty("奖牌图片链接")
    private String icon;

    @ApiModelProperty("已设置数量")
    private Integer usedNum;
}
