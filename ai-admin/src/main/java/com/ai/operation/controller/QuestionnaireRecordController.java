package com.ai.operation.controller;

import java.util.List;

import com.ai.operation.domain.GptSpecifiedCountry;
import com.ai.operation.domain.excel.QuestionnaireDetailsImport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.QuestionnaireRecord;
import com.ai.operation.service.IQuestionnaireRecordService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 调查问卷记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-20
 */
@RestController
@Api(value = "调查问卷记录控制器", tags = {"调查问卷记录管理"})
@RequestMapping("/operation/record")
public class QuestionnaireRecordController extends BaseController {
    @Autowired
    private IQuestionnaireRecordService questionnaireRecordService;

    /**
     * 查询调查问卷记录列表
     */
    @PreAuthorize("@ss.hasPermi('operation:record:list')")
    @ApiOperation("查询调查问卷记录列表")
    @GetMapping("/list")
    public TableDataInfo list(QuestionnaireRecord questionnaireRecord) {
        startPage();
        List<QuestionnaireRecord> list = questionnaireRecordService.selectQuestionnaireRecordList(questionnaireRecord);
        return getDataTable(list);
    }

    /**
     * 导出调查问卷记录列表
     */
    @ApiOperation("导出调查问卷记录列表")
    @PreAuthorize("@ss.hasPermi('operation:record:export')")
    @Log(title = "调查问卷记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuestionnaireRecord questionnaireRecord) {
        List<QuestionnaireRecord> list = questionnaireRecordService.selectQuestionnaireRecordList(questionnaireRecord);
        ExcelUtil<QuestionnaireRecord> util = new ExcelUtil<QuestionnaireRecord>(QuestionnaireRecord.class);
        util.exportExcel(response, list, "调查问卷记录数据");
    }

    /**
     * 获取调查问卷记录详细信息
     */
    @ApiOperation("获取调查问卷记录详细信息")
    @PreAuthorize("@ss.hasPermi('operation:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(questionnaireRecordService.selectQuestionnaireRecordById(id));
    }

    /**
     * 新增调查问卷记录
     */
    @ApiOperation("新增调查问卷记录")
    @PreAuthorize("@ss.hasPermi('operation:record:add')")
    @Log(title = "调查问卷记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuestionnaireRecord questionnaireRecord) {
        return toAjax(questionnaireRecordService.insertQuestionnaireRecord(questionnaireRecord));
    }

    /**
     * 修改调查问卷记录
     */
    @ApiOperation("修改调查问卷记录")
    @PreAuthorize("@ss.hasPermi('operation:record:edit')")
    @Log(title = "调查问卷记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionnaireRecord questionnaireRecord) {
        Integer result = questionnaireRecordService.updateQuestionnaireRecord(questionnaireRecord);
        return result > 0 ? toAjax(result) : AjaxResult.error("修改失败 请检查问卷数据是否正确");

    }

    /**
     * 删除调查问卷记录
     */
    @ApiOperation("删除调查问卷记录")
    @PreAuthorize("@ss.hasPermi('operation:record:remove')")
    @Log(title = "调查问卷记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(questionnaireRecordService.deleteQuestionnaireRecordByIds(ids));
    }

    @ApiOperation("下载模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<QuestionnaireDetailsImport> util = new ExcelUtil<QuestionnaireDetailsImport>(QuestionnaireDetailsImport.class);
        util.importTemplateExcel(response, "调查问卷-问题-导入模板");
    }

    @Log(title = "导入问卷数据", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('operation:record:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,String id) throws Exception {
        ExcelUtil<QuestionnaireDetailsImport> util = new ExcelUtil<QuestionnaireDetailsImport>(QuestionnaireDetailsImport.class);
        List<QuestionnaireDetailsImport> questionnaireDetailsImports = util.importExcel(file.getInputStream());

        if (CollectionUtils.isEmpty(questionnaireDetailsImports)){
            return AjaxResult.error("导入数据不能为空！");
        }
        String message =  questionnaireRecordService.importQuestionnaire(questionnaireDetailsImports,id);
        return success(message);
    }

    /**
     * 发布问卷数据
     */
    @ApiOperation("发布问卷数据")
    @PreAuthorize("@ss.hasPermi('operation:record:edit')")
    @Log(title = "发布问卷数据", businessType = BusinessType.UPDATE)
    @GetMapping("/to-publish/{id}")
    public AjaxResult toPublish(@PathVariable Long id) {
        Boolean result = questionnaireRecordService.toPublish(id);
        return result ? success("发布成功") : AjaxResult.error("发布失败 请检查问卷数据是否正确");
    }

    /**
     * 取消发布
     */
    @ApiOperation("取消发布")
    @PreAuthorize("@ss.hasPermi('operation:record:edit')")
    @Log(title = "取消发布", businessType = BusinessType.UPDATE)
    @GetMapping("/cancel-publish/{id}")
    public AjaxResult cancelPublish(@PathVariable Long id) {
        Boolean result = questionnaireRecordService.toCancelPublish(id);
        return result ? success("取消发布成功") : AjaxResult.error("取消发布失败");
    }

}
