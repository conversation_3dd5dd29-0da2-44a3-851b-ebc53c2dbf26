package com.ai.operation.controller;

import com.ai.admin.domain.vo.ModelNumVo;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.BusinessType;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.constants.LogicParamsCons;
import com.ai.defined.utils.OperationCacheService;
import com.ai.operation.domain.GptPublicFileReview;
import com.ai.operation.domain.vo.GptPublicFileReviewPageVo;
import com.ai.task.domain.vo.DayMaxTaskSizeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 运营缓存数据Controller
 *
 * <AUTHOR>
 * @date 2024-09-03
 */
@RestController
@Api(value = "运营缓存数据控制器", tags = {"运营缓存数据"})
@RequestMapping("/operation/cache")
public class OperationCacheController extends BaseController {

    @Resource
    private OperationCacheService operationCacheService;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    @PreAuthorize("@ss.hasPermi('operation:params:list')")
    @ApiOperation("获取当天最大任务大小列表")
    @GetMapping("/getDayMaxTaskSizeList")
    public TableDataInfo getDayMaxTaskSizeList(
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {

        // 获取分页数据
        List<DayMaxTaskSizeVo> dataList = operationCacheService.getDayMaxTaskSizeList(pageNum, pageSize);

        // 获取总记录数
        Set<String> keys = (Set<String>) redisCachePiclumen.keys(LogicParamsCons.DAY_MAX_TASK_SIZE + "*");
        int total = keys.size();

        return new TableDataInfo(dataList, total);
    }

    @PreAuthorize("@ss.hasPermi('operation:params:list')")
    @ApiOperation("获取所有错误码")
    @GetMapping("/all-error-code")
    public TableDataInfo getAllErrorCodes(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                          @RequestParam(value =  "code" , required = false) String code,
                                          @RequestParam(value =  "description" , required = false) String description,
                                          @RequestParam(value =  "message" , required = false) String message) {

        return operationCacheService.getAllErrorCodes(pageNum,pageSize,code,description,message);
    }

    @PreAuthorize("@ss.hasPermi('operation:params:list')")
    @ApiOperation("获取每日生图数量")
    @GetMapping("/all-model-num")
    public   TableDataInfo getModelNum(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                       @RequestParam(value =  "modelId" , required = false) String modelId,
                                       @RequestParam(value =  "startDate" , required = false)String startDate,
                                       @RequestParam(value =  "endDate" , required = false) String endDate){
        return operationCacheService.getModelNum(pageNum,pageSize, modelId,startDate,endDate);
    }

    @ApiOperation("导出每日生图数量")
    @PreAuthorize("@ss.hasPermi('operation:params:export')")
    @Log(title = "用户图片公开审核", businessType = BusinessType.EXPORT)
    @PostMapping("/model-num-export")
    public void export(HttpServletResponse response,  @RequestParam(value =  "modelId" , required = false) String modelId,
                        @RequestParam(value =  "startDate" , required = false)String startDate,
                        @RequestParam(value =  "endDate" , required = false) String endDate) {
        List<ModelNumVo> allModelNumLis = operationCacheService.getAllModelNumLis(modelId, startDate, endDate);
        ExcelUtil<ModelNumVo> util = new ExcelUtil<ModelNumVo>(ModelNumVo.class);
        util.exportExcel(response, allModelNumLis, "每日生图数量审核数据");
    }
}
