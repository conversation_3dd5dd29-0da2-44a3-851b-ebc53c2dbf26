package com.ai.tracking.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.tracking.domain.KpiBasicData;

/**
 * 基本数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface KpiBasicDataMapper extends BaseMapper<KpiBasicData> {
    /**
     * 查询基本数据
     * 
     * @param id 基本数据主键
     * @return 基本数据
     */
    KpiBasicData selectKpiBasicDataById(Long id);

    /**
     * 查询基本数据列表
     * 
     * @param kpiBasicData 基本数据
     * @return 基本数据集合
     */
    List<KpiBasicData> selectKpiBasicDataList(KpiBasicData kpiBasicData);

    /**
     * 新增基本数据
     * 
     * @param kpiBasicData 基本数据
     * @return 结果
     */
    int insertKpiBasicData(KpiBasicData kpiBasicData);

    /**
     * 修改基本数据
     * 
     * @param kpiBasicData 基本数据
     * @return 结果
     */
    int updateKpiBasicData(KpiBasicData kpiBasicData);

    /**
     * 删除基本数据
     * 
     * @param id 基本数据主键
     * @return 结果
     */
    int deleteKpiBasicDataById(Long id);

    /**
     * 批量删除基本数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteKpiBasicDataByIds(Long[] ids);

    Long updateCreateVipDauUpdate();
}
