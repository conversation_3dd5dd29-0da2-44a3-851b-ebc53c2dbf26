package com.ai.tracking.mapper;

import java.util.List;
import com.ai.tracking.domain.KpiDayAmount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 日金额统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface KpiDayAmountMapper extends BaseMapper<KpiDayAmount> {
    /**
     * 查询日金额统计
     * 
     * @param id 日金额统计主键
     * @return 日金额统计
     */
    public KpiDayAmount selectKpiDayAmountById(Long id);

    /**
     * 查询日金额统计列表
     * 
     * @param kpiDayAmount 日金额统计
     * @return 日金额统计集合
     */
    public List<KpiDayAmount> selectKpiDayAmountList(KpiDayAmount kpiDayAmount);

    /**
     * 新增日金额统计
     * 
     * @param kpiDayAmount 日金额统计
     * @return 结果
     */
    public int insertKpiDayAmount(KpiDayAmount kpiDayAmount);

    /**
     * 修改日金额统计
     * 
     * @param kpiDayAmount 日金额统计
     * @return 结果
     */
    public int updateKpiDayAmount(KpiDayAmount kpiDayAmount);

    /**
     * 删除日金额统计
     * 
     * @param id 日金额统计主键
     * @return 结果
     */
    public int deleteKpiDayAmountById(Long id);

    /**
     * 批量删除日金额统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKpiDayAmountByIds(Long[] ids);
}
