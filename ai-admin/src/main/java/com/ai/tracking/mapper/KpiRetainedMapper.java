package com.ai.tracking.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.tracking.domain.KpiRetained;

/**
 * kpi 数据追踪-留存Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
public interface KpiRetainedMapper extends BaseMapper<KpiRetained> {
    /**
     * 查询kpi 数据追踪-留存
     * 
     * @param id kpi 数据追踪-留存主键
     * @return kpi 数据追踪-留存
     */
    public KpiRetained selectKpiRetainedById(Long id);

    /**
     * 查询kpi 数据追踪-留存列表
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return kpi 数据追踪-留存集合
     */
    public List<KpiRetained> selectKpiRetainedList(KpiRetained kpiRetained);

    /**
     * 新增kpi 数据追踪-留存
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return 结果
     */
    public int insertKpiRetained(KpiRetained kpiRetained);

    /**
     * 修改kpi 数据追踪-留存
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return 结果
     */
    public int updateKpiRetained(KpiRetained kpiRetained);

    /**
     * 删除kpi 数据追踪-留存
     * 
     * @param id kpi 数据追踪-留存主键
     * @return 结果
     */
    public int deleteKpiRetainedById(Long id);

    /**
     * 批量删除kpi 数据追踪-留存
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKpiRetainedByIds(Long[] ids);
}
