package com.ai.tracking.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.tracking.domain.KpiLumenPayCost;

/**
 * lumen使用Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface KpiLumenPayCostMapper extends BaseMapper<KpiLumenPayCost> {
    /**
     * 查询lumen使用
     * 
     * @param id lumen使用主键
     * @return lumen使用
     */
    KpiLumenPayCost selectKpiLumenPayCostById(Long id);

    /**
     * 查询lumen使用列表
     * 
     * @param kpiLumenPayCost lumen使用
     * @return lumen使用集合
     */
    List<KpiLumenPayCost> selectKpiLumenPayCostList(KpiLumenPayCost kpiLumenPayCost);

    /**
     * 新增lumen使用
     * 
     * @param kpiLumenPayCost lumen使用
     * @return 结果
     */
    int insertKpiLumenPayCost(KpiLumenPayCost kpiLumenPayCost);

    /**
     * 修改lumen使用
     * 
     * @param kpiLumenPayCost lumen使用
     * @return 结果
     */
    int updateKpiLumenPayCost(KpiLumenPayCost kpiLumenPayCost);

    /**
     * 删除lumen使用
     * 
     * @param id lumen使用主键
     * @return 结果
     */
    int deleteKpiLumenPayCostById(Long id);

    /**
     * 批量删除lumen使用
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteKpiLumenPayCostByIds(Long[] ids);
}
