package com.ai.tracking.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.tracking.domain.KpiDayAmount;
import com.ai.tracking.service.IKpiDayAmountService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 日金额统计Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@Api(value = "日金额统计控制器", tags = {"日金额统计管理"})
@RequestMapping("/tracking/dayAmount")
public class KpiDayAmountController extends BaseController {
    @Autowired
    private IKpiDayAmountService kpiDayAmountService;

    /**
     * 查询日金额统计列表
     */
    @PreAuthorize("@ss.hasPermi('tracking:dayAmount:list')")
    @ApiOperation("查询日金额统计列表")
    @GetMapping("/list")
    public TableDataInfo list(KpiDayAmount kpiDayAmount) {
        startPage();
        List<KpiDayAmount> list = kpiDayAmountService.selectKpiDayAmountList(kpiDayAmount);
        return getDataTable(list);
    }

    /**
     * 导出日金额统计列表
     */
    @PreAuthorize("@ss.hasPermi('tracking:dayAmount:export')")
    @ApiOperation("导出日金额统计列表")
    @Log(title = "日金额统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiDayAmount kpiDayAmount) {
        List<KpiDayAmount> list = kpiDayAmountService.selectKpiDayAmountList(kpiDayAmount);
        ExcelUtil<KpiDayAmount> util = new ExcelUtil<KpiDayAmount>(KpiDayAmount.class);
        util.exportExcel(response, list, "日金额统计数据");
    }

    /**
     * 获取日金额统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('tracking:dayAmount:query')")
    @ApiOperation("获取日金额统计详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kpiDayAmountService.selectKpiDayAmountById(id));
    }

    /**
     * 新增日金额统计
     */
    @PreAuthorize("@ss.hasPermi('tracking:dayAmount:add')")
    @ApiOperation("新增日金额统计")
    @Log(title = "日金额统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiDayAmount kpiDayAmount) {
        return toAjax(kpiDayAmountService.insertKpiDayAmount(kpiDayAmount));
    }

    /**
     * 修改日金额统计
     */
    @PreAuthorize("@ss.hasPermi('tracking:dayAmount:edit')")
    @ApiOperation("修改日金额统计")
    @Log(title = "日金额统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiDayAmount kpiDayAmount) {
        return toAjax(kpiDayAmountService.updateKpiDayAmount(kpiDayAmount));
    }

    /**
     * 删除日金额统计
     */
    @PreAuthorize("@ss.hasPermi('tracking:dayAmount:remove')")
    @ApiOperation("删除日金额统计")
    @Log(title = "日金额统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(kpiDayAmountService.deleteKpiDayAmountByIds(ids));
    }
}
