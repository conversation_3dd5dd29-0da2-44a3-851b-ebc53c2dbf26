package com.ai.tracking.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.tracking.domain.KpiRetained;
import com.ai.tracking.service.IKpiRetainedService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * kpi 数据追踪-留存Controller
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@RestController
@Api(value = "kpi 数据追踪-留存控制器", tags = {"kpi 数据追踪-留存管理"})
@RequestMapping("/tracking/retained")
public class KpiRetainedController extends BaseController {
    @Autowired
    private IKpiRetainedService kpiRetainedService;

    /**
     * 查询kpi 数据追踪-留存列表
     */
    @PreAuthorize("@ss.hasPermi('tracking:retained:list')")
    @ApiOperation("查询kpi 数据追踪-留存列表")
    @GetMapping("/list")
    public TableDataInfo list(KpiRetained kpiRetained) {
        startPage();
        List<KpiRetained> list = kpiRetainedService.selectKpiRetainedList(kpiRetained);
        return getDataTable(list);
    }

    /**
     * 导出kpi 数据追踪-留存列表
     */
    @ApiOperation("导出kpi 数据追踪-留存列表")
    @PreAuthorize("@ss.hasPermi('tracking:retained:export')")
    @Log(title = "kpi 数据追踪-留存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiRetained kpiRetained) {
        List<KpiRetained> list = kpiRetainedService.selectKpiRetainedList(kpiRetained);
        ExcelUtil<KpiRetained> util = new ExcelUtil<KpiRetained>(KpiRetained.class);
        util.exportExcel(response, list, "kpi 数据追踪-留存数据");
    }

    /**
     * 获取kpi 数据追踪-留存详细信息
     */
    @ApiOperation("获取kpi 数据追踪-留存详细信息")
    @PreAuthorize("@ss.hasPermi('tracking:retained:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kpiRetainedService.selectKpiRetainedById(id));
    }

    /**
     * 新增kpi 数据追踪-留存
     */
    @ApiOperation("新增kpi 数据追踪-留存")
    @PreAuthorize("@ss.hasPermi('tracking:retained:add')")
    @Log(title = "kpi 数据追踪-留存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiRetained kpiRetained) {
        return toAjax(kpiRetainedService.insertKpiRetained(kpiRetained));
    }

    /**
     * 修改kpi 数据追踪-留存
     */
    @ApiOperation("修改kpi 数据追踪-留存")
    @PreAuthorize("@ss.hasPermi('tracking:retained:edit')")
    @Log(title = "kpi 数据追踪-留存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiRetained kpiRetained) {
        return toAjax(kpiRetainedService.updateKpiRetained(kpiRetained));
    }

    /**
     * 删除kpi 数据追踪-留存
     */
    @ApiOperation("删除kpi 数据追踪-留存")
    @PreAuthorize("@ss.hasPermi('tracking:retained:remove')")
    @Log(title = "kpi 数据追踪-留存", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(kpiRetainedService.deleteKpiRetainedByIds(ids));
    }
}
