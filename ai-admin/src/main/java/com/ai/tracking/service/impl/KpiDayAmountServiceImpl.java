package com.ai.tracking.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.tracking.mapper.KpiDayAmountMapper;
import com.ai.tracking.domain.KpiDayAmount;
import com.ai.tracking.service.IKpiDayAmountService;

/**
 * 日金额统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class KpiDayAmountServiceImpl extends ServiceImpl<KpiDayAmountMapper, KpiDayAmount> implements IKpiDayAmountService {
    @Autowired
    private KpiDayAmountMapper kpiDayAmountMapper;

    /**
     * 查询日金额统计
     * 
     * @param id 日金额统计主键
     * @return 日金额统计
     */
    @Override
    public KpiDayAmount selectKpiDayAmountById(Long id) {
        return kpiDayAmountMapper.selectKpiDayAmountById(id);
    }

    /**
     * 查询日金额统计列表
     * 
     * @param kpiDayAmount 日金额统计
     * @return 日金额统计
     */
    @Override
    public List<KpiDayAmount> selectKpiDayAmountList(KpiDayAmount kpiDayAmount) {
        return kpiDayAmountMapper.selectKpiDayAmountList(kpiDayAmount);
    }

    /**
     * 新增日金额统计
     * 
     * @param kpiDayAmount 日金额统计
     * @return 结果
     */
    @Override
    public int insertKpiDayAmount(KpiDayAmount kpiDayAmount) {
        kpiDayAmount.setCreateTime(LocalDateTime.now());
        return kpiDayAmountMapper.insertKpiDayAmount(kpiDayAmount);
    }

    /**
     * 修改日金额统计
     * 
     * @param kpiDayAmount 日金额统计
     * @return 结果
     */
    @Override
    public int updateKpiDayAmount(KpiDayAmount kpiDayAmount) {
        kpiDayAmount.setUpdateTime(LocalDateTime.now());
        return kpiDayAmountMapper.updateKpiDayAmount(kpiDayAmount);
    }

    /**
     * 批量删除日金额统计
     * 
     * @param ids 需要删除的日金额统计主键
     * @return 结果
     */
    @Override
    public int deleteKpiDayAmountByIds(Long[] ids) {
        return kpiDayAmountMapper.deleteKpiDayAmountByIds(ids);
    }

    /**
     * 删除日金额统计信息
     * 
     * @param id 日金额统计主键
     * @return 结果
     */
    @Override
    public int deleteKpiDayAmountById(Long id) {
        return kpiDayAmountMapper.deleteKpiDayAmountById(id);
    }
}
