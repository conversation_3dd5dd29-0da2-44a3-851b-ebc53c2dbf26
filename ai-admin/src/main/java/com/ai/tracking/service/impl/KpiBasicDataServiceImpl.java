package com.ai.tracking.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.tracking.mapper.KpiBasicDataMapper;
import com.ai.tracking.domain.KpiBasicData;
import com.ai.tracking.service.IKpiBasicDataService;

/**
 * 基本数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class KpiBasicDataServiceImpl extends ServiceImpl<KpiBasicDataMapper, KpiBasicData> implements IKpiBasicDataService
{
    @Autowired
    private KpiBasicDataMapper kpiBasicDataMapper;

    /**
     * 查询基本数据
     * 
     * @param id 基本数据主键
     * @return 基本数据
     */
    @Override
    public KpiBasicData selectKpiBasicDataById(Long id)
    {
        return kpiBasicDataMapper.selectKpiBasicDataById(id);
    }

    /**
     * 查询基本数据列表
     * 
     * @param kpiBasicData 基本数据
     * @return 基本数据
     */
    @Override
    public List<KpiBasicData> selectKpiBasicDataList(KpiBasicData kpiBasicData)
    {
        return kpiBasicDataMapper.selectKpiBasicDataList(kpiBasicData);
    }

    /**
     * 新增基本数据
     * 
     * @param kpiBasicData 基本数据
     * @return 结果
     */
    @Override
    public int insertKpiBasicData(KpiBasicData kpiBasicData)
    {
        kpiBasicData.setCreateTime(LocalDateTime.now());
        return kpiBasicDataMapper.insertKpiBasicData(kpiBasicData);
    }

    /**
     * 修改基本数据
     * 
     * @param kpiBasicData 基本数据
     * @return 结果
     */
    @Override
    public int updateKpiBasicData(KpiBasicData kpiBasicData)
    {
        kpiBasicData.setUpdateTime(LocalDateTime.now());
        return kpiBasicDataMapper.updateById(kpiBasicData);
    }

    /**
     * 批量删除基本数据
     * 
     * @param ids 需要删除的基本数据主键
     * @return 结果
     */
    @Override
    public int deleteKpiBasicDataByIds(Long[] ids)
    {
        return kpiBasicDataMapper.deleteKpiBasicDataByIds(ids);
    }

    /**
     * 删除基本数据信息
     * 
     * @param id 基本数据主键
     * @return 结果
     */
    @Override
    public int deleteKpiBasicDataById(Long id)
    {
        return kpiBasicDataMapper.deleteKpiBasicDataById(id);
    }
}
