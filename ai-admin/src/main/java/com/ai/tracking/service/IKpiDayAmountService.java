package com.ai.tracking.service;

import java.util.List;
import com.ai.tracking.domain.KpiDayAmount;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 日金额统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IKpiDayAmountService extends IService<KpiDayAmount> {
    /**
     * 查询日金额统计
     * 
     * @param id 日金额统计主键
     * @return 日金额统计
     */
    KpiDayAmount selectKpiDayAmountById(Long id);

    /**
     * 查询日金额统计列表
     * 
     * @param kpiDayAmount 日金额统计
     * @return 日金额统计集合
     */
    List<KpiDayAmount> selectKpiDayAmountList(KpiDayAmount kpiDayAmount);

    /**
     * 新增日金额统计
     * 
     * @param kpiDayAmount 日金额统计
     * @return 结果
     */
    int insertKpiDayAmount(KpiDayAmount kpiDayAmount);

    /**
     * 修改日金额统计
     * 
     * @param kpiDayAmount 日金额统计
     * @return 结果
     */
    int updateKpiDayAmount(KpiDayAmount kpiDayAmount);

    /**
     * 批量删除日金额统计
     * 
     * @param ids 需要删除的日金额统计主键集合
     * @return 结果
     */
    int deleteKpiDayAmountByIds(Long[] ids);

    /**
     * 删除日金额统计信息
     * 
     * @param id 日金额统计主键
     * @return 结果
     */
    int deleteKpiDayAmountById(Long id);
}
