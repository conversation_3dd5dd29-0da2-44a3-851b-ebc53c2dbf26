package com.ai.tracking.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.tracking.mapper.KpiRetainedMapper;
import com.ai.tracking.domain.KpiRetained;
import com.ai.tracking.service.IKpiRetainedService;

/**
 * kpi 数据追踪-留存Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@Service
public class KpiRetainedServiceImpl extends ServiceImpl<KpiRetainedMapper, KpiRetained> implements IKpiRetainedService
{
    @Autowired
    private KpiRetainedMapper kpiRetainedMapper;

    /**
     * 查询kpi 数据追踪-留存
     * 
     * @param id kpi 数据追踪-留存主键
     * @return kpi 数据追踪-留存
     */
    @Override
    public KpiRetained selectKpiRetainedById(Long id)
    {
        return kpiRetainedMapper.selectKpiRetainedById(id);
    }

    /**
     * 查询kpi 数据追踪-留存列表
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return kpi 数据追踪-留存
     */
    @Override
    public List<KpiRetained> selectKpiRetainedList(KpiRetained kpiRetained)
    {
        return kpiRetainedMapper.selectKpiRetainedList(kpiRetained);
    }

    /**
     * 新增kpi 数据追踪-留存
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return 结果
     */
    @Override
    public int insertKpiRetained(KpiRetained kpiRetained)
    {
        kpiRetained.setCreateTime(LocalDateTime.now());
        return kpiRetainedMapper.insertKpiRetained(kpiRetained);
    }

    /**
     * 修改kpi 数据追踪-留存
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return 结果
     */
    @Override
    public int updateKpiRetained(KpiRetained kpiRetained)
    {
        kpiRetained.setUpdateTime(LocalDateTime.now());
        return kpiRetainedMapper.updateKpiRetained(kpiRetained);
    }

    /**
     * 批量删除kpi 数据追踪-留存
     * 
     * @param ids 需要删除的kpi 数据追踪-留存主键
     * @return 结果
     */
    @Override
    public int deleteKpiRetainedByIds(Long[] ids)
    {
        return kpiRetainedMapper.deleteKpiRetainedByIds(ids);
    }

    /**
     * 删除kpi 数据追踪-留存信息
     * 
     * @param id kpi 数据追踪-留存主键
     * @return 结果
     */
    @Override
    public int deleteKpiRetainedById(Long id)
    {
        return kpiRetainedMapper.deleteKpiRetainedById(id);
    }
}
