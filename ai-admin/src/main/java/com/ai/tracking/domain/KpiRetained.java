package com.ai.tracking.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;
import org.hibernate.validator.internal.util.privilegedactions.LoadClass;

/**
 * kpi 数据追踪-留存对象 kpi_retained
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "kpi_retained", description = "kpi 数据追踪-留存")
@TableName("kpi_retained")
public class KpiRetained extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 日期 */
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate date;

    /** 首次访问量 */
    @ApiModelProperty("首次访问量")
    @Excel(name = "首次访问量")
    private Long firstView;

    /** 次日留存 */
    @ApiModelProperty("次日留存")
    @Excel(name = "次日留存")
    private Long morrow;

    /** 次日留存占比 */
    @ApiModelProperty("次日留存占比")
    @Excel(name = "次日留存占比")
    private Double morrowRate;

    /** 2-7日留存 */
    @ApiModelProperty("2-7日留存")
    @Excel(name = "2-7日留存")
    private Long twoToSeven;

    /** 2-7日留存占比 */
    @ApiModelProperty("2-7日留存占比")
    @Excel(name = "2-7日留存占比")
    private Double twoToSevenRate;

    /** 8-30日留存 */
    @ApiModelProperty("8-30日留存")
    @Excel(name = "8-30日留存")
    private Long eightToThirty;

    /** 8-30日留存占比 */
    @ApiModelProperty("8-30日留存占比")
    @Excel(name = "8-30日留存占比")
    private Double eightToThirtyRate;

    /** 平台(web|ios|android) */
    @ApiModelProperty("平台(web|ios|android)")
    @Excel(name = "平台(web|ios|android)")
    private String platform;
}
