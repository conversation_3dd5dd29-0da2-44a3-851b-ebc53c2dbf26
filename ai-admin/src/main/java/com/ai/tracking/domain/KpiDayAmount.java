package com.ai.tracking.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 日金额统计对象 kpi_day_amount
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "kpi_day_amount", description = "日金额统计")
@TableName("kpi_day_amount")
public class KpiDayAmount extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 统计日期 */
    @ApiModelProperty("统计日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate countDate;

    /** 国家 */
    @ApiModelProperty("国家")
    @Excel(name = "国家")
    private String country;

    /** 源货币 */
    @ApiModelProperty("源货币")
    @Excel(name = "源货币")
    private String srcCurrency;

    /** 目标货币 */
    @ApiModelProperty("目标货币")
    @Excel(name = "目标货币")
    private String currency;

    /** 数量 */
    @ApiModelProperty("数量")
    @Excel(name = "数量")
    private Integer number;

    /** 金额 */
    @ApiModelProperty("金额")
    @Excel(name = "金额")
    private BigDecimal amount;
}
