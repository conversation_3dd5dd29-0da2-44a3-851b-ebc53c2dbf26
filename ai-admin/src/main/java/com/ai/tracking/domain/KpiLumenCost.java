package com.ai.tracking.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

import java.time.LocalDate;

/**
 * kpi 数据追踪-lumen消耗对象 kpi_lumen_cost
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "kpi_lumen_cost", description = "kpi 数据追踪-lumen消耗")
@TableName("kpi_lumen_cost")
public class KpiLumenCost extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 日期 */
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate date;

    /** 生效会员总数 */
    @ApiModelProperty("生效购买会员总数")
    @Excel(name = "生效购买会员总数")
    private Long totalVipNum;

    /** 会员Lumen总量 */
    @ApiModelProperty("会员Lumen总量")
    @Excel(name = "会员Lumen总量")
    private Long totalVipLumenNum;

    /** 会员Lumen消耗总量 */
    @ApiModelProperty("会员Lumen消耗总量")
    @Excel(name = "会员Lumen消耗总量")
    private Long totalVipCostLumenNum;

    /** 生效购买lumen人数 */
    @ApiModelProperty("生效购买lumen人数")
    @Excel(name = "生效购买lumen人数")
    private Long totalRechargeNum;

    /** 购买Lumen总量 */
    @ApiModelProperty("购买Lumen总量")
    @Excel(name = "购买Lumen总量")
    private Long totalRechargeLumenNum;

    /** 会员购买消耗总量 */
    @ApiModelProperty("会员购买消耗总量")
    @Excel(name = "会员购买消耗总量")
    private Long totalRechargeCostLumenNum;

    /** 生效赠送lumen人数 */
    @ApiModelProperty("生效赠送lumen人数")
    @Excel(name = "生效赠送lumen人数")
    private Long totalGiftNum;

    /** 赠送Lumen总量 */
    @ApiModelProperty("赠送Lumen总量")
    @Excel(name = "赠送Lumen总量")
    private Long totalGiftLumenNum;

    /** 赠送Lumen消耗总量 */
    @ApiModelProperty("赠送Lumen消耗总量")
    @Excel(name = "赠送Lumen消耗总量")
    private Long totalGiftCostLumenNum;

    /** 平台(stripe|ios|android|gift) */
    @ApiModelProperty("平台(stripe|ios|android|gift|汇总)")
    @Excel(name = "平台(stripe|ios|android|gift)")
    private String platform;

}
