package com.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 语言代码枚举
 */
@Getter
@AllArgsConstructor
public enum LanguageCodeEnum {

    /**
     * 英语
     */
    ENGLISH("en", "英语"),

    /**
     * 德语
     */
    GERMAN("de_DE", "德语"),

    /**
     * 日语
     */
    JAPANESE("ja_JP", "日语"),

    /**
     * 西班牙语
     */
    SPANISH("es", "西班牙语"),

    /**
     * 葡萄牙语
     */
    PORTUGUESE("pt_BR", "葡萄牙语"),

    /**
     *
     */
    FRENCH("fr_FR", "法语"),

    /**
     * 韩语
     */
    KOREAN("ko", "韩语"),

    /**
     * 意大利语
     */
    ITALIAN("it", "意大利语"),

    /**
     * 繁体中文
     */
    TRADITIONAL_CHINESE("zh-TW", "繁体中文"),
    ;

    private final String code;
    private final String name;

    /**
     * 根据语言代码获取枚举值
     *
     * @param code 语言代码
     * @return 对应的枚举值，如果未找到则返回英语
     */
    public static LanguageCodeEnum getByCode(String code) {
        if (code != null) {
            for (LanguageCodeEnum value : values()) {
                if (value.getCode().equalsIgnoreCase(code)) {
                    return value;
                }
            }
        }
        return ENGLISH;
    }

    /**
     * 检查语言代码是否有效
     *
     * @param code 语言代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        if (code == null) {
            return false;
        }
        for (LanguageCodeEnum value : values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return true;
            }
        }
        return false;
    }
}
