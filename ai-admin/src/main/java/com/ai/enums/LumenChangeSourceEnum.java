package com.ai.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description lumen变化来源枚举
 */
@Getter
public enum LumenChangeSourceEnum {
    CREATE("Create", "生图"),
    REWARDS("Rewards", "任务奖励"),
    RESEARCH("Research", "平台调研领取"),
    CHALLENGE("Challenge", "活动奖励发放"),
    PLATFORM("Platform", "后台发放"),
    TOOLS("Tools", "工具使用"),
    PURCHASE("Purchase", "购买"),
    SUBSCRIBE("Subscribe", "每月会员赠送"),
    ;

    private final String value;
    private final String description;

    LumenChangeSourceEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
