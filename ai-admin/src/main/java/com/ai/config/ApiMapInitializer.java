package com.ai.config;

import com.ai.util.ApiMapHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
public class ApiMapInitializer implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        ClassPathResource resource = new ClassPathResource("api.txt");

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.contains(":\"")) continue;

                // 拆分描述和路径
                String[] parts = line.split(":\"");
                if (parts.length != 2) continue;

                String description = parts[0].trim();
                String path = parts[1].replace("\"", "").trim();

                String fullUrl =  path;
                ApiMapHolder.put(fullUrl, description);
            }
        }
    }
}
