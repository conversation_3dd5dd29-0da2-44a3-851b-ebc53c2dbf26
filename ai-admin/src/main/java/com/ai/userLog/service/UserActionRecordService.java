package com.ai.userLog.service;

import com.ai.common.constant.HttpStatus;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.utils.DateUtils;
import com.ai.userLog.domain.mongodb.UserActionRecord;
import com.ai.util.ApiMapHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.*;

@Service
@Slf4j
public class UserActionRecordService {

    @Autowired
    private MongoTemplate mongoTemplate;

    public TableDataInfo getUserActionRecordList( Integer pageSize,
                                                String markFileId, Boolean isNext,
                                                String startTime,
                                                String endTime,
                                                String platform,
                                                String ipCountry,
                                                String loginName) {

        List<UserActionRecord> userActionList = getUserActionList(pageSize, markFileId, isNext, startTime, endTime, platform, ipCountry, loginName);
        // 封装响应数据
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(userActionList);
        rspData.setTotal(10);
        return rspData;
    }

    public List<UserActionRecord> getUserActionList( Integer pageSize,
                                                           String markFileId, Boolean isNext,
                                                           String startTime,
                                                           String endTime,
                                                           String platform,
                                                           String ipCountry,
                                                           String loginName) {
        Boolean allowExtraFilters = false;

        // 校验页码和每页记录数的有效性
        if (pageSize == null || pageSize < 1) {
            throw new IllegalArgumentException("Page and pageSize must be greater than 0");
        }

        // 创建时间过滤条件
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(loginName)) {
            criteria = criteria.and("userLoginName").is(loginName);
            allowExtraFilters = true;
        }

        if (StringUtils.isNotBlank(startTime)&&StringUtils.isNotBlank(endTime)) {
            criteria = criteria.and("createTime").gt(DateUtils.parseDate(startTime)).lt(DateUtils.parseDate(endTime));
            allowExtraFilters = true;
        }

        // createTime 和 userLoginName 有索引 必须有 这个查询条件的其中一个 才能查询其他查询条件
        if (!allowExtraFilters && (StringUtils.isNotBlank(platform) || StringUtils.isNotBlank(ipCountry))) {
            throw new IllegalArgumentException("查询 platform 或 ipCountry 时，必须提供 loginName 或时间范围");
        }

        if (StringUtils.isNotBlank(platform)) {
            criteria = criteria.and("platform").is(platform);
        }

        if (StringUtils.isNotBlank(ipCountry)) {
            criteria = criteria.and("ipCountry").regex(ipCountry);
        }

        // 创建分页查询
        Query query = new Query(criteria);
        if (StringUtils.isNotBlank(markFileId)) {
            if (isNext) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.DESC, "id"));
            } else {
                query.addCriteria(Criteria.where("id").gt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.ASC, "id"));
            }
        } else {
            query.with(Sort.by(Sort.Direction.DESC, "id"));
        }

        query.limit(pageSize);

        // 查询符合条件的数据
        List<UserActionRecord> commFiles = mongoTemplate.find(query, UserActionRecord.class);

        if (StringUtils.isNotBlank(markFileId) && !isNext) {
            Collections.reverse(commFiles);
        }

        //  接口操作描述
        commFiles.forEach(file -> {
            file.setRequestName(ApiMapHolder.getDescription( getPathFromUrl(file.getRequestUrl())));
        });
        return commFiles;

    }

    public static String getPathFromUrl(String urlStr) {
        try {
            URL url = new URL(urlStr);
            return url.getPath();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
