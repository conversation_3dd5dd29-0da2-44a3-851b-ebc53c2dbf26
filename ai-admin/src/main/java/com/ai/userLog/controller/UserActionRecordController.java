package com.ai.userLog.controller;

import com.ai.common.annotation.Log;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.enums.BusinessType;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.tracking.domain.KpiBasicData;
import com.ai.userLog.domain.mongodb.UserActionRecord;
import com.ai.userLog.service.UserActionRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户行为日志Controller
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@RestController
@Api(value = "用户行为日志控制器", tags = {"用户行为日志管理"})
@RequestMapping("/userLog/actionLog")
public class UserActionRecordController {

    @Autowired
    private UserActionRecordService userActionRecordService;

    @PreAuthorize("@ss.hasPermi('userLog:actionLog:list')")
    @ApiOperation("查看用户行为日志列表")
    @GetMapping("/list")
    public TableDataInfo getUserActionRecordList(@RequestParam(value =  "pageSize" ) Integer pageSize,
                                                 @RequestParam(value =  "markFileId" , required = false) String markFileId,
                                                 @RequestParam(value =  "startTime" , required = false) String startTime,
                                                 @RequestParam(value =  "endTime" , required = false) String endTime,
                                                 @RequestParam(value =  "platform" , required = false) String platform,
                                                 @RequestParam(value =  "ipCountry" , required = false) String ipCountry,
                                                 @RequestParam(value =  "isNext" , required = false) Boolean isNext,
                                                 @RequestParam(value =  "loginName" , required = false) String loginName) {
        return userActionRecordService.getUserActionRecordList(pageSize,markFileId, isNext,startTime,endTime,platform,ipCountry,loginName);
    }

    /**
     * 导出用户行为日志
     */
    @ApiOperation("导出用户行为日志")
    @Log(title = "用户行为日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,@RequestParam(value =  "markFileId" , required = false) String markFileId,
                       @RequestParam(value =  "startTime" , required = false) String startTime,
                       @RequestParam(value =  "endTime" , required = false) String endTime,
                       @RequestParam(value =  "platform" , required = false) String platform,
                       @RequestParam(value =  "ipCountry" , required = false) String ipCountry,
                       @RequestParam(value =  "isNext" , required = false) Boolean isNext,
                       @RequestParam(value =  "loginName" , required = false) String loginName) {
        List<UserActionRecord> list = userActionRecordService.getUserActionList(80000, markFileId, isNext, startTime, endTime, platform, ipCountry, loginName);
        ExcelUtil<UserActionRecord> util = new ExcelUtil<UserActionRecord>(UserActionRecord.class);
        util.exportExcel(response, list, "基本数据数据");
    }

}
