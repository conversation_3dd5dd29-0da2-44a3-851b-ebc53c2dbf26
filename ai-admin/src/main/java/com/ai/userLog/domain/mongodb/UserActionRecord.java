package com.ai.userLog.domain.mongodb;

import com.ai.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * @description 用户行为记录
 */
@Data
@Document(collection = "user_action_record")
public class UserActionRecord {

    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 登录账号
     */
    @Excel(name = "登录账号")
    private String userLoginName;

    /**
     * 行为来源平台
     */
    @Excel(name = "行为来源平台")
    private String platform;

    /**
     * 请求路径
     */
    private String requestUrl;

    /**
     * 请求内容
     */
    private String requestContent;

    /**
     * 功能类型
     */
    @Excel(name = "功能类型")
    private String featureName;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 请求ip
     */
    private String requestIp;

    /**
     * ip所属国家
     */
    @Excel(name = "ip所属国家")
    private String ipCountry;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 请求描述
     */
    @Transient
    @Excel(name = "请求描述")
    private String requestName;
}
