package com.ai.util;

import java.util.HashMap;
import java.util.Map;

public class ApiMapHolder {
    private static final Map<String, String> API_MAP = new HashMap<>();

    public static void put(String url, String description) {
        API_MAP.put(url, description);
    }

    public static String getDescription(String url) {
        return API_MAP.get(url);
    }

    public static Map<String, String> getAll() {
        return API_MAP;
    }
}
