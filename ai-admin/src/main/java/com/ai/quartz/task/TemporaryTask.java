//package com.ai.quartz.task;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.bean.copier.CopyOptions;
//import cn.hutool.poi.excel.ExcelUtil;
//import cn.hutool.poi.excel.ExcelWriter;
//import com.ai.admin.domain.GptUser;
//import com.ai.admin.domain.dto.AppleLumenCompensationDTO;
//import com.ai.admin.mapper.GptUserMapper;
//import com.ai.common.utils.DateUtils;
//import com.ai.quartz.task.activity.UserActivity;
//import com.ai.quartz.task.activity.UserActivityRepository;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.*;
//
///**
// * <AUTHOR>
// * @date 2025/6/19
// * @description
// */
//@Slf4j
//@Service
//public class TemporaryTask {
//    @Resource
//    private UserActivityRepository userActivityRepository;
//    @Resource
//    private GptUserMapper gptUserMapper;
//
//    public void lumenCompensation() {
//        log.info("开始执行苹果用户lumen补偿");
//        long startTime = System.currentTimeMillis();
//        int sendCount = 0;
//        Set<String> sendUserList = new HashSet<>();
//        List<AppleLumenCompensationDTO> sendEmailList = new ArrayList<>();
//        //当日活跃用户
//        Date beijingDate = new Date();
//        String startOfDay = DateUtils.formatDateByPattern(beijingDate, DateUtils.YYYY_MM_DD_00_00_00);
//        String endOfDay = DateUtils.formatDateByPattern(beijingDate, DateUtils.YYYY_MM_DD_23_59_59);
//        List<UserActivity> todayUserActivityList = userActivityRepository.findByDateBetweenInclusive(DateUtils.stringToLocalDate(startOfDay), DateUtils.stringToLocalDate(endOfDay));
//        if (CollectionUtils.isNotEmpty(todayUserActivityList)) {
//            todayUserActivityList.forEach(userActivity -> sendUserList.add(userActivity.getUserId()));
//        }
//
//        //昨日活跃用户
//        String theDayMidnight = DateUtils.formatDateByPattern(beijingDate, DateUtils.YYYY_MM_DD_00_00_00);
//        String yesterdayMidnight = DateUtils.formatDateByPattern(new Date(beijingDate.getTime() - 1000 * 60 * 60 * 24), DateUtils.YYYY_MM_DD_00_00_00);
//        List<UserActivity> yesterdayUserActivityList = userActivityRepository.findByDateBetweenInclusive(DateUtils.stringToLocalDate(yesterdayMidnight), DateUtils.stringToLocalDate(theDayMidnight));
//        if (CollectionUtils.isNotEmpty(yesterdayUserActivityList)) {
//            yesterdayUserActivityList.forEach(userActivity -> sendUserList.add(userActivity.getUserId()));
//        }
//
//        if (!sendUserList.isEmpty()) {
//            List<GptUser> updateList = new ArrayList<>();
//            //执行lumen发送
//            for (String sendUser : sendUserList) {
//                try {
//                    if (StringUtils.isBlank(sendUser)) {
//                        continue;
//                    }
//                    GptUser user = gptUserMapper.selectOne(new LambdaQueryWrapper<>(GptUser.class)
//                            .select(GptUser::getId, GptUser::getLoginName, GptUser::getEmail, GptUser::getAppleId)
//                            .eq(GptUser::getId, sendUser));
//                    if (StringUtils.isBlank(user.getAppleId())) {
//                        continue;
//                    }
//                    GptUser updateUser = new GptUser();
//                    updateUser.setId(user.getId());
//                    updateList.add(updateUser);
//                    sendEmailList.add(new AppleLumenCompensationDTO(user.getId() + "", user.getLoginName(), user.getEmail(), user.getAppleId()));
//                    if (updateList.size() >= 100) {
//                        gptUserMapper.batchUpdateUserSystemRewardLumen(updateList);
//                        updateList.clear();
//                    }
//                    sendCount++;
//                } catch (Exception e) {
//                    log.error("用户lumen补偿发送异常, userId: {}", sendUser, e);
//                }
//            }
//
//            if (CollectionUtils.isNotEmpty(updateList)) {
//                gptUserMapper.batchUpdateUserSystemRewardLumen(updateList);
//            }
//            exportToExcelIgnoreNull(sendEmailList);
//            log.info("苹果用户lumen补偿执行完毕, sendCount: {}, 耗时: {} ms", sendCount, System.currentTimeMillis() - startTime);
//        }
//    }
//
//    public static void exportToExcelIgnoreNull(List<?> dataList) {
//        log.info("执行补偿用户信息导出");
//        try (ExcelWriter writer = ExcelUtil.getWriter("/home/<USER>/piclumen/apple_lumen_compensation_20250619.xlsx")) {
//            writer.setOnlyAlias(true);
//            List<Map<String, Object>> rows = new ArrayList<>();
//            for (Object item : dataList) {
//                Map<String, Object> row = BeanUtil.beanToMap(
//                        item,
//                        new LinkedHashMap<>(),
//                        CopyOptions.create().setIgnoreNullValue(false)
//                );
//                rows.add(row);
//            }
//
//            writer.write(rows, true);
//        } catch (Exception e) {
//            log.error("导出补偿用户信息失败, ", e);
//        }
//    }
//}
