package com.ai.quartz.task;

import com.ai.admin.domain.dto.MidjourneyResponse;
import com.ai.admin.service.DingTalkAlert;
import com.ai.demo.feign.MidjourneyApiClient;
import com.ai.enums.AlarmEnum;
import com.ai.util.LogicUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/19
 * @description
 */
@Slf4j
@Service
public class MidjourneyTask {
    @Resource
    private MidjourneyApiClient midjourneyApiClient;
    @Resource
    private DingTalkAlert dingTalkAlert;
    @Value("${midjourney.api.api-key}")
    private String midjourneyApiKey;
    @Value("${midjourney.api.balance-alarm-threshold:10000}")
    private Integer balanceAlarmThreshold;

    public void checkMidjourneyBalance() {
        try {
            MidjourneyResponse midjourneyResponse = midjourneyApiClient.info(midjourneyApiKey);
            log.info("Midjourney info API call result, midjourneyResponse: {}", midjourneyResponse);
            if (midjourneyResponse == null || midjourneyResponse.getData() == null) {
                log.error("Midjourney info API call result error, midjourneyResponse: {}", JSON.toJSONString(midjourneyResponse));
                return;
            }

            MidjourneyResponse.InfoResponse infoResponse = JSON.parseObject(midjourneyResponse.getData().toString(), MidjourneyResponse.InfoResponse.class);
            if (infoResponse.getBalance().compareTo(new BigDecimal(balanceAlarmThreshold)) < 0) {
                //余额不足，告警
                dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                        AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                        AlarmEnum.AlarmSourceEnum.MIDJOURNEY_BALANCE.getDescription(),
                        "Midjourney余额不足，当前余额：" + infoResponse.getBalance() + "，告警阈值：" + balanceAlarmThreshold,
                        null,
                        null)
                );
            }
        } catch (Exception e) {
            log.error("Check midjourney balance error", e);
        }
    }
}
