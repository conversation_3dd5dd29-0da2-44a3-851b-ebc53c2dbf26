package com.ai.notice.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.notice.domain.GptPersonalNotice;

/**
 * 个人通知发布Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface GptPersonalNoticeMapper extends BaseMapper<GptPersonalNotice> {
    /**
     * 查询个人通知发布
     * 
     * @param id 个人通知发布主键
     * @return 个人通知发布
     */
    GptPersonalNotice selectGptPersonalNoticeById(Long id);

    /**
     * 查询个人通知发布列表
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 个人通知发布集合
     */
    List<GptPersonalNotice> selectGptPersonalNoticeList(GptPersonalNotice gptPersonalNotice);

    /**
     * 新增个人通知发布
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 结果
     */
    int insertGptPersonalNotice(GptPersonalNotice gptPersonalNotice);

    /**
     * 修改个人通知发布
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 结果
     */
    int updateGptPersonalNotice(GptPersonalNotice gptPersonalNotice);

    /**
     * 删除个人通知发布
     * 
     * @param id 个人通知发布主键
     * @return 结果
     */
    int deleteGptPersonalNoticeById(Long id);

    /**
     * 批量删除个人通知发布
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteGptPersonalNoticeByIds(Long[] ids);
}
