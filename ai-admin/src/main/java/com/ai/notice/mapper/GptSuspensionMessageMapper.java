package com.ai.notice.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.notice.domain.GptSuspensionMessage;

/**
 * 停服公告Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface GptSuspensionMessageMapper extends BaseMapper<GptSuspensionMessage> {
    /**
     * 查询停服公
     * 
     * @param id 停服公告主键
     * @return 停服公告
     */
    public GptSuspensionMessage selectGptSuspensionMessageById(Long id);

    /**
     * 查询停服公告列表
     * 
     * @param gptSuspensionMessage 停服公告
     * @return 停服公告集合
     */
    public List<GptSuspensionMessage> selectGptSuspensionMessageList(GptSuspensionMessage gptSuspensionMessage);

    /**
     * 新增停服公告
     * 
     * @param gptSuspensionMessage 停服公告
     * @return 结果
     */
    public int insertGptSuspensionMessage(GptSuspensionMessage gptSuspensionMessage);

    /**
     * 修改停服公告
     * 
     * @param gptSuspensionMessage 停服公告
     * @return 结果
     */
    public int updateGptSuspensionMessage(GptSuspensionMessage gptSuspensionMessage);

    /**
     * 删除停服公告
     * 
     * @param id 停服公告主键
     * @return 结果
     */
    public int deleteGptSuspensionMessageById(Long id);

    /**
     * 批量删除停服公告
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptSuspensionMessageByIds(Long[] ids);
}
