package com.ai.notice.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

import java.time.LocalDateTime;

/**
 * 个人通知发布对象 gpt_personal_notice
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_personal_notice", description = "个人通知发布")
@TableName("gpt_personal_notice")
public class GptPersonalNotice extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 标题信息 */
    @ApiModelProperty("标题信息")
    @Excel(name = "标题信息")
    private String title;

    /** 简介 */
    @ApiModelProperty("简介")
    @Excel(name = "简介")
    private String introduction;

    /** 详情 */
    @ApiModelProperty("详情")
    @Excel(name = "详情")
    private String details;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginNames;

    /** 是否发布 */
    @ApiModelProperty("是否发布")
    @Excel(name = "是否发布")
    private Boolean publish;

    /** 发布时间 */
    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

}
