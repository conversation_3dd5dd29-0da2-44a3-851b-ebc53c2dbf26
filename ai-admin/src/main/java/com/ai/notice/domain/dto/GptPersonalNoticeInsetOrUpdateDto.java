package com.ai.notice.domain.dto;


import com.ai.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "个人通知发布新增或者更新传参")
public class GptPersonalNoticeInsetOrUpdateDto {

    private Long id;

    @ApiModelProperty("标题信息")
    @Excel(name = "标题信息")
    private String title;

    /** 简介 */
    @ApiModelProperty("简介")
    @Excel(name = "简介")
    private String introduction;

    /** 详情 */
    @ApiModelProperty("详情")
    @Excel(name = "详情")
    private String details;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private List<String> loginNames;
}
