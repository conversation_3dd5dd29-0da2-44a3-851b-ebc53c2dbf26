package com.ai.notice.service.impl;

import java.util.*;

import com.ai.admin.domain.GptUser;
import com.ai.admin.mapper.GptUserMapper;
import com.ai.common.constant.HttpStatus;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.NoticeMessType;
import com.ai.common.enums.NoticeUserType;
import com.ai.constants.LogicParamsCons;
import com.ai.notice.domain.dto.GptPersonalNoticeInsetOrUpdateDto;
import com.ai.operation.domain.GptPlatformActivity;
import com.ai.operation.domain.entity.AccountInfo;
import com.ai.operation.domain.entity.CommFile;
import com.ai.operation.domain.entity.mongo.UserPlatformMessage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import com.ai.notice.mapper.GptPersonalNoticeMapper;
import com.ai.notice.domain.GptPersonalNotice;
import com.ai.notice.service.IGptPersonalNoticeService;

import javax.annotation.Resource;

/**
 * 个人通知发布Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
public class GptPersonalNoticeServiceImpl extends ServiceImpl<GptPersonalNoticeMapper, GptPersonalNotice> implements IGptPersonalNoticeService
{
    @Autowired
    private GptPersonalNoticeMapper gptPersonalNoticeMapper;

    @Autowired
    private GptUserMapper gptUserMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    /**
     * 查询个人通知发布
     * 
     * @param id 个人通知发布主键
     * @return 个人通知发布
     */
    @Override
    public GptPersonalNoticeInsetOrUpdateDto selectGptPersonalNoticeById(Long id) {

        GptPersonalNotice gptPersonalNotice = gptPersonalNoticeMapper.selectGptPersonalNoticeById(id);
        GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto = new GptPersonalNoticeInsetOrUpdateDto();

        // 将 GptPersonalNotice 的属性复制到 Dto 中
        BeanUtils.copyProperties(gptPersonalNotice, gptPersonalNoticeInsetOrUpdateDto);

        // 如果 loginNames 字段是逗号分隔的字符串，转换为 List<String>
        String loginNamesStr = gptPersonalNotice.getLoginNames();
        if (loginNamesStr != null && !loginNamesStr.isEmpty()) {
            // 将逗号分隔的字符串转回 List<String>
            List<String> loginNames = Arrays.asList(loginNamesStr.split(","));
            gptPersonalNoticeInsetOrUpdateDto.setLoginNames(loginNames);
        }

        return gptPersonalNoticeInsetOrUpdateDto;
    }


    /**
     * 查询个人通知发布列表
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 个人通知发布
     */
    @Override
    public List<GptPersonalNotice> selectGptPersonalNoticeList(GptPersonalNotice gptPersonalNotice)
    {
        return gptPersonalNoticeMapper.selectGptPersonalNoticeList(gptPersonalNotice);
    }

    public GptPersonalNotice copyGptPersonalNotice(GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto){
        GptPersonalNotice gptPersonalNotice = new GptPersonalNotice();
        BeanUtils.copyProperties(gptPersonalNoticeInsetOrUpdateDto, gptPersonalNotice);

        List<String> loginNames = gptPersonalNoticeInsetOrUpdateDto.getLoginNames();
        // 去重并将 loginNames 列表转为逗号分隔的字符串
        if (loginNames != null && !loginNames.isEmpty()) {
            // 使用 Stream 去重并转换为逗号分隔的字符串
            String loginNamesStr = loginNames.stream()
                    .distinct()  // 去重
                    .collect(Collectors.joining(","));  // 连接成字符串
            gptPersonalNotice.setLoginNames(loginNamesStr); // 设置到 GptPersonalNotice
        }
        return gptPersonalNotice;
    }
    /**
     * 新增个人通知发布
     * 
     * @param gptPersonalNoticeInsetOrUpdateDto 个人通知发布
     * @return 结果
     */
    @Override
    public int insertGptPersonalNotice(GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto) {

        GptPersonalNotice gptPersonalNotice = copyGptPersonalNotice(gptPersonalNoticeInsetOrUpdateDto);
        gptPersonalNotice.setCreateTime(LocalDateTime.now());
        gptPersonalNotice.setPublish(Boolean.FALSE);

        return gptPersonalNoticeMapper.insert(gptPersonalNotice);
    }


    /**
     * 修改个人通知发布
     * 
     * @param gptPersonalNoticeInsetOrUpdateDto 个人通知发布
     * @return 结果
     */
    @Override
    public int updateGptPersonalNotice(GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto)
    {
        GptPersonalNotice gptPersonalNotice = copyGptPersonalNotice(gptPersonalNoticeInsetOrUpdateDto);
        gptPersonalNotice.setUpdateTime(LocalDateTime.now());
        return gptPersonalNoticeMapper.updateGptPersonalNotice(gptPersonalNotice);
    }

    /**
     * 批量删除个人通知发布
     * 
     * @param ids 需要删除的个人通知发布主键
     * @return 结果
     */
    @Override
    public int deleteGptPersonalNoticeByIds(Long[] ids)
    {
        return gptPersonalNoticeMapper.deleteGptPersonalNoticeByIds(ids);
    }

    /**
     * 删除个人通知发布信息
     * 
     * @param id 个人通知发布主键
     * @return 结果
     */
    @Override
    public int deleteGptPersonalNoticeById(Long id)
    {
        return gptPersonalNoticeMapper.deleteGptPersonalNoticeById(id);
    }


    /**
     * 发布个人通知发布信息
     *
     * @param id 发布个人通知发布信息
     * @return 结果
     */
    @Override
    public Boolean toPublish(Long id) {
        // 查询相应的公告通知
        GptPersonalNotice gptPersonalNotice = gptPersonalNoticeMapper.selectById(id);

        // 更新状态为已发布
        gptPersonalNotice.setPublish(Boolean.TRUE);
        gptPersonalNotice.setPublishTime(LocalDateTime.now());
        gptPersonalNotice.setUpdateTime(LocalDateTime.now());
        gptPersonalNoticeMapper.updateById(gptPersonalNotice);

        // 获取所有登录名并去重
        String loginNamesStr = gptPersonalNotice.getLoginNames();
        if (loginNamesStr != null && !loginNamesStr.isEmpty()) {
            // 将逗号分隔的字符串转回 List<String> 并去重
            List<String> loginNames = Arrays.stream(loginNamesStr.split(","))
                    .distinct()  // 去重
                    .collect(Collectors.toList());

            // 查询所有对应的用户信息
            LambdaQueryWrapper<GptUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(GptUser::getLoginName, loginNames);
            List<GptUser> gptUsers = gptUserMapper.selectList(queryWrapper);

            // 将查询结果转为 Map，key 为 loginName，value 为 GptUser
            Map<String, GptUser> userMap = gptUsers.stream()
                    .collect(Collectors.toMap(GptUser::getLoginName, user -> user));

            // 准备发送消息
            List<UserPlatformMessage> insertUserPlatformMessages = loginNames.stream()
                    .filter(userMap::containsKey) // 过滤掉不存在的用户
                    .map(loginName -> {

                        // 刷新reids
                        Integer nPlatformMessageNums =
                                (int) Optional.ofNullable(redisCachePiclumen.
                                        getCacheMapValue(LogicParamsCons.COMM_USER_NOT_READ_MESSAGE + loginName,LogicParamsCons.NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
                        nPlatformMessageNums++;
                        redisCachePiclumen.setCacheMapValue(LogicParamsCons.COMM_USER_NOT_READ_MESSAGE + loginName,LogicParamsCons.NOT_READ_PLATFORM_MESSAGE_NUMS,nPlatformMessageNums);

                        // 新增mongo 数据
                        GptUser gptUser = userMap.get(loginName);
                        AccountInfo accountInfo = new AccountInfo();
                        accountInfo.setUserId(gptUser.getId());
                        accountInfo.setUserLoginName(loginName);
                        accountInfo.setUserName(gptUser.getUserName());
                        accountInfo.setUserAvatarUrl(gptUser.getAvatarUrl());

                        UserPlatformMessage message = new UserPlatformMessage();
                        message.setAccountInfo(accountInfo);
                        message.setTitle(gptPersonalNotice.getTitle());
                        message.setIntroduction(gptPersonalNotice.getIntroduction());
                        message.setDetails(gptPersonalNotice.getDetails());
                        message.setCreateTime(LocalDateTime.now());
                        message.setRead(Boolean.FALSE);
                        message.setMessType(NoticeMessType.official.getValue());
                        message.setMessageId(id.toString());
                        return message;
                    })
                    .collect(Collectors.toList());
            
            // 批量插入消息
            List<List<UserPlatformMessage>> partition = Lists.partition(insertUserPlatformMessages, 100);
            for (List<UserPlatformMessage>  list: partition) {
                mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, UserPlatformMessage.class)
                        .insert(list)
                        .execute();
            }
            // 批量插入消息
//            if (!insertUserPlatformMessages.isEmpty()) {
//                mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, UserPlatformMessage.class)
//                        .insert(insertUserPlatformMessages)
//                        .execute();
//            }
        }

        return Boolean.TRUE;
    }


    /**
     * 查询已发布的个人消息的平台消息详情
     *
     * @return 结果
     */
    @Override
    public TableDataInfo getPlatformMessageListByMessageId (Integer pageSize,Long messageId,
    String markFileId, Boolean isNext,Boolean read,String loginName){

        // 创建时间过滤条件
        Criteria criteria = new Criteria();

        Query query = new Query(criteria);

        query.addCriteria(Criteria.where("messageId").is(messageId.toString()));

        // 创建分页查询
        if (StringUtils.isNotBlank(markFileId)) {
            if (isNext) {
                query.addCriteria(Criteria.where("id").gt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.ASC, "id"));
            } else {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.DESC, "id"));
            }
        } else {
            query.with(Sort.by(Sort.Direction.ASC, "id"));
        }

        if (!Objects.isNull(read)){
            query.addCriteria(Criteria.where("read").is(read));
        }

        if (!Objects.isNull(loginName)){
            query.addCriteria(Criteria.where("accountInfo.userLoginName").is(loginName));
        }
        query.limit(pageSize);

        // 查询符合条件的数据
        List<UserPlatformMessage> userPlatformMessageList = mongoTemplate.find(query, UserPlatformMessage.class);

        if (StringUtils.isNotBlank(markFileId) && !isNext) {
            Collections.reverse(userPlatformMessageList);
        }

        // 封装响应数据
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(userPlatformMessageList);
        rspData.setTotal(10);
        return rspData;
    }
}
