package com.ai.notice.service;

import java.util.List;

import com.ai.common.core.page.TableDataInfo;
import com.ai.notice.domain.dto.GptPersonalNoticeInsetOrUpdateDto;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.notice.domain.GptPersonalNotice;

/**
 * 个人通知发布Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IGptPersonalNoticeService extends IService<GptPersonalNotice> {
    /**
     * 查询个人通知发布
     * 
     * @param id 个人通知发布主键
     * @return 个人通知发布
     */
    GptPersonalNoticeInsetOrUpdateDto selectGptPersonalNoticeById(Long id);

    /**
     * 查询个人通知发布列表
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 个人通知发布集合
     */
    List<GptPersonalNotice> selectGptPersonalNoticeList(GptPersonalNotice gptPersonalNotice);

    /**
     * 新增个人通知发布
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 结果
     */
    int insertGptPersonalNotice(GptPersonalNoticeInsetOrUpdateDto gptPersonalNotice);

    /**
     * 修改个人通知发布
     * 
     * @param gptPersonalNotice 个人通知发布
     * @return 结果
     */
     int updateGptPersonalNotice(GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto);

    /**
     * 批量删除个人通知发布
     * 
     * @param ids 需要删除的个人通知发布主键集合
     * @return 结果
     */
    int deleteGptPersonalNoticeByIds(Long[] ids);

    /**
     * 删除个人通知发布信息
     * 
     * @param id 个人通知发布主键
     * @return 结果
     */
     int deleteGptPersonalNoticeById(Long id);

    /**
     * 发布个人通知发布信息
     *
     * @param id 发布个人通知发布信息
     * @return 结果
     */
     Boolean toPublish(Long id);

    /**
     * 查询已发布的个人消息的平台消息详情
     *
     * @return 结果
     */
     TableDataInfo getPlatformMessageListByMessageId (Integer pageSize, Long messageId,
                                                            String markFileId, Boolean isNext,Boolean read,String loginName);
}
