package com.ai.notice.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.notice.domain.GptSuspensionMessage;
import com.ai.notice.service.IGptSuspensionMessageService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 停服公告Controller
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@Api(value = "停服公告控制器", tags = {"停服公告管理"})
@RequestMapping("/notice/message")
public class GptSuspensionMessageController extends BaseController {
    @Autowired
    private IGptSuspensionMessageService gptSuspensionMessageService;

    /**
     * 查询停服公告列表
     */
    @PreAuthorize("@ss.hasPermi('notice:message:list')")
    @ApiOperation("查询停服公告列表")
    @GetMapping("/list")
    public TableDataInfo list(GptSuspensionMessage gptSuspensionMessage) {
        startPage();
        List<GptSuspensionMessage> list = gptSuspensionMessageService.selectGptSuspensionMessageList(gptSuspensionMessage);
        return getDataTable(list);
    }

    /**
     * 导出停服公告列表
     */
    @ApiOperation("导出停服公告列表")
    @PreAuthorize("@ss.hasPermi('notice:message:export')")
    @Log(title = "停服公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptSuspensionMessage gptSuspensionMessage) {
        List<GptSuspensionMessage> list = gptSuspensionMessageService.selectGptSuspensionMessageList(gptSuspensionMessage);
        ExcelUtil<GptSuspensionMessage> util = new ExcelUtil<GptSuspensionMessage>(GptSuspensionMessage.class);
        util.exportExcel(response, list, "停服公告数据");
    }

    /**
     * 获取停服公告详细信息
     */
    @ApiOperation("获取停服公告详细信息")
    @PreAuthorize("@ss.hasPermi('notice:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptSuspensionMessageService.selectGptSuspensionMessageById(id));
    }

    /**
     * 新增停服公告
     */
    @ApiOperation("新增停服公告")
    @PreAuthorize("@ss.hasPermi('notice:message:add')")
    @Log(title = "停服公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptSuspensionMessage gptSuspensionMessage) {
        return gptSuspensionMessageService.insertGptSuspensionMessage(gptSuspensionMessage);
    }

    /**
     * 修改停服公告
     */
    @ApiOperation("修改停服公告")
    @PreAuthorize("@ss.hasPermi('notice:message:edit')")
    @Log(title = "停服公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptSuspensionMessage gptSuspensionMessage) {
        return toAjax(gptSuspensionMessageService.updateGptSuspensionMessage(gptSuspensionMessage));
    }

    /**
     * 删除停服公告
     */
    @ApiOperation("删除停服公告")
    @PreAuthorize("@ss.hasPermi('notice:message:remove')")
    @Log(title = "停服公告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptSuspensionMessageService.deleteGptSuspensionMessageByIds(ids));
    }
}
