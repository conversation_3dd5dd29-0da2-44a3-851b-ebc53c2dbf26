package com.ai.notice.controller;

import java.time.LocalDate;
import java.util.List;

import com.ai.notice.domain.dto.GptPersonalNoticeInsetOrUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.notice.domain.GptPersonalNotice;
import com.ai.notice.service.IGptPersonalNoticeService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 个人通知发布Controller
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@RestController
@Api(value = "个人通知发布控制器", tags = {"个人通知发布管理"})
@RequestMapping("/notice/personal")
public class GptPersonalNoticeController extends BaseController {
    @Autowired
    private IGptPersonalNoticeService gptPersonalNoticeService;

    /**
     * 查询个人通知发布列表
     */
    @PreAuthorize("@ss.hasPermi('notice:personal:list')")
    @ApiOperation("查询个人通知发布列表")
    @GetMapping("/list")
    public TableDataInfo list(GptPersonalNotice gptPersonalNotice) {
        startPage();
        List<GptPersonalNotice> list = gptPersonalNoticeService.selectGptPersonalNoticeList(gptPersonalNotice);
        return getDataTable(list);
    }

    /**
     * 导出个人通知发布列表
     */
    @ApiOperation("导出个人通知发布列表")
    @PreAuthorize("@ss.hasPermi('notice:personal:export')")
    @Log(title = "个人通知发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptPersonalNotice gptPersonalNotice) {
        List<GptPersonalNotice> list = gptPersonalNoticeService.selectGptPersonalNoticeList(gptPersonalNotice);
        ExcelUtil<GptPersonalNotice> util = new ExcelUtil<GptPersonalNotice>(GptPersonalNotice.class);
        util.exportExcel(response, list, "个人通知发布数据");
    }

    /**
     * 获取个人通知发布详细信息
     */
    @ApiOperation("获取个人通知发布详细信息")
    @PreAuthorize("@ss.hasPermi('notice:personal:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptPersonalNoticeService.selectGptPersonalNoticeById(id));
    }

    /**
     * 新增个人通知发布
     */
    @ApiOperation("新增个人通知发布")
    @PreAuthorize("@ss.hasPermi('notice:personal:add')")
    @Log(title = "个人通知发布", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto) {
        return toAjax(gptPersonalNoticeService.insertGptPersonalNotice(gptPersonalNoticeInsetOrUpdateDto));
    }

    /**
     * 修改个人通知发布
     */
    @ApiOperation("修改个人通知发布")
    @PreAuthorize("@ss.hasPermi('notice:personal:edit')")
    @Log(title = "个人通知发布", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptPersonalNoticeInsetOrUpdateDto gptPersonalNoticeInsetOrUpdateDto) {
        return toAjax(gptPersonalNoticeService.updateGptPersonalNotice(gptPersonalNoticeInsetOrUpdateDto));
    }

    /**
     * 删除个人通知发布
     */
    @ApiOperation("删除个人通知发布")
    @PreAuthorize("@ss.hasPermi('notice:personal:remove')")
    @Log(title = "个人通知发布", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptPersonalNoticeService.deleteGptPersonalNoticeByIds(ids));
    }

    /**
     * 发布个人通知发布信息
     */
    @ApiOperation("发布个人通知发布信息")
    @PreAuthorize("@ss.hasPermi('notice:personal:edit')")
    @Log(title = "发布个人通知发布信息", businessType = BusinessType.UPDATE)
    @GetMapping("/to-publish/{id}")
    public AjaxResult toPublish(@PathVariable Long id) {
        return toAjax(gptPersonalNoticeService.toPublish(id));
    }

    @PreAuthorize("@ss.hasPermi('notice:personal:list')")
    @ApiOperation("查询已发布的个人消息的平台消息详情")
    @GetMapping("/platform-message-list")
    public TableDataInfo getRecentThreeDaysData(@RequestParam(value =  "pageSize" ) Integer pageSize,
                                                @RequestParam(value =  "messageId" ) Long messageId,
                                                @RequestParam(value =  "markFileId" , required = false) String markFileId,
                                                @RequestParam(value =  "isNext" , required = false) Boolean isNext,
                                                @RequestParam(value =  "read" , required = false) Boolean read,
                                                @RequestParam(value =  "loginName" , required = false) String loginName
                                                ) {
        return gptPersonalNoticeService.getPlatformMessageListByMessageId(pageSize,messageId,markFileId, isNext,read,loginName);
    }
}
