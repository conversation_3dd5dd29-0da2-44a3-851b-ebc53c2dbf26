package com.ai;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableFeignClients
@EnableScheduling
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
public class AiManagerApplication {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(AiManagerApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  ai管理网站启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
