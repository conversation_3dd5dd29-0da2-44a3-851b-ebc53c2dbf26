package com.ai.admin.controller;

import java.util.List;

import com.ai.common.config.SystemConfig;
import com.ai.common.utils.file.FileUploadUtils;
import com.ai.common.utils.file.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.admin.domain.GptUserAlbum;
import com.ai.admin.service.IGptUserAlbumService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户相册Controller
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@RestController
@Api(value = "用户相册控制器", tags = {"用户相册管理"})
@Slf4j
@RequestMapping("/admin/album")
public class GptUserAlbumController extends BaseController {
    @Autowired
    private IGptUserAlbumService gptUserAlbumService;

    /**
     * 查询用户相册列表
     */
    @PreAuthorize("@ss.hasPermi('admin:album:list')")
    @ApiOperation("查询用户相册列表")
    @GetMapping("/list")
    public TableDataInfo list(GptUserAlbum gptUserAlbum) {
        startPage();
        List<GptUserAlbum> list = gptUserAlbumService.selectGptUserAlbumList(gptUserAlbum);
        return getDataTable(list);
    }

    /**
     * 导出用户相册列表
     */
    @ApiOperation("导出用户相册列表")
    @PreAuthorize("@ss.hasPermi('admin:album:export')")
    @Log(title = "用户相册", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptUserAlbum gptUserAlbum) {
        List<GptUserAlbum> list = gptUserAlbumService.selectGptUserAlbumList(gptUserAlbum);
        ExcelUtil<GptUserAlbum> util = new ExcelUtil<GptUserAlbum>(GptUserAlbum.class);
        util.exportExcel(response, list, "用户相册数据");
    }

    /**
     * 获取用户相册详细信息
     */
    @ApiOperation("获取用户相册详细信息")
    @PreAuthorize("@ss.hasPermi('admin:album:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptUserAlbumService.selectGptUserAlbumById(id));
    }

    /**
     * 新增用户相册
     */
    @ApiOperation("新增用户相册")
    @PreAuthorize("@ss.hasPermi('admin:album:add')")
    @Log(title = "用户相册", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptUserAlbum gptUserAlbum) {
        return toAjax(gptUserAlbumService.insertGptUserAlbum(gptUserAlbum));
    }

    /**
     * 修改用户相册
     */
    @ApiOperation("修改用户相册")
    @PreAuthorize("@ss.hasPermi('admin:album:edit')")
    @Log(title = "用户相册", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptUserAlbum gptUserAlbum) {
        return toAjax(gptUserAlbumService.updateGptUserAlbum(gptUserAlbum));
    }

    /**
     * 删除用户相册
     */
    @ApiOperation("删除用户相册")
    @PreAuthorize("@ss.hasPermi('admin:album:remove')")
    @Log(title = "用户相册", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptUserAlbumService.deleteGptUserAlbumByIds(ids));
    }

    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = SystemConfig.getUploadPath();
            // 上传并返回新文件名称
            String url = gptUserAlbumService.uploadNew(filePath, file);
//            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
