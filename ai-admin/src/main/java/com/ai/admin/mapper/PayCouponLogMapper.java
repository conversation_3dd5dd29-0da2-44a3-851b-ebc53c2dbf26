package com.ai.admin.mapper;

import java.util.List;
import com.ai.admin.domain.PayCouponLog;
import com.ai.admin.domain.dto.PayCouponStatsDTO;
import com.ai.admin.domain.dto.PayCouponPurchaseDetailDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 优惠券使用日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface PayCouponLogMapper extends BaseMapper<PayCouponLog> {
    
    /**
     * 查询优惠券使用日志列表
     * 
     * @param payCouponLog 优惠券使用日志
     * @return 优惠券使用日志集合
     */
    List<PayCouponLog> selectPayCouponLogList(PayCouponLog payCouponLog);

    /**
     * 根据优惠码查询统计数据
     * 
     * @param couponCode 优惠码
     * @return 统计数据
     */
    PayCouponStatsDTO selectStatsByCouponCode(@Param("couponCode") String couponCode);

    /**
     * 根据优惠码查询购买详情列表
     * 
     * @param couponCode 优惠码
     * @return 购买详情列表
     */
    List<PayCouponPurchaseDetailDTO> selectPurchaseDetailsByCouponCode(@Param("couponCode") String couponCode);

    /**
     * 查询所有优惠码的统计数据
     *
     * @return 统计数据列表
     */
    List<PayCouponStatsDTO> selectAllCouponStats();

    /**
     * 根据类型查询优惠码统计数据
     *
     * @param type     类型
     * @param lastCode
     * @return 统计数据列表
     */
    List<PayCouponStatsDTO> selectCouponStatsByTypePageList(@Param("type") String type, @Param("lastCode") String lastCode);

    /**
     * 查询所有类型列表
     *
     * @return 类型列表
     */
    List<String> selectAllTypes();
}
