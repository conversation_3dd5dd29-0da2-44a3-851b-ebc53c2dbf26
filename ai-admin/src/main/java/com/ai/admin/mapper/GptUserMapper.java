package com.ai.admin.mapper;

import java.util.List;

import com.ai.admin.domain.dto.GptUserVipUpdateParam;
import com.ai.admin.domain.dto.VipUserDto;
import com.ai.admin.domain.vo.VipUserVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.admin.domain.GptUser;
import org.apache.ibatis.annotations.Param;

/**
 * 用户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface GptUserMapper extends BaseMapper<GptUser> {
    /**
     * 查询用户信息
     * 
     * @param id 用户信息主键
     * @return 用户信息
     */
    public GptUser selectGptUserById(Long id);

    /**
     * 查询用户信息列表
     * 
     * @param gptUser 用户信息
     * @return 用户信息集合
     */
   List<GptUser> selectGptUserList(GptUser gptUser);

    /**
     * 新增用户信息
     * 
     * @param gptUser 用户信息
     * @return 结果
     */
    public int insertGptUser(GptUser gptUser);

    /**
     * 修改用户信息
     * 
     * @param gptUser 用户信息
     * @return 结果
     */
    public int updateGptUser(GptUser gptUser);

    /**
     * 删除用户信息
     * 
     * @param id 用户信息主键
     * @return 结果
     */
    public int deleteGptUserById(Long id);

    /**
     * 批量删除用户信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptUserByIds(Long[] ids);


    /**
     * 根据用户名称获取用户id
     * @param opexLoginNameList
     * @return
     */
    public List<String> getUserIdByLoginNames(@Param("opexLoginNameList") List<String> opexLoginNameList);


    Long getUserNums(@Param("startDate") String startDate, @Param("endDate") String endDate,@Param("opexLoginNameList") List<String> opexLoginNameList);


    /**
     * 查询vip 列表
     * @param vipUserDto
     * @return
     */
    List<VipUserVo>  selectVipUserList(VipUserDto vipUserDto);

    void batchCollectionUpdateUsers(@Param("users") List<GptUser> users);

    Long sumTotalImgNum();

    void batchUpdateUserVipInfo(@Param("list") List<GptUserVipUpdateParam> list);

//    void batchUpdateUserSystemRewardLumen(@Param("list") List<GptUser> list);
}
