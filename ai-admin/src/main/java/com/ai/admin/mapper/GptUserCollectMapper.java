package com.ai.admin.mapper;

import java.util.List;

import com.ai.admin.domain.dto.CollectLoginNameNum;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.admin.domain.GptUserCollect;
import org.apache.ibatis.annotations.Param;

/**
 * 图片收藏Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface GptUserCollectMapper extends BaseMapper<GptUserCollect> {

    /**
     * 查询图片收藏
     *
             * @param id 图片收藏主键
     * @return 图片收藏
     */
    GptUserCollect selectGptUserCollectById(Long id);

    /**
     * 查询图片收藏列表
     *
     * @param gptUserCollect 图片收藏
     * @return 图片收藏集合
     */
    List<GptUserCollect> selectGptUserCollectList(GptUserCollect gptUserCollect);

    /**
     * 新增图片收藏
     *
     * @param gptUserCollect 图片收藏
     * @return 结果
     */
    int insertGptUserCollect(GptUserCollect gptUserCollect);

    /**
     * 修改图片收藏
     *
     * @param gptUserCollect 图片收藏
     * @return 结果
     */
    int updateGptUserCollect(GptUserCollect gptUserCollect);

    /**
     * 删除图片收藏
     *
     * @param id 图片收藏主键
     * @return 结果
     */
    int deleteGptUserCollectById(Long id);

    /**
     * 批量删除图片收藏
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptUserCollectByIds(Long[] ids);

    List<CollectLoginNameNum> selectNumByLoginNames(@Param("tableName")String tableName, @Param("loginNames") List<String> loginNames);


}
