package com.ai.admin.mapper;

import com.ai.admin.domain.GptUserAlbum;
import com.ai.admin.domain.ImgControl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户相册Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
public interface ImgControlMapper extends BaseMapper<ImgControl> {


    @Select("select * from gpt_img_control where  del = 0 and img_url is not null and user_id > 0  and id > #{id} order by id asc limit 1000")
    List<ImgControl> queryImgControlList(Long id);
}
