package com.ai.admin.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.admin.domain.GptContacts;

/**
 * 联系我们Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface GptContactsMapper extends BaseMapper<GptContacts> {
    /**
     * 查询联系我们
     * 
     * @param id 联系我们主键
     * @return 联系我们
     */
    public GptContacts selectGptContactsById(Long id);

    /**
     * 查询联系我们列表
     * 
     * @param gptContacts 联系我们
     * @return 联系我们集合
     */
    public List<GptContacts> selectGptContactsList(GptContacts gptContacts);

    /**
     * 新增联系我们
     * 
     * @param gptContacts 联系我们
     * @return 结果
     */
    public int insertGptContacts(GptContacts gptContacts);

    /**
     * 修改联系我们
     * 
     * @param gptContacts 联系我们
     * @return 结果
     */
    public int updateGptContacts(GptContacts gptContacts);

    /**
     * 删除联系我们
     * 
     * @param id 联系我们主键
     * @return 结果
     */
    public int deleteGptContactsById(Long id);

    /**
     * 批量删除联系我们
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptContactsByIds(Long[] ids);
}
