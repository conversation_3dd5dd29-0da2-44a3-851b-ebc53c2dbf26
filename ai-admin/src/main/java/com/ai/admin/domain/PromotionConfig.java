package com.ai.admin.domain;

import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 优惠配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("promotion_config")
@Schema(description = "优惠配置表")
public class PromotionConfig extends MyBaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 优惠类型：first_buy_sub_old_vip_back,lumen_off
     * @see com.ai.enums.PromotionType
     */
    @Schema(description = "优惠类型：first_buy_sub_old_vip_back,lumen_off")
    private String type;

    /**
     * 优惠名称
     */
    @Schema(description = "优惠名称")
    private String name;

    /**
     * 优惠券ID
     */
    @Schema(description = "优惠券ID")
    private String couponId;

    /**
     * 折扣百分比(0-100)
     */
    @Schema(description = "折扣百分比(0-100)")
    private Integer off;

    @Schema(description = "前端显示折扣百分比(如20代表八折)")
    private Integer frontOff;

    private String description;


    /**
     * 生效时间
     */
    @Schema(description = "生效时间")
    private Long startTime;

    /**
     * 截止时间
     */
    @Schema(description = "截止时间")
    private Long redeemBy;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enable;
}
