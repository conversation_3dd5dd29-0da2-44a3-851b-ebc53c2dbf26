package com.ai.admin.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.ToString;

@ToString
public class Resolution {

    @Schema(description = "宽")
    private int width;

    @Schema(description = "高")
    private int height;

    @Schema(description = "批量生成数")
    private int batch_size;

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getBatch_size() {
        return batch_size;
    }

    public void setBatch_size(int batch_size) {
        this.batch_size = batch_size;
    }

}
