package com.ai.admin.domain;

import com.alibaba.excel.annotation.ExcelProperty;;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@HeadRowHeight(20)
@ContentRowHeight(60)
public class PromptRecordExcel {

    /**
     * 创建时间
     */
    @ExcelProperty("Date(UTC)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(30)
    private LocalDateTime createTime;

    @ExcelProperty("Login-name")
    @ColumnWidth(30)
    private String loginName;

    @ExcelProperty("Prompt-params")
    @ColumnWidth(200)
    private String params;

}
