package com.ai.admin.domain.vo;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@ApiModel(description = "vip用户分页返回参数")
public class VipUserVo {

    /** 用户ID */
    @ApiModelProperty(value = "用户账号")
    private Long id;

    @ApiModelProperty(value = "用户账号")
    private String loginName;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String userName;

    /**
     * 用户邮箱
     */
    @ApiModelProperty(value = "用户邮箱")
    private String email;

    /**
     * 头像文件路径
     */
    @ApiModelProperty(value = "头像文件路径")
    private String avatarUrl;

    /**
     * 头像文件缩略图路径
     */
    @ApiModelProperty(value = "头像文件缩略图路径")
    private String thumbnailAvatarUrl;

    /**
     * 已使用的存储空间 bytes
     */
    @ApiModelProperty(value = "已使用的存储空间 bytes")
    private Long usedSize;

    /**
     * 总存储空间 bytes
     */
    @ApiModelProperty(value = "总存储空间 bytes")
    private Long totalSize;

    /**
     * 会员类型： basic 非会员 standard 普通会员 pro 高级会员
     */
    @ApiModelProperty(value = "会员类型： basic 非会员 standard 普通会员 pro 高级会员")
    private String vipType;

    /**
     * 普通会员生效时间
     */
    @ApiModelProperty(value = "普通会员生效时间")
    private Long vipBeginTime;

    /**
     * 普通会员过期时间
     */
    @ApiModelProperty(value = "普通会员过期时间")
    private Long vipEndTime;


    /**
     * 每日免费点数日期(只记录年月日)
     */
    @ApiModelProperty(value = "每日免费点数日期(只记录年月日)")
    private Long dailyLumensTime;

    /**
     * 每日免费点数
     */
    @ApiModelProperty(value = "每日免费点数")
    private Integer dailyLumens;


    /**
     * 剩余每日免费点数
     */
    @ApiModelProperty(value = "剩余每日免费点数")
    private Integer useDailyLumens;


    /**
     * 用户配置相关
     */
    @ApiModelProperty(value = "用户配置相关")
    private JsonNode userConfig;

    /**
     * 价格间隔
     */
    @Schema(description = "价格间隔（例如：year，month）")
    private String priceInterval;


    /**
     * 注册国家
     */
    @Schema(description = "注册国家")
    private String   registCountry;

    /**
     * 用户已收藏数量
     */
    @Schema(description = "用户已收藏数量")
    private Integer usedCollectNum;


}

