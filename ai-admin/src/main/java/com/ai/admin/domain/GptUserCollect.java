package com.ai.admin.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 图片收藏对象 gpt_user_collect
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_user_collect", description = "图片收藏")
@TableName("gpt_user_collect")
public class GptUserCollect extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** prompt_id */
    @ApiModelProperty("prompt_id")
    @Excel(name = "prompt_id")
    private String promptId;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 收藏夹id */
    @ApiModelProperty("收藏夹id")
    @Excel(name = "收藏夹id")
    private Long classifyId;

    /** 文件名称 */
    @ApiModelProperty("文件名称")
    @Excel(name = "文件名称")
    private String fileName;

    /** 缩略图名称 */
    @ApiModelProperty("缩略图名称")
    @Excel(name = "缩略图名称")
    private String thumbnailName;

    /** 高清缩略图名称 */
    @ApiModelProperty("高清缩略图名称")
    @Excel(name = "高清缩略图名称")
    private String highThumbnailName;

    /** 图片路径 */
    @ApiModelProperty("图片路径")
    @Excel(name = "图片路径")
    private String fileUrl;

    /** 缩略图路径 */
    @ApiModelProperty("缩略图路径")
    @Excel(name = "缩略图路径")
    private String thumbnailUrl;

    /** 图片入参 */
    @ApiModelProperty("图片入参")
    @Excel(name = "图片入参")
    private String genInfo;

    /** 生图类型 */
    @ApiModelProperty("生图类型")
    @Excel(name = "生图类型")
    private String originCreate;

    /** 高清缩略图路径 */
    @ApiModelProperty("高清缩略图路径")
    @Excel(name = "高清缩略图路径")
    private String highThumbnailUrl;

    /** $column.columnComment */
    @ApiModelProperty("${comment}")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String miniThumbnailUrl;

    /** 涉黄：NSFW */
    @ApiModelProperty("涉黄：NSFW")
    @Excel(name = "涉黄：NSFW")
    private String sensitiveMessage;

    /** 图片结果宽度 */
    @ApiModelProperty("图片结果宽度")
    @Excel(name = "图片结果宽度")
    private Integer width;

    /** 图片结果高度 */
    @ApiModelProperty("图片结果高度")
    @Excel(name = "图片结果高度")
    private Integer height;

    /** 正向提示词 */
    @ApiModelProperty("正向提示词")
    @Excel(name = "正向提示词")
    private String prompt;

    /** 文件id */
    @ApiModelProperty("文件id")
    @Excel(name = "文件id")
    private Long fileId;

    /** 容量bytes */
    @ApiModelProperty("容量bytes")
    @Excel(name = "容量bytes")
    private Long size;

    /** 0:未删除 1：已删除 */
    @ApiModelProperty("0:未删除 1：已删除")
    @Excel(name = "0:未删除 1：已删除")
    private Boolean del;

}
