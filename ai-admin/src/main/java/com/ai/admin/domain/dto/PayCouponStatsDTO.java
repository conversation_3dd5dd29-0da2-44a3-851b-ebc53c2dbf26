package com.ai.admin.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 优惠券统计数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@Schema(description = "优惠券统计数据")
public class PayCouponStatsDTO {

    @Schema(description = "折扣码")
    private String couponCode;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "会员总&&Lumen购买金额")
    private BigDecimal totalAmount;

    @Schema(description = "购买会员人数")
    private Integer memberCount;

    @Schema(description = "购买会员总金额")
    private BigDecimal totalMemberAmount;

    @Schema(description = "购买Lumen套餐包人数")
    private Integer lumenPackageCount;

    @Schema(description = "购买Lumen金额")
    private BigDecimal lumenAmount;
}
