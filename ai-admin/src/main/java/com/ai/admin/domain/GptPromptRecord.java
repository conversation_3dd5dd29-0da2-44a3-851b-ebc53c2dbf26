package com.ai.admin.domain;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 用户生图任务记录对象 gpt_prompt_record
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_prompt_record", description = "用户生图任务记录")
@TableName("gpt_prompt_record")
public class GptPromptRecord extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** 用户头像 */
    private String avatar;

    /** 生成信息 */
    @ApiModelProperty("生成信息")
    @Excel(name = "生成信息")
    private String genInfo;

    /** 生图模式 */
    @ApiModelProperty("生图模式")
    @Excel(name = "生图模式")
    private String genMode;

    /** 生成任务的id(java端生成) */
    private String markId;

    /** 任务id */
    @ApiModelProperty("任务id")
    @Excel(name = "任务id")
    private String promptId;

    /** 生图任务id */
    @ApiModelProperty("生图任务id")
    @Excel(name = "生图任务id")
    private String taskId;

    /** 任务排队序号 */
    @ApiModelProperty("任务排队序号")
    @Excel(name = "任务排队序号")
    private Long taskNumber;

    /** 正向提示词 */
    @ApiModelProperty("正向提示词")
    @Excel(name = "正向提示词")
    private String prompt;

    /** 反向提示词 */
    @ApiModelProperty("反向提示词")
    @Excel(name = "反向提示词")
    private String negativePrompt;

    /** 生图类型 */
    @ApiModelProperty("生图类型")
    @Excel(name = "生图类型")
    private String originCreate;

    /** 是否发送成功 */
    @ApiModelProperty("是否发送成功")
    @Excel(name = "是否发送成功")
    private Integer sendWsFailure;

    /** 生图开始时间 */
    @ApiModelProperty("生图开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生图开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDateTime genStartTime;

    /** 生图结束时间 */
    @ApiModelProperty("生图结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生图结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDateTime genEndTime;

    /** 生图数量 */
    @ApiModelProperty("生图数量")
    @Excel(name = "生图数量")
    private Long batchSize;

    /** 宽高比 */
    @ApiModelProperty("宽高比")
    @Excel(name = "宽高比")
    private String aspectRatio;

    /** 模型 */
    @ApiModelProperty("模型")
    @Excel(name = "模型")
    private String modelId;

    /**
     * 调用py入参
     */
    @ApiModelProperty("调用py入参")
    private JsonNode promptParams;

    /**
     * 功能类型
     */
    @ApiModelProperty("功能类型")
    @Excel(name = "功能类型")
    private String featureName;

    /**
     * 失败信息
     */
    @ApiModelProperty("失败信息")
    @Excel(name = "失败信息")
    private String failureMessage;

    /**
     * 是否属于fastHour机制内的任务
     */
    @ApiModelProperty("是否属于fastHour机制内的任务")
    @Excel(name = "是否属于fastHour机制内的任务")
    private Boolean fastHour;

    /**
     * 消耗lumens点数
     */
    @ApiModelProperty("消耗lumens点数")
    @Excel(name = "消耗lumens点数")
    private Integer costLumens;

    /**
     * 来源平台： web android ios
     */
    @ApiModelProperty("来源平台： web android ios")
    @Excel(name = "来源平台")
    private String platform;


    /** 0 ： 未删除  1 ：已删除 */
    private Integer del;

}
