package com.ai.admin.service;

import java.util.List;
import java.util.Map;

import com.ai.admin.domain.dto.VipUserDto;
import com.ai.admin.domain.vo.VipUserVo;
import com.ai.orders.domain.SubscriptionCurrent;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.admin.domain.GptUser;

/**
 * 用户信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface IGptUserService extends IService<GptUser> {
    /**
     * 查询用户信息
     * 
     * @param id 用户信息主键
     * @return 用户信息
     */
    public GptUser selectGptUserById(Long id);

    /**
     * 查询用户信息列表
     * 
     * @param gptUser 用户信息
     * @return 用户信息集合
     */
    public List<GptUser> selectGptUserList(GptUser gptUser);

    /**
     * 新增用户信息
     * 
     * @param gptUser 用户信息
     * @return 结果
     */
    public int insertGptUser(GptUser gptUser);

    /**
     * 修改用户信息
     * 
     * @param gptUser 用户信息
     * @return 结果
     */
    public int updateGptUser(GptUser gptUser);

    /**
     * 批量删除用户信息
     * 
     * @param ids 需要删除的用户信息主键集合
     * @return 结果
     */
    public int deleteGptUserByIds(Long[] ids);

    /**
     * 删除用户信息信息
     * 
     * @param id 用户信息主键
     * @return 结果
     */
    public int deleteGptUserById(Long id);

    /**
     * 查询vip 列表
     * @param vipUserDto
     * @return
     */
     List<VipUserVo> selectVipUserList(VipUserDto vipUserDto);

    List<GptUser> getUserByLoginName(String loginName);

    /**
     * 更改vip 状态
     * @return
     */
    void updateUserVipInfo(SubscriptionCurrent current, Long userId);

    /**
     * 更改vip 状态(批量)
     * @return
     */
    void updateUserVipInfoBatch(Map<Long, SubscriptionCurrent> userIdCurrentMap);
}
