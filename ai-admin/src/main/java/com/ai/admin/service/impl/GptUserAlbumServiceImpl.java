package com.ai.admin.service.impl;

import cn.hutool.core.util.URLUtil;
import com.ai.admin.domain.GptUserAlbum;
import com.ai.admin.domain.ImgControl;
import com.ai.admin.mapper.GptUserAlbumMapper;
import com.ai.admin.mapper.ImgControlMapper;
import com.ai.admin.service.IGptUserAlbumService;
import com.ai.common.exception.file.FileNameLengthLimitExceededException;
import com.ai.common.exception.file.FileSizeLimitExceededException;
import com.ai.common.exception.file.InvalidExtensionException;
import com.ai.common.utils.StringUtils;
import com.ai.common.utils.file.FileUploadUtils;
import com.ai.cos.CosCommonService;
import com.ai.purge.CustomThreadFactory;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.RateLimiter;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.model.GetObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 用户相册Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Service
@Slf4j
public class GptUserAlbumServiceImpl extends ServiceImpl<GptUserAlbumMapper, GptUserAlbum> implements IGptUserAlbumService {
    @Autowired
    private GptUserAlbumMapper gptUserAlbumMapper;
    @Autowired
    private ImgControlMapper imgControlMapper;

    @Autowired
    private CosCommonService cosCommonService;
    @Autowired
    private ImgControlServiceImpl imgControlService;


    private static final List<String> SUPPORTED_FORMATS = Arrays.asList("jpeg", "jpg", "png", "webp", "jfif", "ico", "gif", "svg", "bmp");


    /**
     * 查询用户相册
     *
     * @param id 用户相册主键
     * @return 用户相册
     */
    @Override
    public GptUserAlbum selectGptUserAlbumById(Long id) {
        return gptUserAlbumMapper.selectGptUserAlbumById(id);
    }

    /**
     * 查询用户相册列表
     *
     * @param gptUserAlbum 用户相册
     * @return 用户相册
     */
    @Override
    public List<GptUserAlbum> selectGptUserAlbumList(GptUserAlbum gptUserAlbum) {
        return gptUserAlbumMapper.selectGptUserAlbumList(gptUserAlbum);
    }

    /**
     * 新增用户相册
     *
     * @param gptUserAlbum 用户相册
     * @return 结果
     */
    @Override
    public int insertGptUserAlbum(GptUserAlbum gptUserAlbum) {
        gptUserAlbum.setCreateTime(LocalDateTime.now());
        return gptUserAlbumMapper.insertGptUserAlbum(gptUserAlbum);
    }

    /**
     * 修改用户相册
     *
     * @param gptUserAlbum 用户相册
     * @return 结果
     */
    @Override
    public int updateGptUserAlbum(GptUserAlbum gptUserAlbum) {
        gptUserAlbum.setUpdateTime(LocalDateTime.now());
        return gptUserAlbumMapper.updateGptUserAlbum(gptUserAlbum);
    }

    /**
     * 批量删除用户相册
     *
     * @param ids 需要删除的用户相册主键
     * @return 结果
     */
    @Override
    public int deleteGptUserAlbumByIds(Long[] ids) {
        return gptUserAlbumMapper.deleteGptUserAlbumByIds(ids);
    }

    /**
     * 删除用户相册信息
     *
     * @param id 用户相册主键
     * @return 结果
     */
    @Override
    public int deleteGptUserAlbumById(Long id) {
        return gptUserAlbumMapper.deleteGptUserAlbumById(id);
    }

    private static final RateLimiter LIMITER = RateLimiter.create(1000);
    private static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, Runtime.getRuntime().availableProcessors() * 2, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(2000),
                                                                                 new CustomThreadFactory("init-thread-"), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void init() {
        COSClient baseCosClient = cosCommonService.getBaseCosClient();
        List<ImgControl> list = imgControlMapper.queryImgControlList(0L);

        while (!list.isEmpty()) {
            log.info("gpt user album init start : {}", list.size());
            doHandle(list, baseCosClient);
            list = imgControlMapper.queryImgControlList(list.get(list.size() - 1).getId());
            // list = this.lambdaQuery()
            //         .isNull(GptUserAlbum::getSize)
            //         .isNotNull(GptUserAlbum::getImgUrl)
            //         .lt(GptUserAlbum::getId, list.get(list.size() - 1).getId())
            //         .orderByDesc(GptUserAlbum::getId)
            //         .last("limit 1000").list();

        }
        log.info("gpt user album init end");
    }

    private void doHandle(List<ImgControl> list, COSClient baseCosClient) {
        List<CompletableFuture<Void>> futures = new ArrayList<>(list.size());
        for (ImgControl gptUserAlbum : list) {
            if (gptUserAlbum.getRealWidth() != null && gptUserAlbum.getRealHeight() != null) {
                continue;
            }
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                String imgUrl = gptUserAlbum.getImgUrl();
                if (imgUrl == null) {
                    return;
                }
                String bucketName = cosCommonService.getConfig().getBucketName();
                if (imgUrl.contains("piclumen-1324066212")) {
                    bucketName = cosCommonService.getConfig().getBucketNameOld();
                }
                String key = URLUtil.getPath(imgUrl);
                try {
                    getRealWidth(gptUserAlbum, bucketName, key, baseCosClient);
                } catch (Exception e) {
                    log.error("gpt user album init error : ", e);
                }
            }, THREAD_POOL);
            futures.add(voidCompletableFuture);
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();
        log.info("gpt user album init start update");
        imgControlService.saveOrUpdateBatch(list);
        log.info("gpt user album init update end : {}", list.size());
    }

    private static void getRealWidth(ImgControl gptUserAlbum, String bucketName, String key, COSClient baseCosClient) {
        GetObjectRequest getObj = new GetObjectRequest(bucketName, key);
        // 图片基本信息
        getObj.putCustomQueryParameter("imageInfo", null);
        LIMITER.acquire(1);
        COSObject object = baseCosClient.getObject(getObj);
        COSObjectInputStream objectContent = object.getObjectContent();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(objectContent, "UTF-8"))) {
            StringWriter writer = new StringWriter();
            String line;
            while ((line = reader.readLine()) != null) {
                writer.write(line);
            }
            String content = writer.toString();
            JSONObject parse = JSONObject.parse(content);
            Integer realWidth = parse.getInteger("width");
            if (realWidth == null) {
                return;
            }
            gptUserAlbum.setRealWidth(realWidth);
            Integer realHeight = parse.getInteger("height");
            if (realHeight == null) {
                return;
            }
            gptUserAlbum.setRealHeight(realHeight);
            Long size = parse.getLong("size");
            if (size == null) {
                return;
            }
            // gptUserAlbum.setSize(size);
            ShapeFinder.Shape closest = ShapeFinder.findClosestShape(realWidth, realHeight);
            gptUserAlbum.setWidth(closest.getWidth());
            gptUserAlbum.setHeight(closest.getHeight());
        } catch (Exception e) {
            log.error("gpt user album init error", e);
        }
    }

    static class ShapeFinder {
        static class Shape {
            int width;
            int height;
            double shapeRatio;

            public Shape(int width, int height, double shapeRatio) {
                this.width = width;
                this.height = height;
                this.shapeRatio = shapeRatio;
            }

            public int getWidth() {
                return width;
            }

            public int getHeight() {
                return height;
            }

            public double getShapeRatio() {
                return shapeRatio;
            }
        }

        private static final List<Shape> SHAPE_ALL = Arrays.asList(
                new Shape(1024, 1024, 1024 / 1024D),
                new Shape(1536, 640, 1536 / 640D),
                new Shape(640, 1536, 640 / 1536D),
                new Shape(1472, 704, 1472 / 704D),
                new Shape(704, 1472, 704 / 1472D),
                new Shape(1280, 640, 1280 / 640D),
                new Shape(640, 1280, 640 / 1280D),
                new Shape(1344, 768, 1344 / 768D),
                new Shape(768, 1344, 768 / 1344D),
                new Shape(1216, 768, 1216 / 768D),
                new Shape(768, 1216, 768 / 1216D),
                new Shape(1216, 832, 1216 / 768D),
                new Shape(832, 1216, 832 / 1216D),
                new Shape(1152, 896, 1152 / 896D),
                new Shape(896, 1152, 896 / 1152D),
                new Shape(1088, 960, 1088 / 960D),
                new Shape(960, 1088, 960 / 1088D)
        );

        public static Shape findClosestShape(int imageWidth, int imageHeight) {
            double targetRatio = (double) imageWidth / imageHeight;
            Shape closestShape = null;
            double minDiff = Double.MAX_VALUE;

            for (Shape shape : SHAPE_ALL) {
                double shapeRatio = shape.getShapeRatio();
                double diff = Math.abs(targetRatio - shapeRatio);

                if (diff < minDiff) {
                    minDiff = diff;
                    closestShape = shape;
                }
            }
            return closestShape;
        }
    }

    /**
     * 文件上传
     *
     * @param baseDir          相对应用的基目录
     * @param file             上传的文件
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException       如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException                          比如读写文件出错时
     * @throws InvalidExtensionException            文件校验异常
     */
    @Override
    public  String uploadNew(String baseDir, MultipartFile file)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException {

        File fileNew = null;
        fileNew = FileUploadUtils.transferToFile(file, "manage_");
        if (StringUtils.isNotBlank(file.getName()) && file.getName().length() > 200) {
            throw new RuntimeException("The uploaded image file name is too long!");
        }

        String formatName = FileUploadUtils.detectImageFormat(fileNew);
        if (StringUtils.isNotBlank(formatName) && !SUPPORTED_FORMATS.contains(formatName)) {
            throw new RuntimeException("The uploaded image format is not supported!");
        }

        String signedUrl = cosCommonService.uploadToOssWithType(fileNew, "manage");
        return signedUrl;
    }

}
