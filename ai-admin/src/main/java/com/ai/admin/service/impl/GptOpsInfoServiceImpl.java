package com.ai.admin.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.admin.mapper.GptOpsInfoMapper;
import com.ai.admin.domain.GptOpsInfo;
import com.ai.admin.service.IGptOpsInfoService;

/**
 * 运营信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-08
 */
@Service
public class GptOpsInfoServiceImpl extends ServiceImpl<GptOpsInfoMapper, GptOpsInfo> implements IGptOpsInfoService
{
    @Autowired
    private GptOpsInfoMapper gptOpsInfoMapper;

    /**
     * 查询运营信息
     * 
     * @param id 运营信息主键
     * @return 运营信息
     */
    @Override
    public GptOpsInfo selectGptOpsInfoById(Long id)
    {
        return gptOpsInfoMapper.selectGptOpsInfoById(id);
    }

    /**
     * 查询运营信息列表
     * 
     * @param gptOpsInfo 运营信息
     * @return 运营信息
     */
    @Override
    public List<GptOpsInfo> selectGptOpsInfoList(GptOpsInfo gptOpsInfo)
    {
        return gptOpsInfoMapper.selectGptOpsInfoList(gptOpsInfo);
    }

    /**
     * 新增运营信息
     * 
     * @param gptOpsInfo 运营信息
     * @return 结果
     */
    @Override
    public int insertGptOpsInfo(GptOpsInfo gptOpsInfo)
    {
        gptOpsInfo.setCreateTime(LocalDateTime.now());
        return gptOpsInfoMapper.insertGptOpsInfo(gptOpsInfo);
    }

    /**
     * 修改运营信息
     * 
     * @param gptOpsInfo 运营信息
     * @return 结果
     */
    @Override
    public int updateGptOpsInfo(GptOpsInfo gptOpsInfo)
    {
        gptOpsInfo.setUpdateTime(LocalDateTime.now());
        return gptOpsInfoMapper.updateGptOpsInfo(gptOpsInfo);
    }

    /**
     * 批量删除运营信息
     * 
     * @param ids 需要删除的运营信息主键
     * @return 结果
     */
    @Override
    public int deleteGptOpsInfoByIds(Long[] ids)
    {
        return gptOpsInfoMapper.deleteGptOpsInfoByIds(ids);
    }

    /**
     * 删除运营信息信息
     * 
     * @param id 运营信息主键
     * @return 结果
     */
    @Override
    public int deleteGptOpsInfoById(Long id)
    {
        return gptOpsInfoMapper.deleteGptOpsInfoById(id);
    }
}
