package com.ai.admin.service.impl;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

import com.ai.admin.domain.dto.GptUserVipUpdateParam;
import com.ai.admin.domain.dto.VipUserDto;
import com.ai.admin.domain.vo.VipUserVo;
import com.ai.operation.domain.entity.CommFile;
import com.ai.operation.domain.entity.CommUser;
import com.ai.orders.domain.SubscriptionCurrent;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import com.ai.admin.mapper.GptUserMapper;
import com.ai.admin.domain.GptUser;
import com.ai.admin.service.IGptUserService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 用户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
@Slf4j
public class GptUserServiceImpl extends ServiceImpl<GptUserMapper, GptUser> implements IGptUserService
{
    @Autowired
    private GptUserMapper gptUserMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 查询用户信息
     * 
     * @param id 用户信息主键
     * @return 用户信息
     */
    @Override
    public GptUser selectGptUserById(Long id)
    {
        return gptUserMapper.selectGptUserById(id);
    }

    /**
     * 查询用户信息列表
     * 
     * @param gptUser 用户信息
     * @return 用户信息
     */
    @Override
    public List<GptUser> selectGptUserList(GptUser gptUser)
    {
        return gptUserMapper.selectGptUserList(gptUser);
    }

    /**
     * 新增用户信息
     * 
     * @param gptUser 用户信息
     * @return 结果
     */
    @Override
    public int insertGptUser(GptUser gptUser)
    {
        gptUser.setCreateTime(LocalDateTime.now());
        return gptUserMapper.insertGptUser(gptUser);
    }

    /**
     * 修改用户信息
     * 
     * @param gptUser 用户信息
     * @return 结果
     */
    @Override
    public int updateGptUser(GptUser gptUser)
    {
        gptUser.setUpdateTime(LocalDateTime.now());
        return gptUserMapper.updateGptUser(gptUser);
    }

    /**
     * 批量删除用户信息
     * 
     * @param ids 需要删除的用户信息主键
     * @return 结果
     */
    @Override
    public int deleteGptUserByIds(Long[] ids)
    {
        return gptUserMapper.deleteGptUserByIds(ids);
    }

    /**
     * 删除用户信息信息
     * 
     * @param id 用户信息主键
     * @return 结果
     */
    @Override
    public int deleteGptUserById(Long id)
    {
        return gptUserMapper.deleteGptUserById(id);
    }

    /**
     * 查询vip 列表
     * @param vipUserDto
     * @return
     */
    @Override
    public List<VipUserVo> selectVipUserList(VipUserDto vipUserDto)
    {
        Map<String, Object> params = vipUserDto.getParams();
        if (params != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 转换时间字符串为 Instant 类型的时间戳
            params.put("beginVipBeginTime", convertToEpochSecond((String) params.get("beginVipBeginTime"), formatter));
            params.put("endVipBeginTime", convertToEpochSecond((String) params.get("endVipBeginTime"), formatter));

            // 更新 params
            vipUserDto.setParams(params);
        }
        return gptUserMapper.selectVipUserList(vipUserDto);
    }

    // 转换时间字符串为 epoch 秒数
    private long convertToEpochSecond(String timeStr, DateTimeFormatter formatter) {
        if (timeStr == null || timeStr.isEmpty()) {
            return 0; // 或者根据业务需求返回默认值
        }
        LocalDateTime dateTime = LocalDateTime.parse(timeStr, formatter);
        return dateTime.atZone(ZoneId.of("Asia/Shanghai")) // 转换为北京时间（UTC+8）
                .toInstant() // 转为 Instant
                .getEpochSecond(); // 获取 epoch 秒数
    }

    public List<GptUser> getUserByLoginName(String loginName) {
        LambdaQueryWrapper<GptUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(GptUser::getLoginName, loginName);
        return gptUserMapper.selectList(lambdaQueryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserVipInfo(SubscriptionCurrent current, Long userId) {
//        long collectSize = 500;
//        if (VipType.standard.getValue().equals(current.getPlanLevel())) {
//            collectSize = 5000;
//        } else if (VipType.pro.getValue().equals(current.getPlanLevel())) {
//            // 50GB
//            collectSize = 50000;
//        }
        LambdaUpdateWrapper<GptUser> uw = new LambdaUpdateWrapper<>();
        uw.eq(GptUser::getId, userId);
        uw.set(GptUser::getVipType, current.getPlanLevel());
//        uw.set(User::getCurrentSubscription, current.getSubscriptionId());
        uw.set(GptUser::getPriceInterval, current.getPriceInterval());
//        uw.set(User::getVipBeginTime, startDate);
        uw.set(GptUser::getVipEndTime, current.getVipEndTime());
//        uw.set(User::getTotalCollectNum, collectSize);
//        uw.set(User::getTotalSize, totalSize);
        gptUserMapper.update(null, uw);
        updateUserVip(current, userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserVipInfoBatch(Map<Long, SubscriptionCurrent> userIdCurrentMap) {
        List<GptUserVipUpdateParam> updateList = userIdCurrentMap.entrySet().stream()
                .map(entry -> {
                    Long userId = entry.getKey();
                    SubscriptionCurrent current = entry.getValue();
                    GptUserVipUpdateParam param = new GptUserVipUpdateParam();
                    param.setUserId(userId);
                    param.setVipType(current.getPlanLevel());
                    param.setPriceInterval(current.getPriceInterval());
                    param.setVipEndTime(current.getVipEndTime());
                    return param;
                })
                .collect(Collectors.toList());

        gptUserMapper.batchUpdateUserVipInfo(updateList);
        updateUserVipBatch(userIdCurrentMap);
    }

    public CommUser findUserByUserId(Long userId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("accountInfo.userId").is(userId));
        return mongoTemplate.findOne(query, CommUser.class);
    }

    @Transactional
    public void updateUserVip(SubscriptionCurrent current, Long userId) {
        CommUser commUser = findUserByUserId(userId);
        if (Objects.isNull(commUser)) {
            return;
        }
        Update update = new Update();

        if (current.getVipBeginTime() != null) {
            update.set("accountInfo.vipBeginTime", current.getVipBeginTime());
        }
        if (current.getVipEndTime() != null) {
            update.set("accountInfo.vipEndTime", current.getVipEndTime());
        }
        if (current.getPlanLevel() != null) {
            update.set("accountInfo.planLevel", current.getPlanLevel());
        }
        if (current.getPriceInterval() != null) {
            update.set("accountInfo.priceInterval", current.getPriceInterval());
        }
        UpdateResult updateResult = mongoTemplate.updateFirst(
                new Query(Criteria.where("accountInfo.userId").is(userId)),
                update,
                CommUser.class
        );
        log.info("用户更新 updateUserVip 记录数：{}", updateResult.getModifiedCount());
    }

    @Transactional
    public void updateUserVipBatch(Map<Long, SubscriptionCurrent> userIdCurrentMap){
        if (Objects.isNull(userIdCurrentMap)) {
            return;
        }
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, CommUser.class);

        for(Map.Entry<Long, SubscriptionCurrent> entry : userIdCurrentMap.entrySet()){
            Long userId = entry.getKey();
            SubscriptionCurrent current = entry.getValue();
            Update update = new Update();
            if (current.getVipBeginTime() != null) {
                update.set("accountInfo.vipBeginTime", current.getVipBeginTime());
            }
            if (current.getVipEndTime() != null) {
                update.set("accountInfo.vipEndTime", current.getVipEndTime());
            }
            if (current.getPlanLevel() != null) {
                update.set("accountInfo.planLevel", current.getPlanLevel());
            }
            if (current.getPriceInterval() != null) {
                update.set("accountInfo.priceInterval", current.getPriceInterval());
            }
            // 更新 MongoDB 奖项等级
            operations.updateOne(
                    Query.query(Criteria.where("accountInfo.userId").is(userId)),
                    update
            );
            operations.execute();

        }

    }
}
