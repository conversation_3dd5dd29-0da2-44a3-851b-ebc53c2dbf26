package com.ai.admin.service;

import com.ai.admin.domain.GptUser;
import com.ai.admin.domain.dto.SendNotificationCountDTO;
import com.ai.admin.domain.dto.SendNotificationDTO;
import com.ai.admin.mapper.GptUserMapper;
import com.ai.admin.mapper.PushMessageContentMapper;
import com.ai.admin.mapper.PushMessageMapper;
import com.ai.admin.mapper.PushOperateRecordMapper;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.Platform;
import com.ai.constants.LogicParamsCons;
import com.ai.enums.*;
import com.ai.factory.SendNotificationFactory;
import com.ai.operation.domain.PushMessage;
import com.ai.operation.domain.PushMessageContent;
import com.ai.operation.domain.PushOperateRecord;
import com.ai.util.LogicUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description PUSH执行service
 */
@Slf4j
@Service
public class PushExecuteService {
    @Resource
    RedisCachePiclumen redisCachePiclumen;
    @Resource
    private PushMessageMapper pushMessageMapper;
    @Resource
    private PushMessageContentMapper pushMessageContentMapper;
    @Resource
    private GptUserMapper gptUserMapper;
    @Resource
    private PushOperateRecordMapper pushOperateRecordMapper;
    @Resource
    private SendNotificationFactory sendNotificationFactory;
    @Resource
    DingTalkAlert dingTalkAlert;

    /**
     * 轮询处理需要发送的消息
     */
    public void getAndExecute(int autoPush, LocalDateTime pushTime) {
        // 获取待处理的消息
        List<PushMessage> pushMessages = getAndUpdatePendingMessage(autoPush, pushTime);
        if (CollectionUtils.isEmpty(pushMessages)) {
            return;
        }

        pushMessages.forEach(this::getDetailAndExecute);
    }

    private List<PushMessage> getAndUpdatePendingMessage(int autoPush, LocalDateTime pushTime) {
        LambdaQueryWrapper<PushMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PushMessage::getStatus, PushStatusEnum.PENDING.getCode())
                .eq(PushMessage::getDailyAutoPush, autoPush);
        if (pushTime != null) {
            queryWrapper.le(PushMessage::getPushTime, LocalDateTime.now());
        }
        queryWrapper.orderByAsc(PushMessage::getId);

        List<PushMessage> pushMessages = pushMessageMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(pushMessages)) {
            return null;
        }

        PushMessage updateMessage = new PushMessage();
        updateMessage.setStatus(PushStatusEnum.PUSHING.getCode());
        pushMessageMapper.update(updateMessage,
                new LambdaUpdateWrapper<>(PushMessage.class).in(PushMessage::getId, pushMessages.stream().map(PushMessage::getId).collect(Collectors.toList())));

        return pushMessages;
    }

    private void getDetailAndExecute(PushMessage pushMessage) {
        // 2.获取消息内容
        List<PushMessageContent> pushMessageContentPOS = pushMessageContentMapper.selectList(
                new LambdaQueryWrapper<PushMessageContent>()
                        .eq(PushMessageContent::getPushMessageId, pushMessage.getId())
        );

        // 获取默认英语内容
        PushMessageContent defaultContent = pushMessageContentPOS.stream()
                .filter(content -> Objects.equals(LanguageCodeEnum.ENGLISH.getCode(), content.getLanguageCode()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("English content not found"));

        // 3.处理推送范围
        List<String> countryList = StringUtils.isBlank(pushMessage.getPushScope()) ? null : Arrays.asList(pushMessage.getPushScope().split(","));

        // 4.分批处理推送
        int BATCH_SIZE = (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.PUSH_SELECT_USER_BATCH_SIZE_KEY)).orElse(500);
        Long lastId = 0L;
        Object lastIdObj = redisCachePiclumen.get(LogicParamsCons.PUSH_LAST_ID_KEY_PREFIX + pushMessage.getId());
        if (lastIdObj != null) {
            lastId = Long.parseLong(lastIdObj.toString());
        }
        SendNotificationCountDTO sendNotificationCountDTO = new SendNotificationCountDTO();

        while (true) {
            List<GptUser> batch = getPushUserBatch(lastId, BATCH_SIZE);
            if (CollectionUtils.isEmpty(batch)) {
                redisCachePiclumen.deleteObject(LogicParamsCons.PUSH_LAST_ID_KEY_PREFIX + pushMessage.getId());
                break;
            }

            List<GptUser> filterUsers = filterCountryAndToken(batch, countryList);
            if (CollectionUtils.isNotEmpty(filterUsers)) {
                // 处理推送
                processPlatformGroups(filterUsers, defaultContent.getTitle(), defaultContent.getBody(),
                        pushMessage, pushMessageContentPOS, sendNotificationCountDTO);
            }

            lastId = batch.get(batch.size() - 1).getId();
            redisCachePiclumen.set(LogicParamsCons.PUSH_LAST_ID_KEY_PREFIX + pushMessage.getId(), lastId);
        }

        // 5.更新消息状态和记录
        updateMessageStatus(pushMessage, sendNotificationCountDTO);

        // 发送钉钉告警
        if (sendNotificationCountDTO.getSuccessCount() +
                sendNotificationCountDTO.getFailureCount() +
                sendNotificationCountDTO.getLimitCount() > 0) {
            sendDingTalkAlert(defaultContent.getTitle(), sendNotificationCountDTO);
        }
    }

    private List<GptUser> getPushUserBatch(Long lastId, int batchSize) {
        LambdaQueryWrapper<GptUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                GptUser::getId,
                GptUser::getRegistCountry,
//                GptUser::getAndroidFcmToken,
                GptUser::getIosFcmToken,
                GptUser::getLocalLang
        );
        queryWrapper
                .gt(GptUser::getId, lastId)
                .orderByAsc(GptUser::getId)
                .last("limit " + batchSize);

        return gptUserMapper.selectList(queryWrapper);
    }

    private List<GptUser> filterCountryAndToken(List<GptUser> users, List<String> countryList) {
        List<GptUser> filterUsers = null;
        for (GptUser user : users) {
            if (StringUtils.isBlank(user.getIosFcmToken()) && StringUtils.isBlank(user.getAndroidFcmToken())) {
                continue;
            }

            if (CollectionUtils.isNotEmpty(countryList)) {
                if (StringUtils.isBlank(user.getRegistCountry()) || !countryList.contains(user.getRegistCountry())) {
                    continue;
                }
            }

            if (filterUsers == null) {
                filterUsers = new ArrayList<>();
            }
            filterUsers.add(user);
        }

        return filterUsers;
    }

    private void processPlatformGroups(List<GptUser> batch, String defaultTitle, String defaultBody,
                                       PushMessage pushMessage, List<PushMessageContent> messageContents,
                                       SendNotificationCountDTO sendNotificationCountDTO) {
        Map<String, List<GptUser>> platformGroups = groupByPlatform(batch);

        platformGroups.forEach((platform, users) -> {
            try {
                Map<String, List<GptUser>> languageGroups = users.stream().collect(Collectors.groupingBy(user -> StringUtils.isNotBlank(user.getLocalLang()) ?
                        user.getLocalLang() : LanguageCodeEnum.ENGLISH.getCode()));

                languageGroups.forEach((languageCode, usersInLanguage) -> {
                    String finalTitle = defaultTitle;
                    String finalBody = defaultBody;

                    // 如果有多语言内容，查找对应语言的内容
                    if (messageContents != null) {
                        PushMessageContent languageContent = messageContents.stream()
                                .filter(content -> Objects.equals(languageCode, content.getLanguageCode()))
                                .findFirst()
                                .orElse(null);

                        if (languageContent != null) {
                            finalTitle = languageContent.getTitle();
                            finalBody = languageContent.getBody();
                        }
                    }

                    sendNotificationToUsers(usersInLanguage, platform, finalTitle, finalBody,
                            pushMessage, sendNotificationCountDTO);
                });
            } catch (Exception e) {
                log.error("Failed to send notifications for platform: {}, error: {}", platform, e.getMessage());
            }
        });
    }

    private Map<String, List<GptUser>> groupByPlatform(List<GptUser> users) {
        Map<String, List<GptUser>> platformGroups = new HashMap<>();
        for (GptUser user : users) {
            if (StringUtils.isNotBlank(user.getIosFcmToken())) {
                platformGroups.computeIfAbsent(Platform.IOS.getPlatformName(), k -> new ArrayList<>()).add(user);
            }

            if (StringUtils.isNotBlank(user.getAndroidFcmToken())) {
                platformGroups.computeIfAbsent(Platform.ANDROID.getPlatformName(), k -> new ArrayList<>()).add(user);
            }
        }

        return platformGroups;
    }

    private void updateMessageStatus(PushMessage pushMessage, SendNotificationCountDTO sendNotificationCountDTO) {
        // 记录推送数量
        PushOperateRecord pushOperateRecordPO = new PushOperateRecord();
        pushOperateRecordPO.setPushMessageId(pushMessage.getId());
        pushOperateRecordPO.setPushCount(sendNotificationCountDTO.getSuccessCount());
        pushOperateRecordMapper.insert(pushOperateRecordPO);

        // 更新消息状态为已推送
        PushMessage updateMessage = new PushMessage();
        updateMessage.setId(pushMessage.getId());
        updateMessage.setStatus(PushStatusEnum.PUSHED.getCode());
        pushMessageMapper.updateById(updateMessage);
    }

    private void sendNotificationToUsers(List<GptUser> users, String platform,
                                         String title, String body, PushMessage pushMessage,
                                         SendNotificationCountDTO sendNotificationCountDTO) {

        // 获取符合条件用户的FCM tokens
        List<String> fcmTokens;
        if (Platform.IOS.getPlatformName().equals(platform)) {
            fcmTokens = users.stream()
                    .map(GptUser::getIosFcmToken)
                    .filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
        } else {
            fcmTokens = users.stream()
                    .map(GptUser::getAndroidFcmToken)
                    .filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(fcmTokens)) {
            ISendNotification sendNotification = sendNotificationFactory.findSendNotification(
                    Platform.getByValue(platform),
                    NotificationTokenTypeEnum.FCM_TOKEN
            );

            if (sendNotification != null) {
                SendNotificationDTO.SendNotificationDTOBuilder builder = SendNotificationDTO.builder()
                        .targetTokenList(fcmTokens)
                        .pushType(PushTypeEnum.TEXT)
                        .title(title)
                        .body(body);

                // 如果是消息推送任务，添加额外参数
                if (pushMessage != null) {
                    builder.pushType(PushTypeEnum.fromValue(pushMessage.getPushType()))
                            .schemeEnum(SchemeEnum.getByToolType(pushMessage.getSchemeUrl()))
                            .messageId(pushMessage.getId())
                            .imageUrl(pushMessage.getPicUrl())
                            .thumbnailUrl(pushMessage.getThumbnailUrl());
                }

                sendNotification.sendNotification(builder.build(), sendNotificationCountDTO);
            }
        }
    }

    private void sendDingTalkAlert(String title, SendNotificationCountDTO sendNotificationCountDTO) {
        String dingTalkMessage = String.format(
                "Push message Title: %s, Success Count: %d, Failure Count: %d, Limit Count: %d, TimeElapsed: %dms",
                title,
                sendNotificationCountDTO.getSuccessCount(),
                sendNotificationCountDTO.getFailureCount(),
                sendNotificationCountDTO.getLimitCount(),
                sendNotificationCountDTO.getTimeElapsed()
        );
        //发送告警信息
        dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                AlarmEnum.AlarmSourceEnum.PUSH.getDescription(),
                dingTalkMessage,
                null,
                null));
    }
}
