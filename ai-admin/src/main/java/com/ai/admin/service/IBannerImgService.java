package com.ai.admin.service;

import com.ai.admin.domain.BannerImg;
import com.ai.admin.domain.vo.BannerImgVO;
import com.ai.common.core.domain.model.LoginUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 社区首页banner图配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface IBannerImgService extends IService<BannerImg> {
    /**
     * 查询社区首页banner图配置
     * 
     * @param id 社区首页banner图配置主键
     * @return 社区首页banner图配置
     */
    BannerImgVO selectBannerImgById(Long id);

    /**
     * 查询社区首页banner图配置列表
     *
     * @param imgUrl 图片链接地址
     * @param sort 图片顺序
     * @param status 图片状态
     * @return 社区首页banner图配置集合
     */
    List<BannerImgVO> selectBannerImgList(String imgUrl, Integer sort, Integer status);

    /**
     * 新增社区首页banner图配置
     * 
     * @param bannerImgVO 社区首页banner图配置
     * @return 结果
     */
    int insertBannerImg(BannerImgVO bannerImgVO, LoginUser loginUser);

    /**
     * 修改社区首页banner图配置
     * 
     * @param bannerImgVO 社区首页banner图配置
     * @return 结果
     */
    int updateBannerImg(BannerImgVO bannerImgVO, LoginUser loginUser);

    /**
     * 删除社区首页banner图配置
     *
     * @param id 需要删除的社区首页banner图配置主键
     * @return 结果
     */
    int deleteBannerImgById(Long id, LoginUser loginUser);
}
