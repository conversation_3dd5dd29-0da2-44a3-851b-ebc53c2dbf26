package com.ai.admin.service.impl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import com.ai.admin.domain.GptPromptRecord;
import com.ai.admin.domain.vo.PromptFileRanking;
import com.ai.admin.mapper.GptPromptRecordMapper;
import com.ai.admin.mapper.GptUserMapper;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCache;
import com.ai.common.enums.PublicType;
import com.ai.common.enums.ReviewStatus;
import com.ai.common.utils.DateUtils;
import com.ai.common.utils.JsonUtils;
import com.ai.common.utils.StringUtils;
import com.ai.common.utils.dto.ModelInformation;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.domain.GptPublicFileReview;
import com.ai.operation.mapper.GptPublicFileReviewMapper;
import com.ai.purge.vo.PurgeQueryParamVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Objects;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.admin.mapper.GptPromptFileMapper;
import com.ai.admin.domain.GptPromptFile;
import com.ai.admin.service.IGptPromptFileService;

import javax.annotation.Resource;

/**
 * 任务图片对应关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
@Slf4j
public class GptPromptFileServiceImpl extends ServiceImpl<GptPromptFileMapper, GptPromptFile> implements IGptPromptFileService
{
    @Autowired
    private GptPromptFileMapper gptPromptFileMapper;

    @Autowired
    private RedisCache redisCache;

    @Resource
    private GptUserMapper gptUserMapper;


    @Autowired
    private GptPublicFileReviewMapper gptPublicFileReviewMapper;

    @Autowired
    private GptPromptRecordMapper gptPromptRecordMapper;

    /**
     * 查询任务图片对应关系
     * 
     * @param id 任务图片对应关系主键
     * @return 任务图片对应关系
     */
    @Override
    public GptPromptFile selectGptPromptFileById(Long id,String loginName)
    {
        LambdaQueryWrapper<GptPromptFile> ul = new LambdaQueryWrapper<>();
        ul.eq(GptPromptFile::getLoginName, loginName);
        ul.eq(GptPromptFile::getId, id);
        return gptPromptFileMapper.selectOne(ul);
    }

    /**
     * 查询任务图片对应关系列表
     * 
     * @param gptPromptFile 任务图片对应关系
     * @return 任务图片对应关系
     */
    @Override
    public List<GptPromptFile> selectGptPromptFileList(GptPromptFile gptPromptFile)
    {
        return gptPromptFileMapper.selectGptPromptFileList(gptPromptFile);
    }

    /**
     * 新增任务图片对应关系
     * 
     * @param gptPromptFile 任务图片对应关系
     * @return 结果
     */
    @Override
    public int insertGptPromptFile(GptPromptFile gptPromptFile)
    {
        gptPromptFile.setCreateTime(LocalDateTime.now());
        return gptPromptFileMapper.insertGptPromptFile(gptPromptFile);
    }

    /**
     * 修改任务图片对应关系
     * 
     * @param gptPromptFile 任务图片对应关系
     * @return 结果
     */
    @Override
    public int updateGptPromptFile(GptPromptFile gptPromptFile)
    {
        gptPromptFile.setUpdateTime(LocalDateTime.now());
        return gptPromptFileMapper.updateGptPromptFile(gptPromptFile);
    }

    /**
     * 批量删除任务图片对应关系
     * 
     * @param ids 需要删除的任务图片对应关系主键
     * @return 结果
     */
    @Override
    public int deleteGptPromptFileByIds(Long[] ids,String[] loginNames)
    {
        LambdaQueryWrapper<GptPromptFile> ul = new LambdaQueryWrapper<>();
        ul.in(GptPromptFile::getLoginName, loginNames);
        ul.in(GptPromptFile::getId, ids);
        return gptPromptFileMapper.delete(ul);
    }

    /**
     * 删除任务图片对应关系信息
     * 
     * @param id 任务图片对应关系主键
     * @return 结果
     */
    @Override
    public int deleteGptPromptFileById(Long id)
    {
        return gptPromptFileMapper.deleteGptPromptFileById(id);
    }


    /**
     * 首页获取昨日生成图片用户排名(暂定前今日方法后续可能改回来)
     *
     * @return 结果
     */
    @Override
    public List<PromptFileRanking> getPromptFileRanking()
    {
//        // 获取当前时间
//        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
//        // 获取当天凌晨时间和当前时间
//        LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
//        LocalDateTime endOfDay = now;
//
//        // 格式化时间
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        String startDate = startOfDay.format(formatter);
//        String endDate = endOfDay.format(formatter);
//        // 调用 mapper 方法获取数据
//        List<PromptFileRanking> promptFileRanking = gptPromptFileMapper.getPromptFileRanking(startDate, endDate);
//        return promptFileRanking;
        // 定义日期格式
        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        // 计算昨日的日期
        LocalDate yesterday = now.toLocalDate().minusDays(1);
        String formatDate = yesterday.format(formatterDate);
        String listFromRedis = redisCache.getCacheMapValue(LogicParamsCons.PROMPT_File_RANKING ,formatDate);
        if(StringUtils.isNotBlank(listFromRedis)){
            try {
                return JsonUtils.writeToList(listFromRedis, PromptFileRanking.class);
            } catch (JsonProcessingException e) {
                log.info("redis缓存失败",e);
                return null;            }
        }
        // 获取昨日的开始时间和结束时间
        LocalDateTime startOfDay = yesterday.atStartOfDay(); // 昨日凌晨00:00:00
        LocalDateTime endOfDay = yesterday.atTime(LocalTime.MAX); // 昨日23:59:59.999999999
        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startDate = startOfDay.format(formatter);
        String endDate = endOfDay.format(formatter);
        // 调用 mapper 方法获取数据
        List<PromptFileRanking> promptFileRanking = gptPromptFileMapper.getPromptFileRanking(startDate, endDate);
        Collections.reverse(promptFileRanking);
        if (!CollUtil.isEmpty(promptFileRanking)){
            try {
                redisCache.setCacheMapValue(LogicParamsCons.PROMPT_File_RANKING,  formatDate, JsonUtils.writeToString(promptFileRanking));
            } catch (JsonProcessingException e) {
                log.info("redis缓存失败",e);
                return null;
            }
        }

        return promptFileRanking;
    }

    @Override
    public List<GptPromptFile> queryFileListByCondition(PurgeQueryParamVo paramVo) {
        return gptPromptFileMapper.queryFileListByCondition(paramVo);
    }

    @Override
    public List<GptPromptFile> queryFileByDbNameWithPromptIds(String dbName, Collection<String> promptIds) {
        return gptPromptFileMapper.queryFileByDbNameWithPromptIds(dbName, promptIds);
    }

    @Override
    public int deleteGptPromptFileByIdsWithDbName(String dbName, List<Long> ids) {
        return gptPromptFileMapper.deleteGptPromptFileByIdsWithDbName(dbName, ids);
    }

    @Override
    public void insertTempIds(String temDbName, String dbName, List<String> promptIds) {
        gptPromptFileMapper.insertTempIds(temDbName,dbName, promptIds);
    }

    @Override
    public void deleteFileByJoinTemp(String temDbName, String fileDbName) {
        // gptPromptFileMapper.insertToTemp(fileDbName);
        gptPromptFileMapper.deleteFileByJoinTemp(temDbName, fileDbName);
        gptPromptFileMapper.truncateTemp(temDbName);
    }

    @Override
    public List<GptPromptFile> queryDeleteFileDataBatch(String fileDbNum, String startTime, String endTime, int limit) {
        return gptPromptFileMapper.queryDeleteFileDataBatch(fileDbNum, startTime, endTime, limit);
    }


    @Override
    public int physicRemoveDeleteDataBatch(String fileDbNum,  List<Long> ids) {
        return gptPromptFileMapper.physicRemoveDeleteDataBatch(fileDbNum, ids);
    }

    @Override
    public List<GptPromptFile> queryDeleteFileDataBatchWithVip(String fileDbNum, String startTime, Integer vipExpireDays, int limit) {
        return gptPromptFileMapper.queryDeleteFileDataBatchWithVip(fileDbNum, startTime, vipExpireDays, limit);
    }

    @Override
    public Long sumTotalImgNum(){
        return gptUserMapper.sumTotalImgNum();
     }

    @Override
    public AjaxResult resumingAudit(Long id, String loginName){

//        // 恢复审核数据
//        LambdaQueryWrapper<GptPublicFileReview> pfq = new LambdaQueryWrapper();
//        pfq.eq(GptPublicFileReview::getFileId, id);
//
//        GptPublicFileReview gptPublicFileReview = gptPublicFileReviewMapper.selectOne(pfq);
//
//        // 如果找到审核记录恢复 为审核中 没有则恢复为待审核
//        if (!Objects.isNull(gptPublicFileReview)){
//            // 恢复为审核中
//            gptPublicFileReview.setReviewStatus(ReviewStatus.review.getValue());
//            gptPublicFileReview.setDel(0);
//            gptPublicFileReviewMapper.updateById(gptPublicFileReview);
//
//            // 原图状态也恢复
//            LambdaUpdateWrapper<GptPromptFile> fuw = new LambdaUpdateWrapper<>();
//            fuw.eq(GptPromptFile::getLoginName,loginName);
//            fuw.eq(GptPromptFile::getId, id);
//            fuw.set(GptPromptFile::getIsPublic, PublicType.review.getValue());
//            baseMapper.update(null,fuw);
//            return AjaxResult.success("恢复成功 恢复为审核中");
//        }else {
            // 恢复为为公开 (这一版改为只能恢复为未提交)
            // 原图状态也恢复
            GptPromptFile gptPromptFile = selectGptPromptFileById(id, loginName);
            if (Objects.isNull(gptPromptFile)){
                return AjaxResult.error("图片已被用户删除 恢复失败");
            }

            LambdaQueryWrapper<GptPromptRecord> prw = new LambdaQueryWrapper<>();
            prw.eq(GptPromptRecord::getPromptId, gptPromptFile.getPromptId());
            prw.eq(GptPromptRecord::getLoginName, loginName);
            GptPromptRecord gptPromptRecord = gptPromptRecordMapper.selectOne(prw);
            if (Objects.isNull(gptPromptRecord)){
                return AjaxResult.error("图片已被用户删除 恢复失败");
            }
            LambdaUpdateWrapper<GptPromptFile> fuw = new LambdaUpdateWrapper<>();
            fuw.eq(GptPromptFile::getLoginName,loginName);
            fuw.eq(GptPromptFile::getId, id);
            fuw.set(GptPromptFile::getIsPublic, PublicType.undisclosed.getValue());
            baseMapper.update(null,fuw);
            return AjaxResult.success("恢复成功 恢复为未公开");
//        }

    }

}
