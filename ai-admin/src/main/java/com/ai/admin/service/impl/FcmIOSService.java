package com.ai.admin.service.impl;

import com.ai.admin.domain.dto.SendNotificationCountDTO;
import com.ai.admin.domain.dto.SendNotificationDTO;
import com.ai.admin.service.ISendNotification;
import com.ai.common.utils.JsonUtils;
import com.ai.config.PushNotifyProperties;
import com.ai.enums.PushTypeEnum;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FcmIOSService implements ISendNotification {

    private final OkHttpClient fcmClient = new OkHttpClient.Builder()
            .protocols(Arrays.asList(Protocol.HTTP_2, Protocol.HTTP_1_1))
            .connectionPool(new ConnectionPool(32, 5, TimeUnit.MINUTES))
            .connectTimeout(30, TimeUnit.SECONDS)
            .build();

    @Resource
    private PushNotifyProperties pushNotifyProperties;
    @Resource(name = "threadPoolTaskExecutor")
    private ThreadPoolTaskExecutor sendNotificationTask;

    private String fcmIOSAccessToken;
    private GoogleCredentials googleCredentials;
    private static final long TOKEN_REFRESH_INTERVAL = 5 * 60 * 1000; // 5分钟刷新一次
    private long lastTokenRefreshTime;

    @PostConstruct
    public void init() {
        if (pushNotifyProperties == null) {
            throw new IllegalStateException("PushNotifyProperties is not configured");
        }

        if (StringUtils.isBlank(pushNotifyProperties.getFcmIOSFilePath())) {
            throw new IllegalStateException("FCM iOS file path is not configured");
        }

        if (StringUtils.isBlank(pushNotifyProperties.getFcmMessagingScope())) {
            throw new IllegalStateException("FCM messaging scope is not configured");
        }

        org.springframework.core.io.Resource resource = new ClassPathResource(pushNotifyProperties.getFcmIOSFilePath());
        if (!resource.exists()) {
            throw new IllegalStateException("FCM iOS credentials file not found: " + pushNotifyProperties.getFcmIOSFilePath());
        }

        try (var inputStream = resource.getInputStream()) {
            googleCredentials = GoogleCredentials
                    .fromStream(inputStream)
                    .createScoped(Collections.singleton(pushNotifyProperties.getFcmMessagingScope()));

            refreshToken();
            log.info("FCM iOS service initialized successfully");
        } catch (IOException e) {
            log.error("Failed to initialize FCM iOS service", e);
            throw new IllegalStateException("Failed to initialize FCM iOS service: " + e.getMessage(), e);
        }
    }

    private synchronized void refreshToken() {
        try {
            googleCredentials.refreshIfExpired();
            fcmIOSAccessToken = googleCredentials.getAccessToken().getTokenValue();
            lastTokenRefreshTime = System.currentTimeMillis();

            if (StringUtils.isEmpty(fcmIOSAccessToken)) {
                throw new IllegalStateException("Failed to obtain FCM iOS access token");
            }

            log.debug("FCM iOS access token refreshed successfully");
        } catch (IOException e) {
            log.error("Failed to refresh FCM iOS access token", e);
            throw new IllegalStateException("Failed to refresh FCM iOS access token: " + e.getMessage(), e);
        }
    }

    private synchronized String getAccessToken() {
        // 如果距离上次刷新超过29分钟，则刷新token
        if (System.currentTimeMillis() - lastTokenRefreshTime > TOKEN_REFRESH_INTERVAL) {
            refreshToken();
        }
        return fcmIOSAccessToken;
    }

    /**
     * 发送通知
     *
     * @param sendNotificationDTO sendNotificationDTO
     */
    @Override
    public void sendNotification(SendNotificationDTO sendNotificationDTO, SendNotificationCountDTO sendNotificationCountDTO) {
        // 批量处理推送请求
        List<Request> requests = sendNotificationDTO.getTargetTokenList().stream()
                .filter(StringUtils::isNotBlank)
                .map(token -> {
                    JsonObject message = buildMessage(token, sendNotificationDTO);
                    return new Request.Builder()
                            .url(pushNotifyProperties.getFcmIOSEndPoint())
                            .headers(Headers.of("Authorization", "Bearer " + getAccessToken()))
                            .post(RequestBody.create(
                                    MediaType.get("application/json; charset=utf-8"),
                                    message.toString()
                            ))
                            .build();
                })
                .collect(Collectors.toList());

        // 使用HTTP/2多路复用并发发送请求
        CompletableFuture.allOf(
                requests.stream()
                        .map(request -> CompletableFuture.runAsync(() -> {
                            try (Response response = fcmClient.newCall(request).execute()) {
                                if (response.body() != null) {
                                    JsonNode resp = JsonUtils.writeStringToJsonNode(response.body().string());
                                    handleResponse(resp, sendNotificationCountDTO);
                                }
                            } catch (IOException e) {
                                log.error("FCM iOS push failed", e);
                                sendNotificationCountDTO.incrementFailureCount();
                            }
                        }, sendNotificationTask))
                        .toArray(CompletableFuture[]::new)
        ).join();
    }

    private void handleResponse(JsonNode resp, SendNotificationCountDTO sendNotificationCountDTO) {
        log.debug("FCM iOS push response: {}", resp);
        if (resp.has("name")) {
            sendNotificationCountDTO.incrementSuccessCount();
        } else {
            if (resp.has("error")) {
                String errorCode = resp.path("error").path("code").asText();
                String errorMessage = resp.path("error").path("message").asText();

                // 检查是否是限流错误
                if ("429".equals(errorCode)
                        || "RESOURCE_EXHAUSTED".equals(errorCode)
                        || (errorMessage != null && errorMessage.contains("Quota exceeded"))) {
                    sendNotificationCountDTO.incrementLimitCount();
                    log.warn("FCM iOS push rate limited, code: {}, message: {}", errorCode, errorMessage);
                } else {
                    sendNotificationCountDTO.incrementFailureCount();
                    log.error("FCM iOS push failed, code: {}, message: {}", errorCode, errorMessage);
                }
            } else {
                sendNotificationCountDTO.incrementFailureCount();
                log.error("FCM iOS push failed with unexpected response: {}", resp);
            }
        }
    }


    private static JsonObject buildMessage(String token, SendNotificationDTO notificationDTO) {
        JsonObject message = new JsonObject();
        message.addProperty("token", token);

        // 基本通知内容
        JsonObject notification = new JsonObject();
        notification.addProperty("title", notificationDTO.getTitle());
        notification.addProperty("body", notificationDTO.getBody());
        message.add("notification", notification);

        // APNS 特定配置
        JsonObject apns = new JsonObject();
        JsonObject payload = new JsonObject();
        JsonObject aps = new JsonObject();
        JsonObject alert = new JsonObject();

        alert.addProperty("title", notificationDTO.getTitle());
        alert.addProperty("body", notificationDTO.getBody());
        aps.add("alert", alert);
        aps.addProperty("sound", "default");
        aps.addProperty("mutable-content", 1);

        // 根据推送类型设置不同的 category
        String category;
        if (notificationDTO.getPushType() == PushTypeEnum.IMAGE) {
            category = "IMAGE";
        } else {
            category = "GENERAL";
        }
        aps.addProperty("category", category);

        // 添加额外数据
        if (notificationDTO.getMessageId() != null) {
            aps.addProperty("messageId", notificationDTO.getMessageId().toString());
        }
        if (StringUtils.isNotEmpty(notificationDTO.getImageUrl())) {
            aps.addProperty("image", notificationDTO.getImageUrl());
        }
        if (notificationDTO.getSchemeEnum() != null) {
            aps.addProperty("deep_link", notificationDTO.getSchemeEnum().getIosScheme());
        }

        payload.add("aps", aps);
        apns.add("payload", payload);
        message.add("apns", apns);

        JsonObject requestBody = new JsonObject();
        requestBody.add("message", message);
        return requestBody;
    }


}
