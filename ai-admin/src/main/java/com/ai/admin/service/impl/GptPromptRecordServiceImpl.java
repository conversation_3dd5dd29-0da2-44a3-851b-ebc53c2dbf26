package com.ai.admin.service.impl;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;

import com.ai.admin.domain.PromptRecordExcel;
import com.ai.admin.domain.dto.GenGenericPara;
import com.ai.admin.domain.dto.GptPromptRecordPageDto;
import com.ai.admin.domain.dto.UserProfileDto;
import com.ai.admin.domain.vo.UserProfileVo;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.utils.JsonUtils;
import com.ai.common.utils.PythonApiUtils;
import com.ai.common.utils.StringUtils;
import com.ai.common.utils.dto.ModelInformation;
import com.ai.constants.CommandPromptParameters;
import com.ai.constants.LogicParamsCons;
import com.ai.purge.vo.PurgeQueryParamVo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import com.ai.admin.mapper.GptPromptRecordMapper;
import com.ai.admin.domain.GptPromptRecord;
import com.ai.admin.service.IGptPromptRecordService;
import org.springframework.util.CollectionUtils;


/**
 * 用户生图任务记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@Slf4j
@Service
public class GptPromptRecordServiceImpl extends ServiceImpl<GptPromptRecordMapper, GptPromptRecord> implements IGptPromptRecordService
{

    @Value("${sendcloud.api.key}")
    String apiKey;

    @Value("${sendcloud.api.user}")
    String apiUser;

    @Value("${sendcloud.Url}")
    String sendcloudUrl;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    private GptPromptRecordMapper gptPromptRecordMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;

    @Autowired
    private PythonApiUtils pythonApiUtils;


    /**
     * 查询用户生图任务记录
     * 
     * @param id 用户生图任务记录主键
     * @return 用户生图任务记录
     */
    @Override
    public GptPromptRecord selectGptPromptRecordById(Long id,String loginName)
    {
        LambdaQueryWrapper<GptPromptRecord> ul = new LambdaQueryWrapper<>();
        ul.eq(GptPromptRecord::getLoginName, loginName);
        ul.eq(GptPromptRecord::getId, id);
        GptPromptRecord gptPromptRecord = gptPromptRecordMapper.selectOne(ul);
        return gptPromptRecord;
//        return gptPromptRecordMapper.selectGptPromptRecordById(id,loginName);
    }

    /**
     * 查询用户生图任务记录列表
     * 
     * @param gptPromptRecord 用户生图任务记录
     * @return 用户生图任务记录
     */
    @Override
    public List<GptPromptRecord> selectGptPromptRecordList(GptPromptRecordPageDto gptPromptRecord)
    {
        List<GptPromptRecord> gptPromptRecords = gptPromptRecordMapper.selectGptPromptRecordList(gptPromptRecord);
        return gptPromptRecords;
    }

    /**
     * 新增用户生图任务记录
     * 
     * @param gptPromptRecord 用户生图任务记录
     * @return 结果
     */
    @Override
    public int insertGptPromptRecord(GptPromptRecord gptPromptRecord)
    {
        gptPromptRecord.setCreateTime(LocalDateTime.now());
        return gptPromptRecordMapper.insertGptPromptRecord(gptPromptRecord);
    }

    /**
     * 修改用户生图任务记录
     * 
     * @param gptPromptRecord 用户生图任务记录
     * @return 结果
     */
    @Override
    public int updateGptPromptRecord(GptPromptRecord gptPromptRecord)
    {
        gptPromptRecord.setUpdateTime(LocalDateTime.now());
        return gptPromptRecordMapper.updateGptPromptRecord(gptPromptRecord);
    }

    /**
     * 批量删除用户生图任务记录
     * 
     * @param ids 需要删除的用户生图任务记录主键
     * @return 结果
     */
    @Override
    public int deleteGptPromptRecordByIds(Long[] ids,String[] loginNames)
    {
        LambdaQueryWrapper<GptPromptRecord> ul = new LambdaQueryWrapper<>();
        ul.in(GptPromptRecord::getLoginName, loginNames);
        ul.in(GptPromptRecord::getId, ids);
        return gptPromptRecordMapper.delete(ul);
    }

    /**
     * 删除用户生图任务记录信息
     * 
     * @param id 用户生图任务记录主键
     * @return 结果
     */
    @Override
    public int deleteGptPromptRecordById(Long id)
    {
        return gptPromptRecordMapper.deleteGptPromptRecordById(id);
    }

    /**
     * 查询用户生图报告
     *
     * @return 结果
     */
    @Override
    public List<UserProfileVo> getUserProfile(UserProfileDto userProfileDto) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        if (userProfileDto.getCreateTime() == null
                && userProfileDto.getBeginGenCreateTime() == null
                && userProfileDto.getEndGenCreateTime() == null) {
            // 如果 createTime、beginGenCreateTime 和 endGenCreateTime 都为空
            // 设置昨天的零点为开始时间
            userProfileDto.setBeginGenCreateTime(now.minusDays(1).with(LocalTime.MIN));
            // 设置昨天的23:59:59为结束时间
            userProfileDto.setEndGenCreateTime(now.minusDays(1).with(LocalTime.MAX));
        } else if (userProfileDto.getCreateTime() != null) {
            // 如果 createTime 不为空
            // 设置 createTime 的零点为开始时间
            userProfileDto.setBeginGenCreateTime(userProfileDto.getCreateTime().atStartOfDay());
            // 设置 createTime 的 23:59:59 为结束时间
            userProfileDto.setEndGenCreateTime(userProfileDto.getCreateTime().atTime(LocalTime.MAX));
        }

        // 返回查询结果
        return gptPromptRecordMapper.selectGptPromptRecordsWithFiles(userProfileDto);
    }

    @Override
    public List<GptPromptRecord> queryRecordListByCondition(PurgeQueryParamVo paramVo) {
       return  gptPromptRecordMapper.queryRecordListByCondition(paramVo);
    }

    @Override
    public int deleteGptPromptRecordByIdsWithDbName(String dbName, List<Long> ids) {
        return gptPromptRecordMapper.deleteGptPromptRecordByIdsWithDbName(dbName, ids);
    }

    @Override
    public int physicRemoveDeleteDataBatch(String recordDbNum, String endTime, int limit) {
        return gptPromptRecordMapper.physicRemoveDeleteDataBatch(recordDbNum,  endTime, limit);
    }

    @Override
    public Boolean genInfoExport(String loginName, String email) {
        try {
            dealGenInfoExport(loginName,email);
            return Boolean.TRUE;
        }catch (Exception e){
            log.error("genInfoExport error: ", e);
            return Boolean.FALSE;
        }

    }


    void dealGenInfoExport(String loginName,String email) {
        try {
            List<GptPromptRecord> allRecords = new ArrayList<>();
            Long lastId = null;
            boolean hasMoreData = true;

            // Step 2: Perform cursor pagination based on ID
            while (hasMoreData) {
                LambdaQueryWrapper<GptPromptRecord> qw = new LambdaQueryWrapper<>();
//                qw.eq(GptPromptRecord::getDel, Boolean.FALSE);
                qw.eq(GptPromptRecord::getLoginName, loginName);
                qw.le(GptPromptRecord::getCreateTime, "2025-02-26 08:00:00");
                qw.orderByDesc(GptPromptRecord::getId);
                if (lastId != null) {
                    qw.lt(GptPromptRecord::getId, lastId);
                }
                qw.last("limit 1000");

                List<GptPromptRecord> records = gptPromptRecordMapper.selectList(qw);

                if (records.size() < 1000) {
                    hasMoreData = false;
                } else {
                    lastId = records.get(records.size() - 1).getId();
                }
                //组装数据
                allRecords.addAll(records);
            }

            // 如果超过三万条数据则分批量发送
            List<List<GptPromptRecord>> partition = Lists.partition(allRecords, 30000);
            for (List<GptPromptRecord> list : partition) {
                dealGenInfoExcel(list, loginName, email);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 组装excel数据
     *
     * @param allRecords
     * @param loginName
     * @param email
     */
    void dealGenInfoExcel(List<GptPromptRecord> allRecords, String loginName, String email) {
        if (CollectionUtils.isEmpty(allRecords)) {
            return;
        }

        try {
            // Step 1: Prepare the ByteArrayOutputStream for writing the Excel file
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            List<ModelInformation.ModelAbout> modelAbouts=listModels();
            List<PromptRecordExcel> excelData = allRecords.stream().map(record -> {
                PromptRecordExcel promptRecordExcel = new PromptRecordExcel();

                // Convert createTime from Beijing Time (UTC+8) to GMT (UTC)
                ZonedDateTime beijingTime = record.getCreateTime().atZone(ZoneOffset.ofHours(8));  // Assuming createTime is in Beijing time
                ZonedDateTime gmtTime = beijingTime.withZoneSameInstant(ZoneOffset.UTC);  // Convert to GMT (UTC)
                promptRecordExcel.setCreateTime(gmtTime.toLocalDateTime());  // Set to LocalDateTime if needed

                promptRecordExcel.setLoginName(record.getLoginName());
                try {
                    promptRecordExcel.setParams(generateParameters(record, modelAbouts));
                } catch (IOException e) {
                    promptRecordExcel.setParams("");
                }
                return promptRecordExcel;
            }).collect(Collectors.toList());

            // Step 2: Use EasyExcel to write data
            EasyExcel.write(byteArrayOutputStream, PromptRecordExcel.class)
                    .sheet("Prompt Records")
                    .registerWriteHandler(getHorizontalCellStyleStrategy((short) 12)) // Custom cell style handler

                    .doWrite(excelData);

            // Step 3: Get the byte array
            byte[] byteArray = byteArrayOutputStream.toByteArray();

            // Step 4: Send the email with the attachment
            sendEmailWithAttachment(email, byteArray);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 单元格样式策略
     */
    public static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy(Short fontHeightInPoints) {
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.NONE);

        // 配置字体
        WriteFont contentWriteFont = new WriteFont();
        // 字体
        contentWriteFont.setFontName("宋体");
        // 字体大小
        contentWriteFont.setFontHeightInPoints(fontHeightInPoints);
        // 设置加粗
        contentWriteFont.setBold(false);
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 【水平居中需要使用以下两行】
        // 设置文字左右居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置文字上下居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置 自动换行
        contentWriteCellStyle.setWrapped(true);

        // 样式策略
        return new HorizontalCellStyleStrategy(null, contentWriteCellStyle);
    }

    /**
     * 发送用户提示词邮件，并带上附件
     *
     * @param email
     * @param byteArray
     */
    void sendEmailWithAttachment(String email, byte[] byteArray) throws IOException {
        String from = "<EMAIL>";
        String fromName = "<EMAIL>";
        String to = email;
        String subject = "Notification Regarding Restoration of Your Previous Image Generation Prompts and Parameters.";

        //默认邮件模板
        String templatesPath = "classpath:templates/autoReplyTemplate.html";

        Resource resource = resourceLoader.getResource(templatesPath);
        InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
        StringBuilder htmlBuilder = new StringBuilder();
        int c;
        while ((c = reader.read()) != -1) {
            htmlBuilder.append((char) c);
        }
        String html = htmlBuilder.toString();

        // 创建临时文件并写入字节数组
        File tempFile = null;
        try {
            tempFile = File.createTempFile("tempFile_" + System.currentTimeMillis(), ".tmp");
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(byteArray);
            }

            // 使用 MultipartEntityBuilder 添加附件
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPost httPost = new HttpPost(sendcloudUrl);

                MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                builder.addPart("apiUser", new StringBody(apiUser, StandardCharsets.UTF_8));
                builder.addPart("apiKey", new StringBody(apiKey, StandardCharsets.UTF_8));
                builder.addPart("from", new StringBody(from, StandardCharsets.UTF_8));
                builder.addPart("fromName", new StringBody(fromName, StandardCharsets.UTF_8));
                builder.addPart("to", new StringBody(to, StandardCharsets.UTF_8));
                builder.addPart("subject", new StringBody(subject, StandardCharsets.UTF_8));
                builder.addPart("html", new StringBody(html, StandardCharsets.UTF_8));


                // 将文件和文件名一起作为附件
//                FileBody fileBody = new FileBody(tempFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "prompt_records.xlsx");
                FileBody fileBody = new FileBody(tempFile, ContentType.create("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"), "prompt_records.xlsx");
                builder.addPart("attachments", fileBody);

                // 设置请求体
                httPost.setEntity(builder.build());

                // 发送邮件请求
                HttpResponse response = httpClient.execute(httPost);
                // 处理响应
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) { // 正常返回
                    String result = EntityUtils.toString(response.getEntity());
                    log.info("邮件发送成功，接收邮箱为：{}", to);
                } else {
                    log.error("邮件发送失败，接收邮箱为：{}", to);
                    throw new RuntimeException();
                }
            } catch (IOException e) {
                log.error("发送邮件时发生错误", e);
                throw e;  // Rethrow exception to ensure proper handling
            }
        } catch (IOException e) {
            log.error("文件写入或临时文件创建失败", e);
            throw e;  // Ensure the exception is propagated to handle at higher level
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.info("临时文件已删除：{}", tempFile.getAbsolutePath());
                } else {
                    log.error("临时文件删除失败：{}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    private String generateParameters(GptPromptRecord promptRecord,List<ModelInformation.ModelAbout> modelAbouts) throws IOException {
        // 使用 StringBuilder 代替字符串拼接
        StringBuilder parameters = new StringBuilder();

        // 基本参数拼接
        if (!StringUtils.isNull(promptRecord.getPrompt())) {
            parameters.append(promptRecord.getPrompt().replace("score_9, score_8_up, score_7_up, score_6_up, score_5_up, score_4_up,", ""));
        }
        parameters.append(CommandPromptParameters.PICLUMEN)
                .append(CommandPromptParameters.ASPECT)
                .append(promptRecord.getAspectRatio()
                        .replace(" * ", ":"));

        // 处理 negative prompt
        if (!StringUtils.isNull(promptRecord.getNegativePrompt())) {
            parameters.append(CommandPromptParameters.NEGATIVE_PROMPT).append("\"").append(promptRecord.getNegativePrompt()).append("\"");
        } else {
            parameters.append(CommandPromptParameters.NEGATIVE_PROMPT).append("\"\"");
        }

        // 处理 genInfo 参数
        GenGenericPara genGenericPara = JsonUtils.fromString(promptRecord.getGenInfo(), GenGenericPara.class);

        //判断生成信息是否为空 不为空 则写入相应的信息进入元数据
        if (!Objects.isNull(genGenericPara)) {

            if (!Objects.isNull(genGenericPara.getCfg())) {
                parameters.append(CommandPromptParameters.GUIDANCE).append(genGenericPara.getCfg());
            }

            if (!Objects.isNull(genGenericPara.getSteps())) {
                parameters.append(CommandPromptParameters.STEPS).append(genGenericPara.getSteps());
            }

            if (!Objects.isNull(genGenericPara.getSeed())) {
                parameters.append(CommandPromptParameters.SEED).append(genGenericPara.getSeed());
            }
        }

        // 添加批量大小
        parameters.append(CommandPromptParameters.BATCH_SIZE).append(promptRecord.getBatchSize());

        // 处理模型信息
        for (ModelInformation.ModelAbout model : modelAbouts) {
            if (model.getModelId().equals(promptRecord.getModelId())) {
                parameters.append(CommandPromptParameters.MODEL_NAME).append("\"").append(model.getModelDisplay()).append("\"");
                break;
            }
        }

        // 添加生图类型
        parameters.append(CommandPromptParameters.ORIGIN_CREATE).append("\"").append(promptRecord.getOriginCreate()).append("\"");

        // 返回字符串
        return parameters.toString();
    }

    public List<ModelInformation.ModelAbout> listModels() throws IOException {

        String modelDataKey = "modelListKey";
        List<ModelInformation.ModelAbout> modelAboutList = new ArrayList<>();

        /**
         * 如果redis中有数据，则直接返回
         */
        String modelListResult = redisCachePiclumen.getCacheObject(modelDataKey);
        if (StringUtils.isNotBlank(modelListResult)) {
            modelAboutList.addAll(JsonUtils.writeToList(modelListResult, ModelInformation.ModelAbout.class));
            return modelAboutList;
        }

        ModelInformation modelInformation = pythonApiUtils.getModelInformation();

        //调用py后端查询相关信息
        if (!CollectionUtils.isEmpty(modelInformation.getModelMessage())) {
            redisCachePiclumen.setCacheObject(LogicParamsCons.MODEL_LIST_KEY, JsonUtils.writeToString(modelInformation.getModelMessage()), 1, TimeUnit.HOURS);
            modelAboutList.addAll(modelInformation.getModelMessage());

        }
        return modelAboutList;
    }

}
