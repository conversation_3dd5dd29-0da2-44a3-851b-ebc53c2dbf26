package com.ai.admin.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.admin.domain.GptOpsInfo;

/**
 * 运营信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-08
 */
public interface IGptOpsInfoService extends IService<GptOpsInfo> {
    /**
     * 查询运营信息
     * 
     * @param id 运营信息主键
     * @return 运营信息
     */
    public GptOpsInfo selectGptOpsInfoById(Long id);

    /**
     * 查询运营信息列表
     * 
     * @param gptOpsInfo 运营信息
     * @return 运营信息集合
     */
    public List<GptOpsInfo> selectGptOpsInfoList(GptOpsInfo gptOpsInfo);

    /**
     * 新增运营信息
     * 
     * @param gptOpsInfo 运营信息
     * @return 结果
     */
    public int insertGptOpsInfo(GptOpsInfo gptOpsInfo);

    /**
     * 修改运营信息
     * 
     * @param gptOpsInfo 运营信息
     * @return 结果
     */
    public int updateGptOpsInfo(GptOpsInfo gptOpsInfo);

    /**
     * 批量删除运营信息
     * 
     * @param ids 需要删除的运营信息主键集合
     * @return 结果
     */
    public int deleteGptOpsInfoByIds(Long[] ids);

    /**
     * 删除运营信息信息
     * 
     * @param id 运营信息主键
     * @return 结果
     */
    public int deleteGptOpsInfoById(Long id);
}
