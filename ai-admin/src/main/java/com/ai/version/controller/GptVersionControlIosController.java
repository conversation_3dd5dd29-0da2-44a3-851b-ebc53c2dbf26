package com.ai.version.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.version.domain.GptVersionControlIos;
import com.ai.version.service.IGptVersionControlIosService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * ios端版本管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-18
 */
@RestController
@Api(value = "ios端版本管理控制器", tags = {"ios端版本管理管理"})
@RequestMapping("/version/ios")
public class GptVersionControlIosController extends BaseController {
    @Autowired
    private IGptVersionControlIosService gptVersionControlIosService;

    /**
     * 查询ios端版本管理列表
     */
    @PreAuthorize("@ss.hasPermi('version:ios:list')")
    @ApiOperation("查询ios端版本管理列表")
    @GetMapping("/list")
    public TableDataInfo list(GptVersionControlIos gptVersionControlIos) {
        startPage();
        List<GptVersionControlIos> list = gptVersionControlIosService.selectGptVersionControlIosList(gptVersionControlIos);
        return getDataTable(list);
    }

    /**
     * 导出ios端版本管理列表
     */
    @ApiOperation("导出ios端版本管理列表")
    @PreAuthorize("@ss.hasPermi('version:ios:export')")
    @Log(title = "ios端版本管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptVersionControlIos gptVersionControlIos) {
        List<GptVersionControlIos> list = gptVersionControlIosService.selectGptVersionControlIosList(gptVersionControlIos);
        ExcelUtil<GptVersionControlIos> util = new ExcelUtil<GptVersionControlIos>(GptVersionControlIos.class);
        util.exportExcel(response, list, "ios端版本管理数据");
    }

    /**
     * 获取ios端版本管理详细信息
     */
    @ApiOperation("获取ios端版本管理详细信息")
    @PreAuthorize("@ss.hasPermi('version:ios:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptVersionControlIosService.selectGptVersionControlIosById(id));
    }

    /**
     * 新增ios端版本管理
     */
    @ApiOperation("新增ios端版本管理")
    @PreAuthorize("@ss.hasPermi('version:ios:add')")
    @Log(title = "ios端版本管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptVersionControlIos gptVersionControlIos) {
        return gptVersionControlIosService.insertGptVersionControlIos(gptVersionControlIos);
    }

    /**
     * 修改ios端版本管理
     */
    @ApiOperation("修改ios端版本管理")
    @PreAuthorize("@ss.hasPermi('version:ios:edit')")
    @Log(title = "ios端版本管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptVersionControlIos gptVersionControlIos) {
        return gptVersionControlIosService.updateGptVersionControlIos(gptVersionControlIos);
    }

    /**
     * 删除ios端版本管理
     */
    @ApiOperation("删除ios端版本管理")
    @PreAuthorize("@ss.hasPermi('version:ios:remove')")
    @Log(title = "ios端版本管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return gptVersionControlIosService.deleteGptVersionControlIosByIds(ids);
    }
}
