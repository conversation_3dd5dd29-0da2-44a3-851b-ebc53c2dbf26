package com.ai.version.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * web端版本管理对象 gpt_version_control_web
 * 
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_version_control_web", description = "web端版本管理")
@TableName("gpt_version_control_web")
public class GptVersionControlWeb extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 版本号 */
    @ApiModelProperty("版本号")
    @Excel(name = "版本号")
    private String version;

    /** 是否当前版本（0否 1是） */
    @ApiModelProperty("是否当前版本")
    @Excel(name = "是否当前版本", readConverterExp = "0=否,1=是")
    private Integer isCurrent;

}
