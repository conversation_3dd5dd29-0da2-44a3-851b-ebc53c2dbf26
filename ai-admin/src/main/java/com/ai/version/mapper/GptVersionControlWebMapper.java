package com.ai.version.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.version.domain.GptVersionControlWeb;

/**
 * web端版本管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface GptVersionControlWebMapper extends BaseMapper<GptVersionControlWeb> {
    /**
     * 查询web端版本管理
     * 
     * @param id web端版本管理主键
     * @return web端版本管理
     */
    public GptVersionControlWeb selectGptVersionControlWebById(Long id);

    /**
     * 查询web端版本管理列表
     * 
     * @param gptVersionControlWeb web端版本管理
     * @return web端版本管理集合
     */
    public List<GptVersionControlWeb> selectGptVersionControlWebList(GptVersionControlWeb gptVersionControlWeb);

    /**
     * 新增web端版本管理
     * 
     * @param gptVersionControlWeb web端版本管理
     * @return 结果
     */
    public int insertGptVersionControlWeb(GptVersionControlWeb gptVersionControlWeb);

    /**
     * 修改web端版本管理
     * 
     * @param gptVersionControlWeb web端版本管理
     * @return 结果
     */
    public int updateGptVersionControlWeb(GptVersionControlWeb gptVersionControlWeb);

    /**
     * 删除web端版本管理
     * 
     * @param id web端版本管理主键
     * @return 结果
     */
    public int deleteGptVersionControlWebById(Long id);

    /**
     * 批量删除web端版本管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptVersionControlWebByIds(Long[] ids);
}
