package com.ai.version.service.impl;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.constants.LogicParamsCons;
import com.ai.version.domain.GptVersionControlWeb;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.version.mapper.GptVersionControlIosMapper;
import com.ai.version.domain.GptVersionControlIos;
import com.ai.version.service.IGptVersionControlIosService;

import javax.annotation.Resource;

/**
 * ios端版本管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-18
 */
@Service
public class GptVersionControlIosServiceImpl extends ServiceImpl<GptVersionControlIosMapper, GptVersionControlIos> implements IGptVersionControlIosService
{
    @Autowired
    private GptVersionControlIosMapper gptVersionControlIosMapper;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    /**
     * 查询ios端版本管理
     * 
     * @param id ios端版本管理主键
     * @return ios端版本管理
     */
    @Override
    public GptVersionControlIos selectGptVersionControlIosById(Long id)
    {
        return gptVersionControlIosMapper.selectGptVersionControlIosById(id);
    }

    /**
     * 查询ios端版本管理列表
     * 
     * @param gptVersionControlIos ios端版本管理
     * @return ios端版本管理
     */
    @Override
    public List<GptVersionControlIos> selectGptVersionControlIosList(GptVersionControlIos gptVersionControlIos)
    {
        return gptVersionControlIosMapper.selectGptVersionControlIosList(gptVersionControlIos);
    }

    /**
     * 新增ios端版本管理
     * 
     * @param gptVersionControlIos ios端版本管理
     * @return 结果
     */
    @Override
    public AjaxResult insertGptVersionControlIos(GptVersionControlIos gptVersionControlIos)
    {
        String version = gptVersionControlIos.getVersion();
        if (!version.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            return AjaxResult.error("版本号格式不正确");
        }

        Long aLong = gptVersionControlIosMapper.selectCount(new LambdaQueryWrapper<GptVersionControlIos>()
                .eq(GptVersionControlIos::getVersion, version));
        if (aLong >= 1) {
            return AjaxResult.error("该版本号已存在!");
        }

        gptVersionControlIos.setCreateTime(LocalDateTime.now());
        gptVersionControlIos.setIsCurrent(0);
        gptVersionControlIos.setCreateTime(LocalDateTime.now());

        return AjaxResult.success(gptVersionControlIosMapper.insertGptVersionControlIos(gptVersionControlIos));
    }

    /**
     * 修改ios端版本管理
     * 
     * @param gptVersionControlIos ios端版本管理
     * @return 结果
     */
    @Override
    public AjaxResult updateGptVersionControlIos(GptVersionControlIos gptVersionControlIos)
    {
        if (gptVersionControlIos.getIsCurrent().equals(1)){
            //切换版本号时，将当前版本号置为0
            GptVersionControlIos gptVersionControlWeb1 = gptVersionControlIosMapper.selectOne(new LambdaQueryWrapper<GptVersionControlIos>()
                    .eq(GptVersionControlIos::getIsCurrent, 1));
            gptVersionControlIos.setUpdateTime(LocalDateTime.now());
            if (!Objects.isNull(gptVersionControlWeb1)){
                gptVersionControlWeb1.setIsCurrent(0);
                gptVersionControlIosMapper.updateById(gptVersionControlWeb1);
            }
            redisCachePiclumen.setCacheObject(LogicParamsCons.IOS_VERSION,gptVersionControlIos.getVersion());
        } else {
            Long aLong = gptVersionControlIosMapper.selectCount(new LambdaQueryWrapper<GptVersionControlIos>()
                    .eq(GptVersionControlIos::getIsCurrent, 1)
                    .eq(GptVersionControlIos::getId, gptVersionControlIos.getId()));
            if (aLong >= 1){
                return AjaxResult.error("至少保留一个当前版本!");
            }
        }
        gptVersionControlIos.setUpdateTime(LocalDateTime.now());
        return AjaxResult.success(gptVersionControlIosMapper.updateGptVersionControlIos(gptVersionControlIos));
    }

    /**
     * 批量删除ios端版本管理
     * 
     * @param ids 需要删除的ios端版本管理主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteGptVersionControlIosByIds(Long[] ids)
    {
        Long aLong = gptVersionControlIosMapper.selectCount(new LambdaQueryWrapper<GptVersionControlIos>()
                .eq(GptVersionControlIos::getIsCurrent, 1)
                .in(GptVersionControlIos::getId, ids));
        if (aLong >= 1){
            return AjaxResult.error("当前版本不能删除!");
        }
        return AjaxResult.success(gptVersionControlIosMapper.deleteGptVersionControlIosByIds(ids));
    }

    /**
     * 删除ios端版本管理信息
     * 
     * @param id ios端版本管理主键
     * @return 结果
     */
    @Override
    public int deleteGptVersionControlIosById(Long id)
    {
        return gptVersionControlIosMapper.deleteGptVersionControlIosById(id);
    }
}
