package com.ai.version.service.impl;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.constants.LogicParamsCons;
import com.ai.version.domain.GptVersionControlIos;
import com.ai.version.domain.GptVersionControlWeb;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.version.mapper.GptVersionControlAndroidMapper;
import com.ai.version.domain.GptVersionControlAndroid;
import com.ai.version.service.IGptVersionControlAndroidService;

import javax.annotation.Resource;

/**
 * 安卓端版本管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class GptVersionControlAndroidServiceImpl extends ServiceImpl<GptVersionControlAndroidMapper, GptVersionControlAndroid> implements IGptVersionControlAndroidService
{
    @Autowired
    private GptVersionControlAndroidMapper gptVersionControlAndroidMapper;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    /**
     * 查询安卓端版本管理
     * 
     * @param id 安卓端版本管理主键
     * @return 安卓端版本管理
     */
    @Override
    public GptVersionControlAndroid selectGptVersionControlAndroidById(Long id)
    {
        return gptVersionControlAndroidMapper.selectGptVersionControlAndroidById(id);
    }

    /**
     * 查询安卓端版本管理列表
     * 
     * @param gptVersionControlAndroid 安卓端版本管理
     * @return 安卓端版本管理
     */
    @Override
    public List<GptVersionControlAndroid> selectGptVersionControlAndroidList(GptVersionControlAndroid gptVersionControlAndroid)
    {
        return gptVersionControlAndroidMapper.selectGptVersionControlAndroidList(gptVersionControlAndroid);
    }

    /**
     * 新增安卓端版本管理
     * 
     * @param gptVersionControlAndroid 安卓端版本管理
     * @return 结果
     */
    @Override
    public AjaxResult insertGptVersionControlAndroid(GptVersionControlAndroid gptVersionControlAndroid)
    {
        String version = gptVersionControlAndroid.getVersion();
        if (!version.matches("\\d+\\.\\d+\\.\\d+")) {
            return AjaxResult.error("版本号格式不正确");
        }

        Long aLong = gptVersionControlAndroidMapper.selectCount(new LambdaQueryWrapper<GptVersionControlAndroid>()
                .eq(GptVersionControlAndroid::getVersion, version));
        if (aLong >= 1) {
            return AjaxResult.error("该版本号已存在!");
        }

        gptVersionControlAndroid.setCreateTime(LocalDateTime.now());
        gptVersionControlAndroid.setIsCurrent(0);
        gptVersionControlAndroid.setCreateTime(LocalDateTime.now());
        return  AjaxResult.success(gptVersionControlAndroidMapper.insertGptVersionControlAndroid(gptVersionControlAndroid));
    }

    /**
     * 修改安卓端版本管理
     * 
     * @param gptVersionControlAndroid 安卓端版本管理
     * @return 结果
     */
    @Override
    public AjaxResult updateGptVersionControlAndroid(GptVersionControlAndroid gptVersionControlAndroid)
    {
        if (gptVersionControlAndroid.getIsCurrent().equals(1)){
            //切换版本号时，将当前版本号置为0
            GptVersionControlAndroid gptVersionControlAndroid1 = gptVersionControlAndroidMapper.selectOne(new LambdaQueryWrapper<GptVersionControlAndroid>()
                    .eq(GptVersionControlAndroid::getIsCurrent, 1));
            gptVersionControlAndroid.setUpdateTime(LocalDateTime.now());
            if (!Objects.isNull(gptVersionControlAndroid1)){
                gptVersionControlAndroid1.setIsCurrent(0);
                gptVersionControlAndroidMapper.updateById(gptVersionControlAndroid1);
            }
            redisCachePiclumen.setCacheObject(LogicParamsCons.ANDROID_VERSION,gptVersionControlAndroid.getVersion());
        } else {
            Long aLong = gptVersionControlAndroidMapper.selectCount(new LambdaQueryWrapper<GptVersionControlAndroid>()
                    .eq(GptVersionControlAndroid::getIsCurrent, 1)
                    .eq(GptVersionControlAndroid::getId, gptVersionControlAndroid.getId()));
            if (aLong >= 1){
                return AjaxResult.error("至少保留一个当前版本!");
            }
        }
        gptVersionControlAndroid.setUpdateTime(LocalDateTime.now());
        return  AjaxResult.success(gptVersionControlAndroidMapper.updateGptVersionControlAndroid(gptVersionControlAndroid));
    }

    /**
     * 批量删除安卓端版本管理
     * 
     * @param ids 需要删除的安卓端版本管理主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteGptVersionControlAndroidByIds(Long[] ids)
    {
        Long aLong = gptVersionControlAndroidMapper.selectCount(new LambdaQueryWrapper<GptVersionControlAndroid>()
                .eq(GptVersionControlAndroid::getIsCurrent, 1)
                .in(GptVersionControlAndroid::getId, ids));
        if (aLong >= 1){
            return AjaxResult.error("当前版本不能删除!");
        }
        return AjaxResult.success(gptVersionControlAndroidMapper.deleteGptVersionControlAndroidByIds(ids));
    }

    /**
     * 删除安卓端版本管理信息
     * 
     * @param id 安卓端版本管理主键
     * @return 结果
     */
    @Override
    public int deleteGptVersionControlAndroidById(Long id)
    {
        return gptVersionControlAndroidMapper.deleteGptVersionControlAndroidById(id);
    }
}
