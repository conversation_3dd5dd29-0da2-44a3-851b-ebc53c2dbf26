package com.ai.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.ai.task.mapper.GptModelAboutMapper;
import com.ai.task.domain.GptModelAbout;
import com.ai.task.service.IGptModelAboutService;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 模型信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class GptModelAboutServiceImpl extends ServiceImpl<GptModelAboutMapper, GptModelAbout> implements IGptModelAboutService
{

    @Override
    public GptModelAbout selectGptModelAboutById(Long id)
    {
        return baseMapper.selectById(id);
    }

    /**
     * 查询模型信息列表
     *
     * @param gptModelAbout 模型信息
     * @return 模型信息
     */
    @Override
    public List<GptModelAbout> selectGptModelAboutList(GptModelAbout gptModelAbout)
    {
        return baseMapper.selectGptModelAboutList(gptModelAbout);
    }


    /**
     * 新增模型信息
     *
     * @param gptModelAbout 模型信息
     * @return 结果
     */
    @Override
    public int insertGptModelAbout(GptModelAbout gptModelAbout)
    {
        gptModelAbout.setCreateTime(LocalDateTime.now());
        return baseMapper.insert(gptModelAbout);
    }

    /**
     * 修改模型信息
     *
     * @param gptModelAbout 模型信息
     * @return 结果
     */
    @Override
    public int updateGptModelAbout(GptModelAbout gptModelAbout)
    {
        gptModelAbout.setUpdateTime(LocalDateTime.now());
        return baseMapper.updateById(gptModelAbout);
    }


    /**
     * 删除模型信息信息
     *
     * @param id 模型信息主键
     * @return 结果
     */
    @Override
    public int deleteGptModelAboutById(Long id)
    {
        return baseMapper.deleteById(id);
    }


}
