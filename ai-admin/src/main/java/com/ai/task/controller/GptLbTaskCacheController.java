package com.ai.task.controller;

import com.ai.common.annotation.Log;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.BusinessType;
import com.ai.constants.LogicParamsCons;
import com.ai.system.domain.SysCache;
import com.ai.task.service.IGptLbFeaturesRulesService;
import com.ai.task.service.impl.TaskCacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 任务调度缓存监控Controller
 *
 * <AUTHOR>
 * @date 2024-08-27
 */
@RestController
@Api(value = "任务调度缓存监控控制器", tags = {"任务调度缓存监控管理"})
@RequestMapping("/task/cache")
public class GptLbTaskCacheController {

    @Resource
    private IGptLbFeaturesRulesService iGptLbFeaturesRulesService;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    @Resource
    private TaskCacheService taskCacheService;

   private final static Set<String> cacheKeysOther =new HashSet<>();
    {
        cacheKeysOther.add("lb_system_para");
        cacheKeysOther.add("loadbalance_feature");
        cacheKeysOther.add("loadbalance_model");
        cacheKeysOther.add("model_feature");
        cacheKeysOther.add("user_today_create_img_nums");
        cacheKeysOther.add("user_task_cost_times");
        cacheKeysOther.add(LogicParamsCons.WEB_VERSION);
        cacheKeysOther.add(LogicParamsCons.IOS_VERSION);
        cacheKeysOther.add(LogicParamsCons.ANDROID_VERSION);
        cacheKeysOther.add(LogicParamsCons.BL_RULE_FLUX);
        cacheKeysOther.add(LogicParamsCons.BL_RULE_COMMON);
        cacheKeysOther.add(LogicParamsCons.ALL_FEATURE_SERVERIDS);

    }

    /**
     * 获取任务调度缓存名称
     *
     * @return 结果
     */

    @PreAuthorize("@ss.hasPermi('task:cache:list')")
    @ApiOperation("获取任务调度缓存名称")
    @GetMapping("/getNames")
    public AjaxResult cache() {
        return AjaxResult.success(iGptLbFeaturesRulesService.getTaskCacheNames());
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @ApiOperation("获取任务调度获取缓存键")
    @GetMapping("/getKeys/{cacheName}")
    public AjaxResult getCacheKeys(@PathVariable String cacheName) {
        Set<String> cacheKeys;

        if (cacheName.contains("serviceQueue")) {
            // 如果 cacheName 包含 "serviceQueue"，执行另一个方法
            Map<String, String> cacheMap = redisCachePiclumen.getCacheMap(cacheName.split("_")[0]);
            cacheKeys = cacheMap.keySet(); // 提取 Map 中的所有键
        } else if (cacheName.equals(LogicParamsCons.OTHER_TASK_SCHEDULING_CACHES)) {
            cacheKeys = cacheKeysOther;
        } else {
            cacheKeys = (Set<String>) redisCachePiclumen.keys("*" + cacheName + "*");
        }

        return AjaxResult.success(new TreeSet<>(cacheKeys));
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @ApiOperation("获取任务调度获取缓存值")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey) {
        DataType type = redisCachePiclumen.getType(cacheKey);
        String cacheObject = "";
        switch (type.code()) {
            case "string":
                cacheObject=redisCachePiclumen.getCacheObject(cacheKey)+"";break;
            case "hash":
                Map<String, String> cacheMap = redisCachePiclumen.getCacheMap(cacheKey);
                cacheObject=cacheMap.toString();
                break;
            case "list":
                List<String> cacheList = redisCachePiclumen.getCacheList(cacheKey);
                cacheObject=cacheList.toString();
                break;
            case "zset":
                Set<ZSetOperations.TypedTuple<String>> cacheSet = redisCachePiclumen.getZSetAllVaule(cacheKey);
                System.out.println(cacheSet);
                StringBuilder zsetBuilder = new StringBuilder();
                for (ZSetOperations.TypedTuple<String> tuple : cacheSet) {
                    zsetBuilder.append("Value: ").append(tuple.getValue())
                            .append(", Score: ").append(tuple.getScore())
                            .append("; ");
                }
                cacheObject = zsetBuilder.toString();
                break;
            default:
                cacheObject="";
        }
        SysCache sysCache = new SysCache(cacheName, cacheKey, cacheObject);
        sysCache.setCacheKey(cacheKey);
        return AjaxResult.success(sysCache);
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheKey/{cacheKey}")
    @Log(title = "删除任务键值", businessType = BusinessType.DELETE)
    public AjaxResult clearCacheKey(@PathVariable String cacheKey) {
        if (cacheKey.contains("fairQueue") && !cacheKey.contains("counter")){
            taskCacheService.clearCacheKey(cacheKey);
        }else {
            redisCachePiclumen.deleteObject(cacheKey);
        }
        return AjaxResult.success();
    }

    @ApiOperation("根据日期获取儿童色情词 过滤次数")
    @GetMapping("/get-child-porn-count")
    public AjaxResult getChildPornCountByDate(@RequestParam("date") String date){
        return AjaxResult.success(taskCacheService.getChildPornCountByDate(date));
    }
}
