package com.ai.task.domain.vo;

import com.ai.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("保底规则实例返回参数")
public class GuaranteesInstanceVo {

    /** 实例标识 */
    @ApiModelProperty("实例标识")
    private String instance;

    /** 标识 */
    @ApiModelProperty("标识")
    private String mark;

    /** 实例标识 */
    @ApiModelProperty("是否保底实例0不是1是")
    private Integer isGuarantees;
}
