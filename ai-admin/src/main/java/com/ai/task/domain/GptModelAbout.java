package com.ai.task.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 模型信息对象 gpt_model_about
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_model_about", description = "模型信息")
@TableName("gpt_model_about")
public class GptModelAbout extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;



    /** 模型id */
    @ApiModelProperty("模型id")
    @Excel(name = "模型id")
    private String modelId;

    /** 模型名称 */
    @ApiModelProperty("模型名称")
    @Excel(name = "模型名称")
    private String modelDisplay;

    /** 模型类型 */
    @ApiModelProperty("模型类型")
    @Excel(name = "模型类型")
    private String modelType;

    /** 默认超分降噪指数 */
    @ApiModelProperty("默认超分降噪指数")
    @Excel(name = "默认超分降噪指数")
    private Double defaultHdFixDenoise;

    /** 模型图标 */
    @ApiModelProperty("模型图标")
    @Excel(name = "模型图标")
    private String modelAvatar;

    /** 模型描述 */
    @ApiModelProperty("模型描述")
    @Excel(name = "模型描述")
    private String modelDesc;

    /** 模型排序 */
    @ApiModelProperty("模型排序")
    @Excel(name = "模型排序")
    private Integer modelOrder;

    @ApiModelProperty("ios排序")
    @Excel(name = "ios排序")
    private Integer iosOrder;

    @ApiModelProperty("android排序")
    @Excel(name = "android排序")
    private Integer  androidOrder;

    /** web,ios,android */
    @ApiModelProperty("web,ios,android")
    @Excel(name = "web,ios,android")
    private String platform;

    @ApiModelProperty("模型来源类型：0-工作流模式，1-API模式（第三方）")
    @Excel(name = "模型来源类型：0-工作流模式，1-API模式（第三方）")
    private Integer modelOriginType;

    /** 模型详情 defaultConfig 信息（JSON） */
    @ApiModelProperty("模型详情 defaultConfig 信息")
    @Excel(name = "模型详情 defaultConfig 信息", readConverterExp = "JSON")
    private String defaultConfig;

    /** 模型所支持的风格类型列表（JSON） */
    @ApiModelProperty("模型所支持的风格类型列表")
    @Excel(name = "模型所支持的风格类型列表", readConverterExp = "JSON")
    private String supportStyleList;

    @ApiModelProperty("按张数扣点系数")
    @Excel(name = "按张数扣点系数")
    private Integer coefficientByNum;

    @ApiModelProperty("按像素扣点系数")
    @Excel(name = "按像素扣点系数")
    private Integer coefficientByPixel;



}
