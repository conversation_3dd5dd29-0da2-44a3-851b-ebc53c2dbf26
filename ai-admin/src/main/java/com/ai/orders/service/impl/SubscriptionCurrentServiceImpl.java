package com.ai.orders.service.impl;

import java.time.format.DateTimeFormatter;
import java.util.*;

import cn.hutool.core.util.ObjectUtil;
import com.ai.admin.service.IGptUserService;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.*;
import com.ai.common.utils.DateUtils;
import com.ai.common.utils.StringUtils;
import com.ai.defined.utils.PayLogContextHolder;
import com.ai.orders.domain.*;
import com.ai.orders.domain.dto.SubscriptionCurrentDto;
import com.ai.orders.mapper.*;
import com.ai.orders.service.IPayLumenRecordService;
import com.ai.orders.service.ISysLumenAdminOperateLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.service.ISubscriptionCurrentService;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * 当前订阅Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
@Slf4j
public class SubscriptionCurrentServiceImpl extends ServiceImpl<SubscriptionCurrentMapper, SubscriptionCurrent> implements ISubscriptionCurrentService
{
    @Autowired
    private SubscriptionCurrentMapper subscriptionCurrentMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;

    @Resource
    private ObjectMapper logicObjectMapper;

    @Autowired
    private IGptUserService gptUserService;

    @Autowired
    private SysLumenAdminOperateLogMapper sysLumenAdminOperateLogMapper;

    @Autowired
    private PayLogicPurchaseRecordMapper payLogicPurchaseRecordMapper;

    @Autowired
    private PayLumenRecordMapper payLumenRecordMapper;

    // 缓存前缀
    private static final String CACHE_KEY_PREFIX = "user:subscriptions:";

    @Autowired
    private IPayLumenRecordService payLumenRecordService;

    @Autowired
    private ISysLumenAdminOperateLogService sysLumenAdminOperateLogService;

    @Autowired
    private UserPayRecordMapper userPayRecordMapper;

    @Autowired
    private  UserPayRecordItemMapper userPayRecordItemMapper;

    /**
     * 查询当前订阅
     * 
     * @param id 当前订阅主键
     * @return 当前订阅
     */
    @Override
    public SubscriptionCurrent selectSubscriptionCurrentById(Long id)
    {
        return subscriptionCurrentMapper.selectSubscriptionCurrentById(id);
    }

    /**
     * 查询当前订阅列表
     * 
     * @param subscriptionCurrent 当前订阅
     * @return 当前订阅
     */
    @Override
    public List<SubscriptionCurrent> selectSubscriptionCurrentList(SubscriptionCurrent subscriptionCurrent)
    {

        Map<String, Object> params = subscriptionCurrent.getParams();
        if (params != null){

            // 转换时间字符串为 Instant 类型的时间戳
            params.put("beginVipBegin", DateUtils.convertToEpochSecond(params.get("beginVipBegin").toString()));
            params.put("endVipBegin", DateUtils.convertToEpochSecond(params.get("endVipBegin").toString()));

            // 更新 params
            subscriptionCurrent.setParams(params);
        }
        return subscriptionCurrentMapper.selectSubscriptionCurrentList(subscriptionCurrent);
    }

    /**
     * 新增当前订阅
     * 
     * @param subscriptionCurrentDto 当前订阅
     * @return 结果
     */
    @Override
    public AjaxResult insertSubscriptionCurrent(SubscriptionCurrentDto subscriptionCurrentDto, LoginUser loginUser)
    {

        // 1. 获取该用户的所有赠送订阅记录
        LambdaQueryWrapper<SubscriptionCurrent> scw = new LambdaQueryWrapper<>();
        scw.eq(SubscriptionCurrent::getUserId, subscriptionCurrentDto.getUserId());
        scw.eq(SubscriptionCurrent::getVipPlatform,VipPlatform.GIFT.getPlatformName());

        List<SubscriptionCurrent> existingSubscriptions = subscriptionCurrentMapper
                .selectList(scw);

        if (existingSubscriptions != null && !existingSubscriptions.isEmpty()) {
            Long newVipBeginTime = DateUtils.getTimestamp(subscriptionCurrentDto.getVipBeginTimeDate());
            Long newVipEndTime = DateUtils.getTimestamp(subscriptionCurrentDto.getVipEndTimeDate());

            // 遍历所有的现有订阅记录，判断是否有交叉时间
            for (SubscriptionCurrent existingSubscription : existingSubscriptions) {
                Long existingVipBeginTime = existingSubscription.getVipBeginTime();
                Long existingVipEndTime = existingSubscription.getVipEndTime();

                // 判断时间交叉条件
                if ((existingVipEndTime > newVipBeginTime && existingVipBeginTime < newVipEndTime) ||
                        (existingVipEndTime > newVipEndTime && existingVipBeginTime < newVipBeginTime)) {
                    // 时间交叉，返回，不插入
                    log.info("用户ID: {} 已经有有效的赠送会员记录，时间范围存在交叉，无法继续赠送会员。", subscriptionCurrentDto.getUserId());
                    return AjaxResult.error("此用户 已经有有效的赠送会员记录，时间范围存在交叉，无法继续赠送会员。");
                }
            }
        }

        SubscriptionCurrent subscriptionCurrent = new SubscriptionCurrent();
        BeanUtils.copyProperties(subscriptionCurrentDto, subscriptionCurrent);
        subscriptionCurrent.setCreateTime(LocalDateTime.now());
        String platformName = VipPlatform.GIFT.getPlatformName();
        String subscriptionId = platformName+"_"+ StringUtils.generateRandomString(22);
        subscriptionCurrent.setSubscriptionId(subscriptionId);
        subscriptionCurrent.setVipPlatform(platformName);
        subscriptionCurrent.setVipBeginTime(DateUtils.getTimestamp(subscriptionCurrentDto.getVipBeginTimeDate()));
        subscriptionCurrent.setVipEndTime(DateUtils.getTimestamp(subscriptionCurrentDto.getVipEndTimeDate()));
        subscriptionCurrent.setAutoRenewStatus(0);
        subscriptionCurrent.setInvalid(0);
        subscriptionCurrentMapper.insert(subscriptionCurrent);

        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        if (subscriptionCurrent.getVipBeginTime() < currentTimestamp && currentTimestamp < subscriptionCurrent.getVipEndTime()){
            log.info("在当前订阅期限内，刷新会员状态和 Redis");
            updateUserVipStatus(subscriptionCurrent.getUserId());
        }

        // 插入会员记录
        UserPayRecord userPayRecord = buildUserPayRecord(subscriptionCurrentDto, subscriptionCurrent);
        userPayRecordMapper.insert(userPayRecord);

        UserPayRecordItem userPayRecordItem = buildUserPayRecordItem(subscriptionCurrent, userPayRecord);
        userPayRecordItemMapper.insert(userPayRecordItem);

        // 插入操作日志
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(subscriptionCurrent.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(0l);
        log.setAfterChangeLumen(0l);
        log.setType(LumenAdminOperateType.giftVip.getValue()); // 赠送会员
        log.setCreateTime(LocalDateTime.now());
        log.setLumen(0);
        log.setCreateBy(loginUser.getUserId().toString());
        sysLumenAdminOperateLogMapper.insert(log);
        return AjaxResult.success("赠送成功");
    }

    /**
     * 修改当前订阅
     * 
     * @param subscriptionCurrent 当前订阅
     * @return 结果
     */
    @Override
    public int updateSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent)
    {
        subscriptionCurrent.setUpdateTime(LocalDateTime.now());
        return subscriptionCurrentMapper.updateSubscriptionCurrent(subscriptionCurrent);
    }

    /**
     * 批量删除当前订阅
     * 
     * @param ids 需要删除的当前订阅主键
     * @return 结果
     */
    @Override
    public int deleteSubscriptionCurrentByIds(Long[] ids)
    {
        return subscriptionCurrentMapper.deleteSubscriptionCurrentByIds(ids);
    }

    /**
     * 删除当前订阅信息
     * 
     * @param id 当前订阅主键
     * @return 结果
     */
    @Override
    public int deleteSubscriptionCurrentById(Long id,LoginUser loginUser)
    {
        SubscriptionCurrent subscriptionCurrent = subscriptionCurrentMapper.selectById(id);
        subscriptionCurrentMapper.deleteSubscriptionCurrentById(id);
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());

        if (subscriptionCurrent.getVipBeginTime() < currentTimestamp && currentTimestamp < subscriptionCurrent.getVipEndTime()){
            log.info("在当前订阅期限内，刷新会员状态和 Redis");
            updateUserVipStatus(subscriptionCurrent.getUserId());
        }

        // 插入操作日志
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(subscriptionCurrent.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(0l);
        log.setAfterChangeLumen(0l);
        log.setType(LumenAdminOperateType.deleteVip.getValue()); // 删除会员
        log.setCreateTime(LocalDateTime.now());
        log.setLumen(0);
        log.setCreateBy(loginUser.getUserId().toString());
        return sysLumenAdminOperateLogMapper.insert(log);
    }

    @Override
    public void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = getLogicValidHighSubscriptionsFromDb(userId);
        log.info("[{}] 更新用户VIP等级1 start: loginName={} newPlanLevel={} priceInterval={}", PayLogContextHolder.getLogUUID(), subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        gptUserService.updateUserVipInfo(subscription, userId);
        log.info("[{}] 更新用户VIP等级1 end: loginName={}, newPlanLevel={} priceInterval={}", PayLogContextHolder.getLogUUID(), subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }
    @Override
    public void updateUserVipStatusBatch(List<Long> userIds) {
        Map<Long, SubscriptionCurrent> logicValidHighSubscriptionsFromDbBath = getLogicValidHighSubscriptionsFromDbBath(userIds);
        log.info("更新用户VIP等级1 current:{}  ", logicValidHighSubscriptionsFromDbBath);
        gptUserService.updateUserVipInfoBatch(logicValidHighSubscriptionsFromDbBath);
        log.info("更新用户VIP等级1 current:{}  ", logicValidHighSubscriptionsFromDbBath);
    }

    /**
     * 获取最高级别的有效订阅
     *
     * @param subscriptions 订阅记录列表
     * @return 最高级别的订阅
     */
    private SubscriptionCurrent getHighestVipSubscription(List<SubscriptionCurrent> subscriptions) {
        if (subscriptions.isEmpty()) {
            // 如果没有有效订阅，则返回一个默认的订阅对象
            SubscriptionCurrent defaultSubscription = new SubscriptionCurrent();
            defaultSubscription.setPlanLevel("basic");
            return defaultSubscription;
        }

        // 按 vipType 和 priceInterval 排序，获取最高级别的订阅
        return subscriptions.stream().sorted(Comparator.comparing((SubscriptionCurrent sub) -> {
                    // 设置 vipType 的优先级，转换为整数
                    switch (sub.getPlanLevel()) {
                        case "pro":
                            return 3;    // pro 为最高
                        case "standard":
                            return 2;    // standard 为中等
                        case "basic":
                            return 1;    // basic 为最低
                        default:
                            return 1;    // 未知类型
                    }
                }).thenComparing(sub -> {
                    // 设置 priceInterval 的优先级，年付 > 月付
                    return "year".equals(sub.getPriceInterval()) ? 1 : 0;
                })
//                .thenComparing(SubscriptionCurrent::getVipEndTime) // 新增时间倒序
                .reversed()) // reversed 确保 vipType 和 priceInterval 按优先级从高到低排序
                .findFirst() // 获取排序后的第一个元素
                .orElseGet(() -> {
                    // 如果没有订阅，则返回一个默认的订阅对象
                    SubscriptionCurrent defaultSubscription = new SubscriptionCurrent();
                    defaultSubscription.setPlanLevel("basic");
                    return defaultSubscription;
                });
    }

    @Override
    public List<SubscriptionCurrent> getLogicValidSubscriptionsFromDb(Long userId) {

        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）
        // 直接读取主库
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.setWriteRouteOnly();
            LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubscriptionCurrent::getUserId, userId) // user_id = userId
                    .eq(SubscriptionCurrent::getInvalid, 0)
                    .le(SubscriptionCurrent::getVipBeginTime, currentTime) // vip_begin_time <= currentTime
                    .ge(SubscriptionCurrent::getVipEndTime, currentTime); // vip_end_time >= currentTime

            // 执行查询
            return subscriptionCurrentMapper.selectList(queryWrapper);
        }

    }

    public List<SubscriptionCurrent> getLogicValidSubscriptionsFromDbBatch(List<Long> userIds) {

        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）
        // 直接读取主库
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.setWriteRouteOnly();
            LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SubscriptionCurrent::getUserId, userIds) // user_id = userId
                    .eq(SubscriptionCurrent::getInvalid, 0)
                    .le(SubscriptionCurrent::getVipBeginTime, currentTime) // vip_begin_time <= currentTime
                    .ge(SubscriptionCurrent::getVipEndTime, currentTime); // vip_end_time >= currentTime

            // 执行查询
            return subscriptionCurrentMapper.selectList(queryWrapper);
        }

    }

    @Override
    public SubscriptionCurrent getLogicValidHighSubscriptionsFromDb(Long userId) {
        // 构建缓存键
        String cacheKey = CACHE_KEY_PREFIX + userId;
        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        // 1. 删除redis
        redisCachePiclumen.deleteObject(cacheKey);
        // 2. 如果缓存中没有数据或数据已过期，则从数据库查询
        List<SubscriptionCurrent> subscriptionCurrents = getLogicValidSubscriptionsFromDb(userId);

        SubscriptionCurrent highestVipSubscription = getHighestVipSubscription(subscriptionCurrents);

        // 3. 将查询结果缓存到 Redis，设置缓存过期时间（例如：5分钟）
        try {
            if (!VipType.basic.getValue().equalsIgnoreCase(highestVipSubscription.getPlanLevel())) {
                redisCachePiclumen.stringSet(cacheKey, logicObjectMapper.writeValueAsString(highestVipSubscription), 60, TimeUnit.MINUTES);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize subscription data to Redis.", e);
        }

        // 返回最高级别的订阅
        return highestVipSubscription;
    }

    public Map<Long, SubscriptionCurrent> getLogicValidHighSubscriptionsFromDbBath(List<Long> userIds) {
        for (Long userId : userIds) {
            // 构建缓存键
            String cacheKey = CACHE_KEY_PREFIX + userId;

            // 1. 删除redis
            redisCachePiclumen.deleteObject(cacheKey);
        }
        // 2. 如果缓存中没有数据或数据已过期，则从数据库查询
        List<SubscriptionCurrent> subscriptionCurrents = getLogicValidSubscriptionsFromDbBatch(userIds);

        Map<Long, List<SubscriptionCurrent>> map = subscriptionCurrents.stream().collect(Collectors.groupingBy(SubscriptionCurrent::getUserId));

        Map<Long, SubscriptionCurrent> resultMap = new HashMap<>();

        for (Map.Entry<Long, List<SubscriptionCurrent>> entry : map.entrySet()) {
            Long userId = entry.getKey();
            List<SubscriptionCurrent> currents = entry.getValue();
            SubscriptionCurrent highestVipSubscription = getHighestVipSubscription(currents);
            resultMap.put(userId, highestVipSubscription);
        }

        // 3. 将查询结果缓存到 Redis，设置缓存过期时间（例如：5分钟）
        for( Map.Entry<Long, SubscriptionCurrent> entry : resultMap.entrySet()){
            Long userId = entry.getKey();
            // 构建缓存键
            String cacheKey = CACHE_KEY_PREFIX + userId;
            SubscriptionCurrent highestVipSubscription = entry.getValue();
            try {
                if (!VipType.basic.getValue().equalsIgnoreCase(highestVipSubscription.getPlanLevel())) {
                    redisCachePiclumen.stringSet(cacheKey, logicObjectMapper.writeValueAsString(highestVipSubscription), 60, TimeUnit.MINUTES);
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Failed to serialize subscription data to Redis.", e);
            }
        }
        // 返回最高级别的订阅
        return resultMap;
    }
    public void insertLogBatch(Long payLogicPurchaseRecordId,LoginUser loginUser) {
        LambdaQueryWrapper<PayLumenRecord> plu = new LambdaQueryWrapper<>();
        plu.eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicPurchaseRecordId);
        List<PayLumenRecord> payLumenRecords = payLumenRecordMapper.selectList(plu);
        List<SysLumenAdminOperateLog> logList = new ArrayList<>();
        for (PayLumenRecord record:payLumenRecords) {
            SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
            log.setOperName(loginUser.getUsername());
            log.setLumenRecordId(record.getId());
            log.setDeptName(loginUser.getUser().getDept().getDeptName());
            log.setBeforeChangeLumen(0l);
            log.setAfterChangeLumen(0l);
            log.setType(LumenAdminOperateType.invalid.getValue()); // 设置会员为无效
            log.setCreateTime(LocalDateTime.now());
            log.setLumen(0);
            log.setCreateBy(loginUser.getUserId().toString());
            logList.add(log);
         }
        sysLumenAdminOperateLogService.saveBatch(logList);
    }

    /**
     * 设置会员为无效
     *
     * @param id 当前订阅表主键id
     */
    @Override
    public AjaxResult setInvalid(Long id, LoginUser loginUser){

        SubscriptionCurrent subscriptionCurrent = baseMapper.selectById(id);
        if (ObjectUtils.isEmpty(subscriptionCurrent)) {
            return AjaxResult.error("找不到相应的订阅信息");
        }

        // 判断当前订阅是否已经过期
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        if (subscriptionCurrent.getVipEndTime()<currentTimestamp || subscriptionCurrent.getCurrentPeriodEnd()<currentTimestamp){
            return AjaxResult.error("当前订阅已经过期");
        }

//        // 设置会员结束时间和订阅周期结束时间为当前时间 续订状态改为关闭
//        subscriptionCurrent.setVipEndTime(currentTimestamp);
//        subscriptionCurrent.setCurrentPeriodEnd(currentTimestamp);
//        subscriptionCurrent.setAutoRenewStatus(0);
        subscriptionCurrent.setInvalid(1);
        baseMapper.updateById(subscriptionCurrent);

        // lumen 实时记录设置为无效
        LambdaQueryWrapper<PayLogicPurchaseRecord> lpw = new LambdaQueryWrapper<>();
        lpw.eq(PayLogicPurchaseRecord::getSubscriptionId, subscriptionCurrent.getSubscriptionId());
        PayLogicPurchaseRecord payLogicPurchaseRecord = payLogicPurchaseRecordMapper.selectOne(lpw);

        LambdaUpdateWrapper<PayLumenRecord> plu = new LambdaUpdateWrapper<>();
        plu.set(PayLumenRecord::getInvalid, 1);
        plu.eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicPurchaseRecord.getId());
        payLumenRecordMapper.update(null, plu);

        // 删除redis
        redisCachePiclumen.deleteObject(CACHE_KEY_PREFIX + subscriptionCurrent.getUserId());
        payLumenRecordService.resettingPersonalLumens(subscriptionCurrent.getLoginName());

        // 添加操作日志
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(subscriptionCurrent.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(0l);
        log.setAfterChangeLumen(0l);
        log.setType(LumenAdminOperateType.invalidVip.getValue()); // 设置会员为无效
        log.setCreateTime(LocalDateTime.now());
        log.setLumen(0);
        log.setCreateBy(loginUser.getUserId().toString());
        sysLumenAdminOperateLogMapper.insert(log);
        insertLogBatch(payLogicPurchaseRecord.getId(),loginUser);
        return AjaxResult.success();
    }

    public UserPayRecord buildUserPayRecord(SubscriptionCurrentDto subscriptionCurrentDto,SubscriptionCurrent subscriptionCurrent){
        UserPayRecord userPayRecord = new UserPayRecord();
        userPayRecord.setUserId(subscriptionCurrentDto.getUserId());
        userPayRecord.setLoginName(subscriptionCurrentDto.getLoginName());
        userPayRecord.setDetail(subscriptionCurrentDto.getSubscriptionName());
        userPayRecord.setPlatform(PaymentPlatform.BACKEND.getCode());
        userPayRecord.setSource(PaymentSourceEnum.PLATFORM.getName());
        userPayRecord.setAmount(0l);
        userPayRecord.setAmountExcludingTax(0l);
        userPayRecord.setAfterDiscountAmount(0l);
        userPayRecord.setExternalTransactionId(subscriptionCurrent.getId().toString());
        userPayRecord.setPaymentStatus("completed");

        return userPayRecord;
    }

    public UserPayRecordItem buildUserPayRecordItem(SubscriptionCurrent subscriptionCurrent,UserPayRecord userPayRecord){
        UserPayRecordItem userPayRecordItem = new UserPayRecordItem();
        userPayRecordItem.setLoginName(subscriptionCurrent.getLoginName());
        userPayRecordItem.setUserId(subscriptionCurrent.getUserId());
        userPayRecordItem.setPlatform(PaymentPlatform.BACKEND.getCode());
        userPayRecordItem.setProductType(PaymentType.PLAN.getType());
        userPayRecordItem.setPlanLevel(subscriptionCurrent.getPlanLevel());
        userPayRecordItem.setPriceInterval(subscriptionCurrent.getPriceInterval());
        userPayRecordItem.setRecordId(userPayRecord.getId());
        userPayRecordItem.setTotalAmount(0l);
        userPayRecordItem.setUnitAmount(0l);
        userPayRecordItem.setTotalAmountExcludingTax(0l);
        userPayRecordItem.setQty(1);
        return userPayRecordItem;

    }

}
