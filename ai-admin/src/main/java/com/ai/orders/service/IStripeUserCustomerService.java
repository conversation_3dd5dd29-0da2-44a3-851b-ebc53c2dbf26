package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.StripeUserCustomer;

/**
 * Stripe 中客户信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IStripeUserCustomerService extends IService<StripeUserCustomer> {
    /**
     * 查询Stripe 中客户信息
     * 
     * @param id Stripe 中客户信息主键
     * @return Stripe 中客户信息
     */
    public StripeUserCustomer selectStripeUserCustomerById(Long id);

    /**
     * 查询Stripe 中客户信息列表
     * 
     * @param stripeUserCustomer Stripe 中客户信息
     * @return Stripe 中客户信息集合
     */
    public List<StripeUserCustomer> selectStripeUserCustomerList(StripeUserCustomer stripeUserCustomer);

    /**
     * 新增Stripe 中客户信息
     * 
     * @param stripeUserCustomer Stripe 中客户信息
     * @return 结果
     */
    public int insertStripeUserCustomer(StripeUserCustomer stripeUserCustomer);

    /**
     * 修改Stripe 中客户信息
     * 
     * @param stripeUserCustomer Stripe 中客户信息
     * @return 结果
     */
    public int updateStripeUserCustomer(StripeUserCustomer stripeUserCustomer);

    /**
     * 批量删除Stripe 中客户信息
     * 
     * @param ids 需要删除的Stripe 中客户信息主键集合
     * @return 结果
     */
    public int deleteStripeUserCustomerByIds(Long[] ids);

    /**
     * 删除Stripe 中客户信息信息
     * 
     * @param id Stripe 中客户信息主键
     * @return 结果
     */
    public int deleteStripeUserCustomerById(Long id);
}
