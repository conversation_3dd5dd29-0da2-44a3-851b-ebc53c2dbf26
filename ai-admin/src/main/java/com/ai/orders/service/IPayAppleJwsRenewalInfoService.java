package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.PayAppleJwsRenewalInfo;

/**
 * App订阅信息表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface IPayAppleJwsRenewalInfoService extends IService<PayAppleJwsRenewalInfo> {
    /**
     * 查询App订阅信息表
     * 
     * @param id App订阅信息表主键
     * @return App订阅信息表
     */
    public PayAppleJwsRenewalInfo selectPayAppleJwsRenewalInfoById(Long id);

    /**
     * 查询App订阅信息表列表
     * 
     * @param payAppleJwsRenewalInfo App订阅信息表
     * @return App订阅信息表集合
     */
    public List<PayAppleJwsRenewalInfo> selectPayAppleJwsRenewalInfoList(PayAppleJwsRenewalInfo payAppleJwsRenewalInfo);

    /**
     * 新增App订阅信息表
     * 
     * @param payAppleJwsRenewalInfo App订阅信息表
     * @return 结果
     */
    public int insertPayAppleJwsRenewalInfo(PayAppleJwsRenewalInfo payAppleJwsRenewalInfo);

    /**
     * 修改App订阅信息表
     * 
     * @param payAppleJwsRenewalInfo App订阅信息表
     * @return 结果
     */
    public int updatePayAppleJwsRenewalInfo(PayAppleJwsRenewalInfo payAppleJwsRenewalInfo);

    /**
     * 批量删除App订阅信息表
     * 
     * @param ids 需要删除的App订阅信息表主键集合
     * @return 结果
     */
    public int deletePayAppleJwsRenewalInfoByIds(Long[] ids);

    /**
     * 删除App订阅信息表信息
     * 
     * @param id App订阅信息表主键
     * @return 结果
     */
    public int deletePayAppleJwsRenewalInfoById(Long id);
}
