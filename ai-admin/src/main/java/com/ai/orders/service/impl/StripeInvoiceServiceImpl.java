package com.ai.orders.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.StripeInvoiceMapper;
import com.ai.orders.domain.StripeInvoice;
import com.ai.orders.service.IStripeInvoiceService;

/**
 * stripe账单记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class StripeInvoiceServiceImpl extends ServiceImpl<StripeInvoiceMapper, StripeInvoice> implements IStripeInvoiceService
{
    @Autowired
    private StripeInvoiceMapper stripeInvoiceMapper;

    /**
     * 查询stripe账单记录
     * 
     * @param id stripe账单记录主键
     * @return stripe账单记录
     */
    @Override
    public StripeInvoice selectStripeInvoiceById(Long id)
    {
        return stripeInvoiceMapper.selectStripeInvoiceById(id);
    }

    /**
     * 查询stripe账单记录列表
     * 
     * @param stripeInvoice stripe账单记录
     * @return stripe账单记录
     */
    @Override
    public List<StripeInvoice> selectStripeInvoiceList(StripeInvoice stripeInvoice)
    {
        return stripeInvoiceMapper.selectStripeInvoiceList(stripeInvoice);
    }

    /**
     * 新增stripe账单记录
     * 
     * @param stripeInvoice stripe账单记录
     * @return 结果
     */
    @Override
    public int insertStripeInvoice(StripeInvoice stripeInvoice)
    {
        stripeInvoice.setCreateTime(LocalDateTime.now());
        return stripeInvoiceMapper.insertStripeInvoice(stripeInvoice);
    }

    /**
     * 修改stripe账单记录
     * 
     * @param stripeInvoice stripe账单记录
     * @return 结果
     */
    @Override
    public int updateStripeInvoice(StripeInvoice stripeInvoice)
    {
        stripeInvoice.setUpdateTime(LocalDateTime.now());
        return stripeInvoiceMapper.updateStripeInvoice(stripeInvoice);
    }

    /**
     * 批量删除stripe账单记录
     * 
     * @param ids 需要删除的stripe账单记录主键
     * @return 结果
     */
    @Override
    public int deleteStripeInvoiceByIds(Long[] ids)
    {
        return stripeInvoiceMapper.deleteStripeInvoiceByIds(ids);
    }

    /**
     * 删除stripe账单记录信息
     * 
     * @param id stripe账单记录主键
     * @return 结果
     */
    @Override
    public int deleteStripeInvoiceById(Long id)
    {
        return stripeInvoiceMapper.deleteStripeInvoiceById(id);
    }
}
