package com.ai.orders.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.PayAppleUserRelationMapper;
import com.ai.orders.domain.PayAppleUserRelation;
import com.ai.orders.service.IPayAppleUserRelationService;

/**
 * Stripe 中客户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class PayAppleUserRelationServiceImpl extends ServiceImpl<PayAppleUserRelationMapper, PayAppleUserRelation> implements IPayAppleUserRelationService
{
    @Autowired
    private PayAppleUserRelationMapper payAppleUserRelationMapper;

    /**
     * 查询Stripe 中客户信息
     * 
     * @param id Stripe 中客户信息主键
     * @return Stripe 中客户信息
     */
    @Override
    public PayAppleUserRelation selectPayAppleUserRelationById(Long id)
    {
        return payAppleUserRelationMapper.selectPayAppleUserRelationById(id);
    }

    /**
     * 查询Stripe 中客户信息列表
     * 
     * @param payAppleUserRelation Stripe 中客户信息
     * @return Stripe 中客户信息
     */
    @Override
    public List<PayAppleUserRelation> selectPayAppleUserRelationList(PayAppleUserRelation payAppleUserRelation)
    {
        return payAppleUserRelationMapper.selectPayAppleUserRelationList(payAppleUserRelation);
    }

    /**
     * 新增Stripe 中客户信息
     * 
     * @param payAppleUserRelation Stripe 中客户信息
     * @return 结果
     */
    @Override
    public int insertPayAppleUserRelation(PayAppleUserRelation payAppleUserRelation)
    {
        payAppleUserRelation.setCreateTime(LocalDateTime.now());
        return payAppleUserRelationMapper.insertPayAppleUserRelation(payAppleUserRelation);
    }

    /**
     * 修改Stripe 中客户信息
     * 
     * @param payAppleUserRelation Stripe 中客户信息
     * @return 结果
     */
    @Override
    public int updatePayAppleUserRelation(PayAppleUserRelation payAppleUserRelation)
    {
        payAppleUserRelation.setUpdateTime(LocalDateTime.now());
        return payAppleUserRelationMapper.updatePayAppleUserRelation(payAppleUserRelation);
    }

    /**
     * 批量删除Stripe 中客户信息
     * 
     * @param ids 需要删除的Stripe 中客户信息主键
     * @return 结果
     */
    @Override
    public int deletePayAppleUserRelationByIds(Long[] ids)
    {
        return payAppleUserRelationMapper.deletePayAppleUserRelationByIds(ids);
    }

    /**
     * 删除Stripe 中客户信息信息
     * 
     * @param id Stripe 中客户信息主键
     * @return 结果
     */
    @Override
    public int deletePayAppleUserRelationById(Long id)
    {
        return payAppleUserRelationMapper.deletePayAppleUserRelationById(id);
    }
}
