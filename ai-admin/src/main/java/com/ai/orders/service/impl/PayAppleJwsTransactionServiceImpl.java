package com.ai.orders.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.PayAppleJwsTransactionMapper;
import com.ai.orders.domain.PayAppleJwsTransaction;
import com.ai.orders.service.IPayAppleJwsTransactionService;

/**
 * 苹果支付JWS交易记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class PayAppleJwsTransactionServiceImpl extends ServiceImpl<PayAppleJwsTransactionMapper, PayAppleJwsTransaction> implements IPayAppleJwsTransactionService
{
    @Autowired
    private PayAppleJwsTransactionMapper payAppleJwsTransactionMapper;

    /**
     * 查询苹果支付JWS交易记录
     * 
     * @param id 苹果支付JWS交易记录主键
     * @return 苹果支付JWS交易记录
     */
    @Override
    public PayAppleJwsTransaction selectPayAppleJwsTransactionById(Long id)
    {
        return payAppleJwsTransactionMapper.selectPayAppleJwsTransactionById(id);
    }

    /**
     * 查询苹果支付JWS交易记录列表
     * 
     * @param payAppleJwsTransaction 苹果支付JWS交易记录
     * @return 苹果支付JWS交易记录
     */
    @Override
    public List<PayAppleJwsTransaction> selectPayAppleJwsTransactionList(PayAppleJwsTransaction payAppleJwsTransaction)
    {
        return payAppleJwsTransactionMapper.selectPayAppleJwsTransactionList(payAppleJwsTransaction);
    }

    /**
     * 新增苹果支付JWS交易记录
     * 
     * @param payAppleJwsTransaction 苹果支付JWS交易记录
     * @return 结果
     */
    @Override
    public int insertPayAppleJwsTransaction(PayAppleJwsTransaction payAppleJwsTransaction)
    {
        payAppleJwsTransaction.setCreateTime(LocalDateTime.now());
        return payAppleJwsTransactionMapper.insertPayAppleJwsTransaction(payAppleJwsTransaction);
    }

    /**
     * 修改苹果支付JWS交易记录
     * 
     * @param payAppleJwsTransaction 苹果支付JWS交易记录
     * @return 结果
     */
    @Override
    public int updatePayAppleJwsTransaction(PayAppleJwsTransaction payAppleJwsTransaction)
    {
        payAppleJwsTransaction.setUpdateTime(LocalDateTime.now());
        return payAppleJwsTransactionMapper.updatePayAppleJwsTransaction(payAppleJwsTransaction);
    }

    /**
     * 批量删除苹果支付JWS交易记录
     * 
     * @param ids 需要删除的苹果支付JWS交易记录主键
     * @return 结果
     */
    @Override
    public int deletePayAppleJwsTransactionByIds(Long[] ids)
    {
        return payAppleJwsTransactionMapper.deletePayAppleJwsTransactionByIds(ids);
    }

    /**
     * 删除苹果支付JWS交易记录信息
     * 
     * @param id 苹果支付JWS交易记录主键
     * @return 结果
     */
    @Override
    public int deletePayAppleJwsTransactionById(Long id)
    {
        return payAppleJwsTransactionMapper.deletePayAppleJwsTransactionById(id);
    }
}
