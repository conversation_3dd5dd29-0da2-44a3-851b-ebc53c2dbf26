package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.SysLumenAdminOperateLog;

/**
 * Lumen管理操作日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface ISysLumenAdminOperateLogService extends IService<SysLumenAdminOperateLog> {
    /**
     * 查询Lumen管理操作日志
     * 
     * @param id Lumen管理操作日志主键
     * @return Lumen管理操作日志
     */
    public SysLumenAdminOperateLog selectSysLumenAdminOperateLogById(Long id);

    /**
     * 查询Lumen管理操作日志列表
     * 
     * @param sysLumenAdminOperateLog Lumen管理操作日志
     * @return Lumen管理操作日志集合
     */
    public List<SysLumenAdminOperateLog> selectSysLumenAdminOperateLogList(SysLumenAdminOperateLog sysLumenAdminOperateLog);

    /**
     * 新增Lumen管理操作日志
     * 
     * @param sysLumenAdminOperateLog Lumen管理操作日志
     * @return 结果
     */
    public int insertSysLumenAdminOperateLog(SysLumenAdminOperateLog sysLumenAdminOperateLog);

    /**
     * 修改Lumen管理操作日志
     * 
     * @param sysLumenAdminOperateLog Lumen管理操作日志
     * @return 结果
     */
    public int updateSysLumenAdminOperateLog(SysLumenAdminOperateLog sysLumenAdminOperateLog);

    /**
     * 批量删除Lumen管理操作日志
     * 
     * @param ids 需要删除的Lumen管理操作日志主键集合
     * @return 结果
     */
    public int deleteSysLumenAdminOperateLogByIds(Long[] ids);

    /**
     * 删除Lumen管理操作日志信息
     * 
     * @param id Lumen管理操作日志主键
     * @return 结果
     */
    public int deleteSysLumenAdminOperateLogById(Long id);
}
