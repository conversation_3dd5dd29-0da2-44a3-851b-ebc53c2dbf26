package com.ai.operation.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.GptExportGenInfo;

/**
 * 用户导出生图入参记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IGptExportGenInfoService extends IService<GptExportGenInfo> {
    /**
     * 查询用户导出生图入参记录
     * 
     * @param id 用户导出生图入参记录主键
     * @return 用户导出生图入参记录
     */
    public GptExportGenInfo selectGptExportGenInfoById(Long id);

    /**
     * 查询用户导出生图入参记录列表
     * 
     * @param gptExportGenInfo 用户导出生图入参记录
     * @return 用户导出生图入参记录集合
     */
    public List<GptExportGenInfo> selectGptExportGenInfoList(GptExportGenInfo gptExportGenInfo);

    /**
     * 新增用户导出生图入参记录
     * 
     * @param gptExportGenInfo 用户导出生图入参记录
     * @return 结果
     */
    public int insertGptExportGenInfo(GptExportGenInfo gptExportGenInfo);

    /**
     * 修改用户导出生图入参记录
     * 
     * @param gptExportGenInfo 用户导出生图入参记录
     * @return 结果
     */
    public int updateGptExportGenInfo(GptExportGenInfo gptExportGenInfo);

    /**
     * 批量删除用户导出生图入参记录
     * 
     * @param ids 需要删除的用户导出生图入参记录主键集合
     * @return 结果
     */
    public int deleteGptExportGenInfoByIds(Long[] ids);

    /**
     * 删除用户导出生图入参记录信息
     * 
     * @param id 用户导出生图入参记录主键
     * @return 结果
     */
    public int deleteGptExportGenInfoById(Long id);

    /**
     * 发送用户生图入参记录邮件
     *
     * @param id 用户导出生图入参记录主键
     * @return 结果
     */
    Boolean sendEmail(Long id);
}
