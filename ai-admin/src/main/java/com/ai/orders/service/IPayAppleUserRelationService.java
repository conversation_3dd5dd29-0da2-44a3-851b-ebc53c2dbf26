package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.PayAppleUserRelation;

/**
 * Stripe 中客户信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface IPayAppleUserRelationService extends IService<PayAppleUserRelation> {
    /**
     * 查询Stripe 中客户信息
     * 
     * @param id Stripe 中客户信息主键
     * @return Stripe 中客户信息
     */
    public PayAppleUserRelation selectPayAppleUserRelationById(Long id);

    /**
     * 查询Stripe 中客户信息列表
     * 
     * @param payAppleUserRelation Stripe 中客户信息
     * @return Stripe 中客户信息集合
     */
    public List<PayAppleUserRelation> selectPayAppleUserRelationList(PayAppleUserRelation payAppleUserRelation);

    /**
     * 新增Stripe 中客户信息
     * 
     * @param payAppleUserRelation Stripe 中客户信息
     * @return 结果
     */
    public int insertPayAppleUserRelation(PayAppleUserRelation payAppleUserRelation);

    /**
     * 修改Stripe 中客户信息
     * 
     * @param payAppleUserRelation Stripe 中客户信息
     * @return 结果
     */
    public int updatePayAppleUserRelation(PayAppleUserRelation payAppleUserRelation);

    /**
     * 批量删除Stripe 中客户信息
     * 
     * @param ids 需要删除的Stripe 中客户信息主键集合
     * @return 结果
     */
    public int deletePayAppleUserRelationByIds(Long[] ids);

    /**
     * 删除Stripe 中客户信息信息
     * 
     * @param id Stripe 中客户信息主键
     * @return 结果
     */
    public int deletePayAppleUserRelationById(Long id);
}
