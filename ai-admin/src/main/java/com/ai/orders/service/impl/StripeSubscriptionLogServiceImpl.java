package com.ai.orders.service.impl;

import java.util.List;

import com.ai.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.StripeSubscriptionLogMapper;
import com.ai.orders.domain.StripeSubscriptionLog;
import com.ai.orders.service.IStripeSubscriptionLogService;

/**
 * stripe 订阅日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
public class StripeSubscriptionLogServiceImpl extends ServiceImpl<StripeSubscriptionLogMapper, StripeSubscriptionLog> implements IStripeSubscriptionLogService
{
    @Autowired
    private StripeSubscriptionLogMapper stripeSubscriptionLogMapper;

    /**
     * 查询stripe 订阅日志
     * 
     * @param id stripe 订阅日志主键
     * @return stripe 订阅日志
     */
    @Override
    public StripeSubscriptionLog selectStripeSubscriptionLogById(Long id)
    {
        return stripeSubscriptionLogMapper.selectStripeSubscriptionLogById(id);
    }

    /**
     * 查询stripe 订阅日志列表
     * 
     * @param stripeSubscriptionLog stripe 订阅日志
     * @return stripe 订阅日志
     */
    @Override
    public List<StripeSubscriptionLog> selectStripeSubscriptionLogList(StripeSubscriptionLog stripeSubscriptionLog)
    {
        Map<String, Object> params = stripeSubscriptionLog.getParams();
        if (params != null){

            // 转换时间字符串为 Instant 类型的时间戳
            params.put("beginStartDate", DateUtils.convertToEpochSecond(params.get("beginStartDate").toString()));
            params.put("endStartDate", DateUtils.convertToEpochSecond(params.get("endStartDate").toString()));

            // 更新 params
            stripeSubscriptionLog.setParams(params);
        }
        return stripeSubscriptionLogMapper.selectStripeSubscriptionLogList(stripeSubscriptionLog);
    }

    /**
     * 新增stripe 订阅日志
     * 
     * @param stripeSubscriptionLog stripe 订阅日志
     * @return 结果
     */
    @Override
    public int insertStripeSubscriptionLog(StripeSubscriptionLog stripeSubscriptionLog)
    {
        stripeSubscriptionLog.setCreateTime(LocalDateTime.now());
        return stripeSubscriptionLogMapper.insertStripeSubscriptionLog(stripeSubscriptionLog);
    }

    /**
     * 修改stripe 订阅日志
     * 
     * @param stripeSubscriptionLog stripe 订阅日志
     * @return 结果
     */
    @Override
    public int updateStripeSubscriptionLog(StripeSubscriptionLog stripeSubscriptionLog)
    {
        stripeSubscriptionLog.setUpdateTime(LocalDateTime.now());
        return stripeSubscriptionLogMapper.updateStripeSubscriptionLog(stripeSubscriptionLog);
    }

    /**
     * 批量删除stripe 订阅日志
     * 
     * @param ids 需要删除的stripe 订阅日志主键
     * @return 结果
     */
    @Override
    public int deleteStripeSubscriptionLogByIds(Long[] ids)
    {
        return stripeSubscriptionLogMapper.deleteStripeSubscriptionLogByIds(ids);
    }

    /**
     * 删除stripe 订阅日志信息
     * 
     * @param id stripe 订阅日志主键
     * @return 结果
     */
    @Override
    public int deleteStripeSubscriptionLogById(Long id)
    {
        return stripeSubscriptionLogMapper.deleteStripeSubscriptionLogById(id);
    }
}
