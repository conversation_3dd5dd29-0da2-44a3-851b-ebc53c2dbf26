package com.ai.orders.service;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.orders.domain.dto.SubscriptionCurrentDto;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.SubscriptionCurrent;

/**
 * 当前订阅Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ISubscriptionCurrentService extends IService<SubscriptionCurrent> {
    /**
     * 查询当前订阅
     * 
     * @param id 当前订阅主键
     * @return 当前订阅
     */
    SubscriptionCurrent selectSubscriptionCurrentById(Long id);

    /**
     * 查询当前订阅列表
     * 
     * @param subscriptionCurrent 当前订阅
     * @return 当前订阅集合
     */
    List<SubscriptionCurrent> selectSubscriptionCurrentList(SubscriptionCurrent subscriptionCurrent);

    /**
     * 新增当前订阅
     * 
     * @param subscriptionCurrent 当前订阅
     * @return 结果
     */
    AjaxResult insertSubscriptionCurrent(SubscriptionCurrentDto subscriptionCurrent, LoginUser loginUser);

    /**
     * 修改当前订阅
     * 
     * @param subscriptionCurrent 当前订阅
     * @return 结果
     */
    int updateSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent);

    /**
     * 批量删除当前订阅
     * 
     * @param ids 需要删除的当前订阅主键集合
     * @return 结果
     */
    int deleteSubscriptionCurrentByIds(Long[] ids);

    /**
     * 删除当前订阅信息
     * 
     * @param id 当前订阅主键
     * @return 结果
     */
    int deleteSubscriptionCurrentById(Long id,LoginUser loginUser);

    List<SubscriptionCurrent> getLogicValidSubscriptionsFromDb(Long userId);

    SubscriptionCurrent getLogicValidHighSubscriptionsFromDb(Long userId);

    /**
     * 设置会员为无效
     *
     * @param id 当前订阅表主键id
     */
    AjaxResult setInvalid(Long id, LoginUser loginUser);

    void updateUserVipStatus(Long userId);

    void updateUserVipStatusBatch(List<Long> userIds);
}
