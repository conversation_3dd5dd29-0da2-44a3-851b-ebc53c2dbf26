package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.StripeScheduleLog;

/**
 * stripe预定阅记录日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IStripeScheduleLogService extends IService<StripeScheduleLog> {
    /**
     * 查询stripe预定阅记录日志
     * 
     * @param id stripe预定阅记录日志主键
     * @return stripe预定阅记录日志
     */
    public StripeScheduleLog selectStripeScheduleLogById(Long id);

    /**
     * 查询stripe预定阅记录日志列表
     * 
     * @param stripeScheduleLog stripe预定阅记录日志
     * @return stripe预定阅记录日志集合
     */
    public List<StripeScheduleLog> selectStripeScheduleLogList(StripeScheduleLog stripeScheduleLog);

    /**
     * 新增stripe预定阅记录日志
     * 
     * @param stripeScheduleLog stripe预定阅记录日志
     * @return 结果
     */
    public int insertStripeScheduleLog(StripeScheduleLog stripeScheduleLog);

    /**
     * 修改stripe预定阅记录日志
     * 
     * @param stripeScheduleLog stripe预定阅记录日志
     * @return 结果
     */
    public int updateStripeScheduleLog(StripeScheduleLog stripeScheduleLog);

    /**
     * 批量删除stripe预定阅记录日志
     * 
     * @param ids 需要删除的stripe预定阅记录日志主键集合
     * @return 结果
     */
    public int deleteStripeScheduleLogByIds(Long[] ids);

    /**
     * 删除stripe预定阅记录日志信息
     * 
     * @param id stripe预定阅记录日志主键
     * @return 结果
     */
    public int deleteStripeScheduleLogById(Long id);
}
