package com.ai.orders.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.StripeUserCustomerMapper;
import com.ai.orders.domain.StripeUserCustomer;
import com.ai.orders.service.IStripeUserCustomerService;

/**
 * Stripe 中客户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class StripeUserCustomerServiceImpl extends ServiceImpl<StripeUserCustomerMapper, StripeUserCustomer> implements IStripeUserCustomerService
{
    @Autowired
    private StripeUserCustomerMapper stripeUserCustomerMapper;

    /**
     * 查询Stripe 中客户信息
     * 
     * @param id Stripe 中客户信息主键
     * @return Stripe 中客户信息
     */
    @Override
    public StripeUserCustomer selectStripeUserCustomerById(Long id)
    {
        return stripeUserCustomerMapper.selectStripeUserCustomerById(id);
    }

    /**
     * 查询Stripe 中客户信息列表
     * 
     * @param stripeUserCustomer Stripe 中客户信息
     * @return Stripe 中客户信息
     */
    @Override
    public List<StripeUserCustomer> selectStripeUserCustomerList(StripeUserCustomer stripeUserCustomer)
    {
        return stripeUserCustomerMapper.selectStripeUserCustomerList(stripeUserCustomer);
    }

    /**
     * 新增Stripe 中客户信息
     * 
     * @param stripeUserCustomer Stripe 中客户信息
     * @return 结果
     */
    @Override
    public int insertStripeUserCustomer(StripeUserCustomer stripeUserCustomer)
    {
        stripeUserCustomer.setCreateTime(LocalDateTime.now());
        return stripeUserCustomerMapper.insertStripeUserCustomer(stripeUserCustomer);
    }

    /**
     * 修改Stripe 中客户信息
     * 
     * @param stripeUserCustomer Stripe 中客户信息
     * @return 结果
     */
    @Override
    public int updateStripeUserCustomer(StripeUserCustomer stripeUserCustomer)
    {
        stripeUserCustomer.setUpdateTime(LocalDateTime.now());
        return stripeUserCustomerMapper.updateStripeUserCustomer(stripeUserCustomer);
    }

    /**
     * 批量删除Stripe 中客户信息
     * 
     * @param ids 需要删除的Stripe 中客户信息主键
     * @return 结果
     */
    @Override
    public int deleteStripeUserCustomerByIds(Long[] ids)
    {
        return stripeUserCustomerMapper.deleteStripeUserCustomerByIds(ids);
    }

    /**
     * 删除Stripe 中客户信息信息
     * 
     * @param id Stripe 中客户信息主键
     * @return 结果
     */
    @Override
    public int deleteStripeUserCustomerById(Long id)
    {
        return stripeUserCustomerMapper.deleteStripeUserCustomerById(id);
    }
}
