package com.ai.orders.service.impl;

import java.util.List;

import com.ai.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.StripeScheduleLogMapper;
import com.ai.orders.domain.StripeScheduleLog;
import com.ai.orders.service.IStripeScheduleLogService;

/**
 * stripe预定阅记录日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
public class StripeScheduleLogServiceImpl extends ServiceImpl<StripeScheduleLogMapper, StripeScheduleLog> implements IStripeScheduleLogService
{
    @Autowired
    private StripeScheduleLogMapper stripeScheduleLogMapper;

    /**
     * 查询stripe预定阅记录日志
     * 
     * @param id stripe预定阅记录日志主键
     * @return stripe预定阅记录日志
     */
    @Override
    public StripeScheduleLog selectStripeScheduleLogById(Long id)
    {
        return stripeScheduleLogMapper.selectStripeScheduleLogById(id);
    }

    /**
     * 查询stripe预定阅记录日志列表
     * 
     * @param stripeScheduleLog stripe预定阅记录日志
     * @return stripe预定阅记录日志
     */
    @Override
    public List<StripeScheduleLog> selectStripeScheduleLogList(StripeScheduleLog stripeScheduleLog)
    {
        Map<String, Object> params = stripeScheduleLog.getParams();
        if (params != null){

            // 转换时间字符串为 Instant 类型的时间戳
            params.put("beginStartDate", DateUtils.convertToEpochSecond(params.get("beginStartDate").toString()));
            params.put("endStartDate", DateUtils.convertToEpochSecond(params.get("endStartDate").toString()));

            // 更新 params
            stripeScheduleLog.setParams(params);
        }
        return stripeScheduleLogMapper.selectStripeScheduleLogList(stripeScheduleLog);
    }

    /**
     * 新增stripe预定阅记录日志
     * 
     * @param stripeScheduleLog stripe预定阅记录日志
     * @return 结果
     */
    @Override
    public int insertStripeScheduleLog(StripeScheduleLog stripeScheduleLog)
    {
        stripeScheduleLog.setCreateTime(LocalDateTime.now());
        return stripeScheduleLogMapper.insertStripeScheduleLog(stripeScheduleLog);
    }

    /**
     * 修改stripe预定阅记录日志
     * 
     * @param stripeScheduleLog stripe预定阅记录日志
     * @return 结果
     */
    @Override
    public int updateStripeScheduleLog(StripeScheduleLog stripeScheduleLog)
    {
        stripeScheduleLog.setUpdateTime(LocalDateTime.now());
        return stripeScheduleLogMapper.updateStripeScheduleLog(stripeScheduleLog);
    }

    /**
     * 批量删除stripe预定阅记录日志
     * 
     * @param ids 需要删除的stripe预定阅记录日志主键
     * @return 结果
     */
    @Override
    public int deleteStripeScheduleLogByIds(Long[] ids)
    {
        return stripeScheduleLogMapper.deleteStripeScheduleLogByIds(ids);
    }

    /**
     * 删除stripe预定阅记录日志信息
     * 
     * @param id stripe预定阅记录日志主键
     * @return 结果
     */
    @Override
    public int deleteStripeScheduleLogById(Long id)
    {
        return stripeScheduleLogMapper.deleteStripeScheduleLogById(id);
    }
}
