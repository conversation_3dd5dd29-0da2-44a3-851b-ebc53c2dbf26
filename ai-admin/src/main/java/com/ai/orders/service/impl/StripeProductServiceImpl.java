package com.ai.orders.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.StripeProductMapper;
import com.ai.orders.domain.StripeProduct;
import com.ai.orders.service.IStripeProductService;

/**
 * 存储 Stripe 商品和价格信息的Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class StripeProductServiceImpl extends ServiceImpl<StripeProductMapper, StripeProduct> implements IStripeProductService
{
    @Autowired
    private StripeProductMapper stripeProductMapper;

    /**
     * 查询存储 Stripe 商品和价格信息的
     * 
     * @param id 存储 Stripe 商品和价格信息的主键
     * @return 存储 Stripe 商品和价格信息的
     */
    @Override
    public StripeProduct selectStripeProductById(Long id)
    {
        return stripeProductMapper.selectStripeProductById(id);
    }

    /**
     * 查询存储 Stripe 商品和价格信息的列表
     * 
     * @param stripeProduct 存储 Stripe 商品和价格信息的
     * @return 存储 Stripe 商品和价格信息的
     */
    @Override
    public List<StripeProduct> selectStripeProductList(StripeProduct stripeProduct)
    {
        return stripeProductMapper.selectStripeProductList(stripeProduct);
    }

    /**
     * 新增存储 Stripe 商品和价格信息的
     * 
     * @param stripeProduct 存储 Stripe 商品和价格信息的
     * @return 结果
     */
    @Override
    public int insertStripeProduct(StripeProduct stripeProduct)
    {
        stripeProduct.setCreateTime(LocalDateTime.now());
        return stripeProductMapper.insertStripeProduct(stripeProduct);
    }

    /**
     * 修改存储 Stripe 商品和价格信息的
     * 
     * @param stripeProduct 存储 Stripe 商品和价格信息的
     * @return 结果
     */
    @Override
    public int updateStripeProduct(StripeProduct stripeProduct)
    {
        stripeProduct.setUpdateTime(LocalDateTime.now());
        return stripeProductMapper.updateStripeProduct(stripeProduct);
    }

    /**
     * 批量删除存储 Stripe 商品和价格信息的
     * 
     * @param ids 需要删除的存储 Stripe 商品和价格信息的主键
     * @return 结果
     */
    @Override
    public int deleteStripeProductByIds(Long[] ids)
    {
        return stripeProductMapper.deleteStripeProductByIds(ids);
    }

    /**
     * 删除存储 Stripe 商品和价格信息的信息
     * 
     * @param id 存储 Stripe 商品和价格信息的主键
     * @return 结果
     */
    @Override
    public int deleteStripeProductById(Long id)
    {
        return stripeProductMapper.deleteStripeProductById(id);
    }
}
