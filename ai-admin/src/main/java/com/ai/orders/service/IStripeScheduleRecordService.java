package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.StripeScheduleRecord;

/**
 * stripe 预定阅记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IStripeScheduleRecordService extends IService<StripeScheduleRecord> {
    /**
     * 查询stripe 预定阅记录
     * 
     * @param id stripe 预定阅记录主键
     * @return stripe 预定阅记录
     */
    public StripeScheduleRecord selectStripeScheduleRecordById(Long id);

    /**
     * 查询stripe 预定阅记录列表
     * 
     * @param stripeScheduleRecord stripe 预定阅记录
     * @return stripe 预定阅记录集合
     */
    public List<StripeScheduleRecord> selectStripeScheduleRecordList(StripeScheduleRecord stripeScheduleRecord);

    /**
     * 新增stripe 预定阅记录
     * 
     * @param stripeScheduleRecord stripe 预定阅记录
     * @return 结果
     */
    public int insertStripeScheduleRecord(StripeScheduleRecord stripeScheduleRecord);

    /**
     * 修改stripe 预定阅记录
     * 
     * @param stripeScheduleRecord stripe 预定阅记录
     * @return 结果
     */
    public int updateStripeScheduleRecord(StripeScheduleRecord stripeScheduleRecord);

    /**
     * 批量删除stripe 预定阅记录
     * 
     * @param ids 需要删除的stripe 预定阅记录主键集合
     * @return 结果
     */
    public int deleteStripeScheduleRecordByIds(Long[] ids);

    /**
     * 删除stripe 预定阅记录信息
     * 
     * @param id stripe 预定阅记录主键
     * @return 结果
     */
    public int deleteStripeScheduleRecordById(Long id);
}
