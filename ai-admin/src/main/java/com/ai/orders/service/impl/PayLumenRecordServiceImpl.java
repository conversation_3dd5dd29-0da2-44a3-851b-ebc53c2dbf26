package com.ai.orders.service.impl;

import java.util.*;

import com.ai.admin.domain.GptUser;
import com.ai.common.constant.HttpStatus;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.LumenAdminOperateType;
import com.ai.common.enums.VipPlatform;
import com.ai.common.utils.DateUtils;
import com.ai.constants.LogicParamsCons;
import com.ai.enums.LumenChangeSourceEnum;
import com.ai.enums.LumenChangeTypeEnum;
import com.ai.operation.service.LumenChangeRecordService;
import com.ai.orders.controller.rep.AddOrReduceLumenReq;
import com.ai.orders.domain.SysLumenAdminOperateLog;
import com.ai.orders.domain.mongo.LogLumenCost;
import com.ai.orders.mapper.SysLumenAdminOperateLogMapper;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;

import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.PayLumenRecordMapper;
import com.ai.orders.domain.PayLumenRecord;
import com.ai.orders.service.IPayLumenRecordService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * lumen实时记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Service
@Slf4j
public class PayLumenRecordServiceImpl extends ServiceImpl<PayLumenRecordMapper, PayLumenRecord> implements IPayLumenRecordService
{
    @Autowired
    private PayLumenRecordMapper payLumenRecordMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;

    @Autowired
    private SysLumenAdminOperateLogMapper sysLumenAdminOperateLogMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private LumenChangeRecordService lumenChangeRecordService;

    /**
     * 查询lumen实时记录
     * 
     * @param id lumen实时记录主键
     * @return lumen实时记录
     */
    @Override
    public PayLumenRecord selectPayLumenRecordById(Long id)
    {
        return payLumenRecordMapper.selectPayLumenRecordById(id);
    }

    /**
     * 查询lumen实时记录列表
     * 
     * @param payLumenRecord lumen实时记录
     * @return lumen实时记录
     */
    @Override
    public List<PayLumenRecord> selectPayLumenRecordList(PayLumenRecord payLumenRecord)
    {
        return payLumenRecordMapper.selectPayLumenRecordList(payLumenRecord);
    }

    /**
     * 新增lumen实时记录
     * 
     * @param payLumenRecord lumen实时记录
     * @return 结果
     */
    @Override
    public int insertPayLumenRecord(PayLumenRecord payLumenRecord,LoginUser loginUser)
    {
        payLumenRecord.setCreateTime(LocalDateTime.now());
        payLumenRecord.setLumenLeftQty(payLumenRecord.getLumenQty());
        payLumenRecord.setCurrentPeriodEnd(DateUtils.getTimestamp(payLumenRecord.getCurrentPeriodEnDate()));
        payLumenRecord.setCurrentPeriodStart(DateUtils.getTimestamp(payLumenRecord.getCurrentPeriodStartDate()));
        payLumenRecord.setLogicPeriodEnd(payLumenRecord.getCurrentPeriodEnd());
        payLumenRecord.setLogicPeriodStart(payLumenRecord.getCurrentPeriodStart());
        payLumenRecord.setVipPlatForm(VipPlatform.GIFT.GIFT.getPlatformName());
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        Long happenTime = payLumenRecord.getCurrentPeriodStart();
        // 如果在当前订阅期限内，清空 Redis 缓存
        if (isInSubscriptionPeriod(payLumenRecord, currentTimestamp)) {
            log.info("在当前订阅期限内，清空 Redis 缓存");
            resettingPersonalLumens(payLumenRecord.getLoginName());
            happenTime = currentTimestamp;
        }
        Long lumen = payLumenRecord.getLumenQty();

        // 添加lumen
        payLumenRecordMapper.insert(payLumenRecord);

        // 用户lumen变化记录
        GptUser gptUser = new GptUser();
        gptUser.setLoginName(payLumenRecord.getLoginName());
        gptUser.setId(payLumenRecord.getUserId());
        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(gptUser, Math.toIntExact(payLumenRecord.getLumenQty()), LumenChangeTypeEnum.ADD.getValue(),
                LumenChangeSourceEnum.PLATFORM.getValue(),payLumenRecord.getDetail(),null,payLumenRecord.getId(),happenTime);
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(payLumenRecord.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(lumen);
        log.setAfterChangeLumen(lumen);
        log.setType(LumenAdminOperateType.gift.getValue()); // 1 表示减少操作
        log.setCreateTime(LocalDateTime.now());
        log.setLumen(Math.toIntExact(lumen));
        log.setCreateBy(loginUser.getUserId().toString());
        return sysLumenAdminOperateLogMapper.insert(log);
    }

    /**
     * 修改lumen实时记录
     * 
     * @param payLumenRecord lumen实时记录
     * @return 结果
     */
    @Override
    public int updatePayLumenRecord(PayLumenRecord payLumenRecord)
    {
        payLumenRecord.setUpdateTime(LocalDateTime.now());
        return payLumenRecordMapper.updatePayLumenRecord(payLumenRecord);
    }

    /**
     * 批量删除lumen实时记录
     * 
     * @param ids 需要删除的lumen实时记录主键
     * @return 结果
     */
    @Override
    public int deletePayLumenRecordByIds(Long[] ids)
    {
        return payLumenRecordMapper.deletePayLumenRecordByIds(ids);
    }

    /**
     * 删除lumen实时记录信息
     * 
     * @param id lumen实时记录主键
     * @return 结果
     */
    @Override
    public int deletePayLumenRecordById(Long id)
    {
        return payLumenRecordMapper.deletePayLumenRecordById(id);
    }

    /**
     * 减少lumen
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult reduceLumen(AddOrReduceLumenReq  req, LoginUser loginUser) {

        Long id = req.getId();
        Long lumen = req.getLumen();
        String detail = req.getDetail();

        // 查询会员有效点数和充值有效点数
        PayLumenRecord payLumenRecord = getValidPayLumenRecord(id);
        if (payLumenRecord == null) {
            return AjaxResult.error("lumen 实时记录不存在 或者无效");
        }

        Long beforeChangeLumen = payLumenRecord.getLumenLeftQty();
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());

        // 减少 lumen 并校验余额
        if (!reduceLumenAndValidate(payLumenRecord, Math.toIntExact(lumen), currentTimestamp)) {
            return AjaxResult.error("lumen不足");
        }
        try {
            // 更新记录
            payLumenRecord.setUpdateTime(LocalDateTime.now());
            payLumenRecordMapper.updatePayLumenRecord(payLumenRecord);
        }catch (Exception e){
            log.error("更新错误",e);
            return AjaxResult.error("lumen不足");
        }

        Long happenTime =  payLumenRecord.getCurrentPeriodStart();
        // 如果在当前订阅期限内，清空 Redis 缓存
        if (isInSubscriptionPeriod(payLumenRecord, currentTimestamp)) {
            log.info("当前订阅期内减少lumen量");
            happenTime = currentTimestamp;
            resettingPersonalLumens(payLumenRecord.getLoginName());
        } else {
            log.info("当前订阅期外减少lumen量");
        }

        // 用户lumen变化记录
        GptUser gptUser = new GptUser();
        gptUser.setLoginName(payLumenRecord.getLoginName());
        gptUser.setId(payLumenRecord.getUserId());
        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(gptUser, Math.toIntExact(lumen), LumenChangeTypeEnum.DEDUCT.getValue(),
                LumenChangeSourceEnum.PLATFORM.getValue(),detail,null,id,happenTime);

        // 记录操作日志
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(payLumenRecord.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(beforeChangeLumen);
        log.setAfterChangeLumen(payLumenRecord.getLumenLeftQty());
        log.setLumen(Math.toIntExact(lumen));
        log.setType(LumenAdminOperateType.reduce.getValue()); // 1 表示减少操作
        log.setCreateTime(LocalDateTime.now());
        log.setCreateBy(loginUser.getUserId().toString());
        sysLumenAdminOperateLogMapper.insert(log);

        return AjaxResult.success();
    }


    /**
     * 设置无效
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult setLumenInvalid(AddOrReduceLumenReq req, LoginUser loginUser) {

        Long id = req.getId();
        Long lumen = req.getLumen();
        String detail = req.getDetail();

        // 查询会员有效点数和充值有效点数
        PayLumenRecord payLumenRecord = getValidPayLumenRecord(id);
        if (payLumenRecord == null) {
            return AjaxResult.error("lumen 实时记录不存在 或者已经是无效");
        }

        payLumenRecord.setInvalid(1);
        payLumenRecord.setUpdateTime(LocalDateTime.now());
        payLumenRecordMapper.updatePayLumenRecord(payLumenRecord);

        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        // 如果在当前订阅期限内，清空 Redis 缓存
        if (isInSubscriptionPeriod(payLumenRecord, currentTimestamp)) {
            log.info("当前订阅期内设置lumen为无效");
            resettingPersonalLumens(payLumenRecord.getLoginName());
            // 用户lumen变化记录
            GptUser gptUser = new GptUser();
            gptUser.setLoginName(payLumenRecord.getLoginName());
            gptUser.setId(payLumenRecord.getUserId());
            lumenChangeRecordService.saveOrUpdateLumenChangeRecord(gptUser, Math.toIntExact(lumen), LumenChangeTypeEnum.DEDUCT.getValue(),
                    LumenChangeSourceEnum.PLATFORM.getValue(),detail,null,id,currentTimestamp);
        } else {
            log.info("当前订阅期外设置lumen为无效");
            // 如果设置未来失效直接删除记录
            lumenChangeRecordService.removeLumenChangeRecordByRelationId(id);
        }



        // 记录操作日志
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(payLumenRecord.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(payLumenRecord.getLumenLeftQty());
        log.setAfterChangeLumen(payLumenRecord.getLumenLeftQty());
        log.setType(LumenAdminOperateType.invalid.getValue()); // 1 表示减少操作
        log.setCreateTime(LocalDateTime.now());
        log.setCreateBy(loginUser.getUserId().toString());
        sysLumenAdminOperateLogMapper.insert(log);

        return AjaxResult.success();
    }

    /**
     * 赠送lumen
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult getGiftLumen(AddOrReduceLumenReq req, LoginUser loginUser) {

        Long id = req.getId();
        Long lumen = req.getLumen();
        String detail = req.getDetail();

        // 查询会员有效点数和充值有效点数
        PayLumenRecord payLumenRecord = getValidPayLumenRecord(id);
        if (payLumenRecord == null) {
            return AjaxResult.error("lumen 实时记录不存在 或者无效");
        }

        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        Long happenTime =  payLumenRecord.getCurrentPeriodStart();
        // 如果在当前订阅期限内，清空 Redis 缓存
        if (isInSubscriptionPeriod(payLumenRecord, currentTimestamp)) {
            log.info("当前订阅期内赠送lumen");
            happenTime = currentTimestamp;
            resettingPersonalLumens(payLumenRecord.getLoginName());
        } else {
            log.info("当前订阅期外赠送lumen");
        }

        PayLumenRecord newPayLumenRecord = new PayLumenRecord();
        BeanUtils.copyProperties(payLumenRecord, newPayLumenRecord);
        newPayLumenRecord.setId(null);
        newPayLumenRecord.setType(LumenAdminOperateType.gift.getValue());
        newPayLumenRecord.setLumenQty(lumen);
        newPayLumenRecord.setLumenLeftQty(lumen);
        newPayLumenRecord.setCreateTime(LocalDateTime.now());
        payLumenRecordMapper.insert(newPayLumenRecord);

        // 用户lumen变化记录
        GptUser gptUser = new GptUser();
        gptUser.setLoginName(payLumenRecord.getLoginName());
        gptUser.setId(payLumenRecord.getUserId());
        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(gptUser, Math.toIntExact(lumen), LumenChangeTypeEnum.ADD.getValue(),
                LumenChangeSourceEnum.PLATFORM.getValue(),detail,null,newPayLumenRecord.getId(),happenTime);


        // 记录操作日志
        SysLumenAdminOperateLog log = new SysLumenAdminOperateLog();
        log.setOperName(loginUser.getUsername());
        log.setLumenRecordId(newPayLumenRecord.getId());
        log.setDeptName(loginUser.getUser().getDept().getDeptName());
        log.setBeforeChangeLumen(lumen);
        log.setAfterChangeLumen(lumen);
        log.setType(LumenAdminOperateType.gift.getValue()); // 1 表示减少操作
        log.setCreateTime(LocalDateTime.now());
        log.setLumen(Math.toIntExact(lumen));
        log.setCreateBy(loginUser.getUserId().toString());
        sysLumenAdminOperateLogMapper.insert(log);

        return AjaxResult.success();
    }

    /**
     * 查看lumen 操作日志
     *
     * @param lumenRecordId lumen实时记录主键
     * @return 结果
     */
    public List<SysLumenAdminOperateLog> getLumenAdminOperateLogList(Long lumenRecordId) {

        LambdaQueryWrapper<SysLumenAdminOperateLog> lol = new LambdaQueryWrapper<>();
        lol.eq(SysLumenAdminOperateLog::getLumenRecordId, lumenRecordId);
        lol.orderByDesc(SysLumenAdminOperateLog::getCreateTime);
        List<SysLumenAdminOperateLog> sysLumenAdminOperateLogs = sysLumenAdminOperateLogMapper.selectList(lol);
        return  sysLumenAdminOperateLogs;
    }

    /**
     * 获取有效的 PayLumenRecord
     */
    private PayLumenRecord getValidPayLumenRecord(Long id) {
        LambdaQueryWrapper<PayLumenRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayLumenRecord::getId, id);
        queryWrapper.eq(PayLumenRecord::getInvalid, 0);
        return payLumenRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 判断是否在订阅期内
     */
    private boolean isInSubscriptionPeriod(PayLumenRecord record, Long currentTimestamp) {
        return record.getLogicPeriodStart() < currentTimestamp && currentTimestamp < record.getLogicPeriodEnd();
    }

    /**
     * 减少 lumen 并校验余额
     */
    private boolean reduceLumenAndValidate(PayLumenRecord record, Integer lumen, Long currentTimestamp) {
        record.setLumenLeftQty(record.getLumenLeftQty() - lumen);
        return record.getLumenLeftQty() >= 0;
    }

    @Override
    public TableDataInfo getLumenCosts(Integer pageSize,
                                                String markFileId, Boolean isNext,String loginName) {
        // 校验页码和每页记录数的有效性
        if (pageSize == null || pageSize < 1) {
            throw new IllegalArgumentException("Page and pageSize must be greater than 0");
        }
        // 创建时间过滤条件
        Criteria criteria = new Criteria();

        // 创建分页查询
        Query query = new Query(criteria);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(markFileId)) {
            if (isNext) {
                query.addCriteria(Criteria.where("id").gt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.ASC, "id"));
            } else {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.DESC, "id"));
            }
        } else {
            query.with(Sort.by(Sort.Direction.ASC, "id"));
        }

        query.addCriteria(Criteria.where("loginName").is(loginName));


        query.limit(pageSize);

        // 查询符合条件的数据
        List<LogLumenCost> logLumenCosts = mongoTemplate.find(query, LogLumenCost.class);

        if (StringUtils.isNotBlank(markFileId) && !isNext) {
            Collections.reverse(logLumenCosts);
        }

        // 封装响应数据
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(logLumenCosts);
        rspData.setTotal(10);
        return rspData;
    }



    public void resettingPersonalLumens(String loginName) {
        if (StringUtils.isBlank(loginName)) {
            return;
        }

        log.info("开始重置用户:{} 有效的点数和使用的点数", loginName);
        try {
            //删除用户有效的点数
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_RECHARGE_TOTAL_LUMENS, loginName);
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_RECHARGE_USE_LUMENS, loginName);
            //删除用户使用的点数
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_VIP_TOTAL_LUMENS, loginName);
            redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.USER_VIP_USE_LUMENS, loginName);
            log.info("结束重置用户:{} 有效的点数和使用的点数", loginName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
