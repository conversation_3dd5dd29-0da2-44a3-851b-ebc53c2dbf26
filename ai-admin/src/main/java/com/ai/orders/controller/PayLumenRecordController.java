package com.ai.orders.controller;

import java.time.LocalDate;
import java.util.List;

import com.ai.common.core.domain.model.LoginUser;
import com.ai.orders.controller.rep.AddOrReduceLumenReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.PayLumenRecord;
import com.ai.orders.service.IPayLumenRecordService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * lumen实时记录Controller
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@RestController
@Api(value = "lumen实时记录控制器", tags = {"lumen实时记录管理"})
@RequestMapping("/orders/lumenRecord")
public class PayLumenRecordController extends BaseController {
    @Autowired
    private IPayLumenRecordService payLumenRecordService;

    /**
     * 查询lumen实时记录列表
     */
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:list')")
    @ApiOperation("查询lumen实时记录列表")
    @GetMapping("/list")
    public TableDataInfo list(PayLumenRecord payLumenRecord) {
        startPage();
        List<PayLumenRecord> list = payLumenRecordService.selectPayLumenRecordList(payLumenRecord);
        return getDataTable(list);
    }

    /**
     * 导出lumen实时记录列表
     */
    @ApiOperation("导出lumen实时记录列表")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:export')")
    @Log(title = "lumen实时记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayLumenRecord payLumenRecord) {
        List<PayLumenRecord> list = payLumenRecordService.selectPayLumenRecordList(payLumenRecord);
        ExcelUtil<PayLumenRecord> util = new ExcelUtil<PayLumenRecord>(PayLumenRecord.class);
        util.exportExcel(response, list, "lumen实时记录数据");
    }

    /**
     * 获取lumen实时记录详细信息
     */
    @ApiOperation("获取lumen实时记录详细信息")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payLumenRecordService.selectPayLumenRecordById(id));
    }

    /**
     * 新增lumen实时记录
     */
    @ApiOperation("新增lumen实时记录")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:add')")
    @Log(title = "lumen实时记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayLumenRecord payLumenRecord) {
        LoginUser loginUser = getLoginUser();
        return toAjax(payLumenRecordService.insertPayLumenRecord(payLumenRecord,loginUser));
    }

    /**
     * 修改lumen实时记录
     */
    @ApiOperation("修改lumen实时记录")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:edit')")
    @Log(title = "lumen实时记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayLumenRecord payLumenRecord) {
        return toAjax(payLumenRecordService.updatePayLumenRecord(payLumenRecord));
    }

    /**
     * 删除lumen实时记录
     */
    @ApiOperation("删除lumen实时记录")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:remove')")
    @Log(title = "lumen实时记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payLumenRecordService.deletePayLumenRecordByIds(ids));
    }

    /**
     * 减少lumen
     *
     * @return 结果
     */
    @ApiOperation("减少lumen量")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:edit')")
    @Log(title = "减少lumen量", businessType = BusinessType.UPDATE)
    @PostMapping("/reduceLumen")
    public AjaxResult reduceLumen(@RequestBody AddOrReduceLumenReq  req){
        LoginUser loginUser = getLoginUser();
        return payLumenRecordService.reduceLumen(req, loginUser);
    }

    /**
     * 设置无效
     *
     * @return 结果
     */
    @ApiOperation("设置无效")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:edit')")
    @Log(title = "设置无效", businessType = BusinessType.UPDATE)
    @PostMapping("/setLumenInvalid")
    public AjaxResult setInvalid(@RequestBody AddOrReduceLumenReq req){
        LoginUser loginUser = getLoginUser();
        return payLumenRecordService.setLumenInvalid(req, loginUser);
    }


    /**
     * 赠送lumen
     *
     * @return 结果
     */
    @ApiOperation("赠送lumen")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:edit')")
    @Log(title = "赠送lumen", businessType = BusinessType.UPDATE)
    @PostMapping("/getGiftLumen")
    public AjaxResult addLumen(@RequestBody AddOrReduceLumenReq req){
        LoginUser loginUser = getLoginUser();
        return payLumenRecordService.getGiftLumen(req, loginUser);
    }

    /**
     * 查看lumen 操作日志
     *
     * @param lumenRecordId lumen实时记录主键
     * @return 结果
     */
    @ApiOperation("查看lumen 操作日志")
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:query')")
    @GetMapping("/getLumenAdminOperateLogList/{lumenRecordId}")
    public AjaxResult getLumenAdminOperateLogList(@PathVariable("lumenRecordId") Long lumenRecordId){
        return success(payLumenRecordService.getLumenAdminOperateLogList(lumenRecordId));
    }

    /**
     * 查看lumen 消费日志
     *
     * @param loginName 用户LoginName
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('orders:lumenRecord:list')")
    @ApiOperation("查看lumen 消费日志")
    @GetMapping("/lumen-list")
    public TableDataInfo getLumenCosts( @RequestParam(value =  "pageSize" ) Integer pageSize,
                                @RequestParam(value =  "markFileId" , required = false) String markFileId,
                                @RequestParam(value =  "isNext" , required = false) Boolean isNext,
                                @RequestParam(value =  "loginName" ) String loginName){

        return payLumenRecordService.getLumenCosts(pageSize, markFileId, isNext, loginName);
    }

}
