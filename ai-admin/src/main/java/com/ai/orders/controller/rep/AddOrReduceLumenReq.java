package com.ai.orders.controller.rep;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "增加lumen 数量传入参数")
public class AddOrReduceLumenReq {

    @ApiModelProperty("lumen 实时记录id")
    private Long id;

    @ApiModelProperty("lumen 数")
    private Long lumen;

    @ApiModelProperty("理由 ")
    private String  detail;

}
