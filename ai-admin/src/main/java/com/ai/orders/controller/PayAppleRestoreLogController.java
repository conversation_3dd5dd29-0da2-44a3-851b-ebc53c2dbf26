package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.PayAppleRestoreLog;
import com.ai.orders.service.IPayAppleRestoreLogService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 会员restore记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@Api(value = "会员restore记录控制器", tags = {"会员restore记录管理"})
@RequestMapping("/orders/restoreLog")
public class PayAppleRestoreLogController extends BaseController {
    @Autowired
    private IPayAppleRestoreLogService payAppleRestoreLogService;

    /**
     * 查询会员restore记录列表
     */
    @PreAuthorize("@ss.hasPermi('orders:restoreLog:list')")
    @ApiOperation("查询会员restore记录列表")
    @GetMapping("/list")
    public TableDataInfo list(PayAppleRestoreLog payAppleRestoreLog) {
        startPage();
        List<PayAppleRestoreLog> list = payAppleRestoreLogService.selectPayAppleRestoreLogList(payAppleRestoreLog);
        return getDataTable(list);
    }

    /**
     * 导出会员restore记录列表
     */
    @ApiOperation("导出会员restore记录列表")
    @PreAuthorize("@ss.hasPermi('orders:restoreLog:export')")
    @Log(title = "会员restore记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayAppleRestoreLog payAppleRestoreLog) {
        List<PayAppleRestoreLog> list = payAppleRestoreLogService.selectPayAppleRestoreLogList(payAppleRestoreLog);
        ExcelUtil<PayAppleRestoreLog> util = new ExcelUtil<PayAppleRestoreLog>(PayAppleRestoreLog.class);
        util.exportExcel(response, list, "会员restore记录数据");
    }

    /**
     * 获取会员restore记录详细信息
     */
    @ApiOperation("获取会员restore记录详细信息")
    @PreAuthorize("@ss.hasPermi('orders:restoreLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payAppleRestoreLogService.selectPayAppleRestoreLogById(id));
    }

    /**
     * 新增会员restore记录
     */
    @ApiOperation("新增会员restore记录")
    @PreAuthorize("@ss.hasPermi('orders:restoreLog:add')")
    @Log(title = "会员restore记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayAppleRestoreLog payAppleRestoreLog) {
        return toAjax(payAppleRestoreLogService.insertPayAppleRestoreLog(payAppleRestoreLog));
    }

    /**
     * 修改会员restore记录
     */
    @ApiOperation("修改会员restore记录")
    @PreAuthorize("@ss.hasPermi('orders:restoreLog:edit')")
    @Log(title = "会员restore记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayAppleRestoreLog payAppleRestoreLog) {
        return toAjax(payAppleRestoreLogService.updatePayAppleRestoreLog(payAppleRestoreLog));
    }

    /**
     * 删除会员restore记录
     */
    @ApiOperation("删除会员restore记录")
    @PreAuthorize("@ss.hasPermi('orders:restoreLog:remove')")
    @Log(title = "会员restore记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payAppleRestoreLogService.deletePayAppleRestoreLogByIds(ids));
    }
}
