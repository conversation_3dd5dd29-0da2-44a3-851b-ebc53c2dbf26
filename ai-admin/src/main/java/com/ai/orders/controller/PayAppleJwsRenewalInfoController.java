package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.PayAppleJwsRenewalInfo;
import com.ai.orders.service.IPayAppleJwsRenewalInfoService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * App订阅信息表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@Api(value = "App订阅信息表控制器", tags = {"App订阅信息表管理"})
@RequestMapping("/orders/info")
public class PayAppleJwsRenewalInfoController extends BaseController {
    @Autowired
    private IPayAppleJwsRenewalInfoService payAppleJwsRenewalInfoService;

    /**
     * 查询App订阅信息表列表
     */
    @PreAuthorize("@ss.hasPermi('orders:info:list')")
    @ApiOperation("查询App订阅信息表列表")
    @GetMapping("/list")
    public TableDataInfo list(PayAppleJwsRenewalInfo payAppleJwsRenewalInfo) {
        startPage();
        List<PayAppleJwsRenewalInfo> list = payAppleJwsRenewalInfoService.selectPayAppleJwsRenewalInfoList(payAppleJwsRenewalInfo);
        return getDataTable(list);
    }

    /**
     * 导出App订阅信息表列表
     */
    @ApiOperation("导出App订阅信息表列表")
    @PreAuthorize("@ss.hasPermi('orders:info:export')")
    @Log(title = "App订阅信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayAppleJwsRenewalInfo payAppleJwsRenewalInfo) {
        List<PayAppleJwsRenewalInfo> list = payAppleJwsRenewalInfoService.selectPayAppleJwsRenewalInfoList(payAppleJwsRenewalInfo);
        ExcelUtil<PayAppleJwsRenewalInfo> util = new ExcelUtil<PayAppleJwsRenewalInfo>(PayAppleJwsRenewalInfo.class);
        util.exportExcel(response, list, "App订阅信息表数据");
    }

    /**
     * 获取App订阅信息表详细信息
     */
    @ApiOperation("获取App订阅信息表详细信息")
    @PreAuthorize("@ss.hasPermi('orders:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payAppleJwsRenewalInfoService.selectPayAppleJwsRenewalInfoById(id));
    }

    /**
     * 新增App订阅信息表
     */
    @ApiOperation("新增App订阅信息表")
    @PreAuthorize("@ss.hasPermi('orders:info:add')")
    @Log(title = "App订阅信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayAppleJwsRenewalInfo payAppleJwsRenewalInfo) {
        return toAjax(payAppleJwsRenewalInfoService.insertPayAppleJwsRenewalInfo(payAppleJwsRenewalInfo));
    }

    /**
     * 修改App订阅信息表
     */
    @ApiOperation("修改App订阅信息表")
    @PreAuthorize("@ss.hasPermi('orders:info:edit')")
    @Log(title = "App订阅信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayAppleJwsRenewalInfo payAppleJwsRenewalInfo) {
        return toAjax(payAppleJwsRenewalInfoService.updatePayAppleJwsRenewalInfo(payAppleJwsRenewalInfo));
    }

    /**
     * 删除App订阅信息表
     */
    @ApiOperation("删除App订阅信息表")
    @PreAuthorize("@ss.hasPermi('orders:info:remove')")
    @Log(title = "App订阅信息表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payAppleJwsRenewalInfoService.deletePayAppleJwsRenewalInfoByIds(ids));
    }
}
