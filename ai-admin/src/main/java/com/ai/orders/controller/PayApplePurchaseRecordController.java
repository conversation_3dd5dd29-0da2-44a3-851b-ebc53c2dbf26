package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.PayApplePurchaseRecord;
import com.ai.orders.service.IPayApplePurchaseRecordService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * apple购买逻辑Controller
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@Api(value = "apple购买逻辑控制器", tags = {"apple购买逻辑管理"})
@RequestMapping("/orders/purchaseRecord")
public class PayApplePurchaseRecordController extends BaseController {
    @Autowired
    private IPayApplePurchaseRecordService payApplePurchaseRecordService;

    /**
     * 查询apple购买逻辑列表
     */
    @PreAuthorize("@ss.hasPermi('orders:purchaseRecord:list')")
    @ApiOperation("查询apple购买逻辑列表")
    @GetMapping("/list")
    public TableDataInfo list(PayApplePurchaseRecord payApplePurchaseRecord) {
        startPage();
        List<PayApplePurchaseRecord> list = payApplePurchaseRecordService.selectPayApplePurchaseRecordList(payApplePurchaseRecord);
        return getDataTable(list);
    }

    /**
     * 导出apple购买逻辑列表
     */
    @ApiOperation("导出apple购买逻辑列表")
    @PreAuthorize("@ss.hasPermi('orders:purchaseRecord:export')")
    @Log(title = "apple购买逻辑", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayApplePurchaseRecord payApplePurchaseRecord) {
        List<PayApplePurchaseRecord> list = payApplePurchaseRecordService.selectPayApplePurchaseRecordList(payApplePurchaseRecord);
        ExcelUtil<PayApplePurchaseRecord> util = new ExcelUtil<PayApplePurchaseRecord>(PayApplePurchaseRecord.class);
        util.exportExcel(response, list, "apple购买逻辑数据");
    }

    /**
     * 获取apple购买逻辑详细信息
     */
    @ApiOperation("获取apple购买逻辑详细信息")
    @PreAuthorize("@ss.hasPermi('orders:purchaseRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payApplePurchaseRecordService.selectPayApplePurchaseRecordById(id));
    }

    /**
     * 新增apple购买逻辑
     */
    @ApiOperation("新增apple购买逻辑")
    @PreAuthorize("@ss.hasPermi('orders:purchaseRecord:add')")
    @Log(title = "apple购买逻辑", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayApplePurchaseRecord payApplePurchaseRecord) {
        return toAjax(payApplePurchaseRecordService.insertPayApplePurchaseRecord(payApplePurchaseRecord));
    }

    /**
     * 修改apple购买逻辑
     */
    @ApiOperation("修改apple购买逻辑")
    @PreAuthorize("@ss.hasPermi('orders:purchaseRecord:edit')")
    @Log(title = "apple购买逻辑", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayApplePurchaseRecord payApplePurchaseRecord) {
        return toAjax(payApplePurchaseRecordService.updatePayApplePurchaseRecord(payApplePurchaseRecord));
    }

    /**
     * 删除apple购买逻辑
     */
    @ApiOperation("删除apple购买逻辑")
    @PreAuthorize("@ss.hasPermi('orders:purchaseRecord:remove')")
    @Log(title = "apple购买逻辑", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payApplePurchaseRecordService.deletePayApplePurchaseRecordByIds(ids));
    }
}
