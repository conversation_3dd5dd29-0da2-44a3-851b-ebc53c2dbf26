package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.PayAppleUpgradeLog;
import com.ai.orders.service.IPayAppleUpgradeLogService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * apple restore logController
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@Api(value = "apple restore log控制器", tags = {"apple restore log管理"})
@RequestMapping("/orders/upgradeLog")
public class PayAppleUpgradeLogController extends BaseController {
    @Autowired
    private IPayAppleUpgradeLogService payAppleUpgradeLogService;

    /**
     * 查询apple restore log列表
     */
    @PreAuthorize("@ss.hasPermi('orders:upgradeLog:list')")
    @ApiOperation("查询apple restore log列表")
    @GetMapping("/list")
    public TableDataInfo list(PayAppleUpgradeLog payAppleUpgradeLog) {
        startPage();
        List<PayAppleUpgradeLog> list = payAppleUpgradeLogService.selectPayAppleUpgradeLogList(payAppleUpgradeLog);
        return getDataTable(list);
    }

    /**
     * 导出apple restore log列表
     */
    @ApiOperation("导出apple restore log列表")
    @PreAuthorize("@ss.hasPermi('orders:upgradeLog:export')")
    @Log(title = "apple restore log", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayAppleUpgradeLog payAppleUpgradeLog) {
        List<PayAppleUpgradeLog> list = payAppleUpgradeLogService.selectPayAppleUpgradeLogList(payAppleUpgradeLog);
        ExcelUtil<PayAppleUpgradeLog> util = new ExcelUtil<PayAppleUpgradeLog>(PayAppleUpgradeLog.class);
        util.exportExcel(response, list, "apple restore log数据");
    }

    /**
     * 获取apple restore log详细信息
     */
    @ApiOperation("获取apple restore log详细信息")
    @PreAuthorize("@ss.hasPermi('orders:upgradeLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payAppleUpgradeLogService.selectPayAppleUpgradeLogById(id));
    }

    /**
     * 新增apple restore log
     */
    @ApiOperation("新增apple restore log")
    @PreAuthorize("@ss.hasPermi('orders:upgradeLog:add')")
    @Log(title = "apple restore log", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayAppleUpgradeLog payAppleUpgradeLog) {
        return toAjax(payAppleUpgradeLogService.insertPayAppleUpgradeLog(payAppleUpgradeLog));
    }

    /**
     * 修改apple restore log
     */
    @ApiOperation("修改apple restore log")
    @PreAuthorize("@ss.hasPermi('orders:upgradeLog:edit')")
    @Log(title = "apple restore log", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayAppleUpgradeLog payAppleUpgradeLog) {
        return toAjax(payAppleUpgradeLogService.updatePayAppleUpgradeLog(payAppleUpgradeLog));
    }

    /**
     * 删除apple restore log
     */
    @ApiOperation("删除apple restore log")
    @PreAuthorize("@ss.hasPermi('orders:upgradeLog:remove')")
    @Log(title = "apple restore log", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payAppleUpgradeLogService.deletePayAppleUpgradeLogByIds(ids));
    }
}
