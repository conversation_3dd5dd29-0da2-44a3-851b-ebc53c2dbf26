package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.SysLumenAdminOperateLog;
import com.ai.orders.service.ISysLumenAdminOperateLogService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * Lumen管理操作日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@Api(value = "Lumen管理操作日志控制器", tags = {"Lumen管理操作日志管理"})
@RequestMapping("/orders/log")
public class SysLumenAdminOperateLogController extends BaseController {
    @Autowired
    private ISysLumenAdminOperateLogService sysLumenAdminOperateLogService;

    /**
     * 查询Lumen管理操作日志列表
     */
    @PreAuthorize("@ss.hasPermi('orders:log:list')")
    @ApiOperation("查询Lumen管理操作日志列表")
    @GetMapping("/list")
    public TableDataInfo list(SysLumenAdminOperateLog sysLumenAdminOperateLog) {
        startPage();
        List<SysLumenAdminOperateLog> list = sysLumenAdminOperateLogService.selectSysLumenAdminOperateLogList(sysLumenAdminOperateLog);
        return getDataTable(list);
    }

    /**
     * 导出Lumen管理操作日志列表
     */
    @ApiOperation("导出Lumen管理操作日志列表")
    @PreAuthorize("@ss.hasPermi('orders:log:export')")
    @Log(title = "Lumen管理操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLumenAdminOperateLog sysLumenAdminOperateLog) {
        List<SysLumenAdminOperateLog> list = sysLumenAdminOperateLogService.selectSysLumenAdminOperateLogList(sysLumenAdminOperateLog);
        ExcelUtil<SysLumenAdminOperateLog> util = new ExcelUtil<SysLumenAdminOperateLog>(SysLumenAdminOperateLog.class);
        util.exportExcel(response, list, "Lumen管理操作日志数据");
    }

    /**
     * 获取Lumen管理操作日志详细信息
     */
    @ApiOperation("获取Lumen管理操作日志详细信息")
    @PreAuthorize("@ss.hasPermi('orders:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysLumenAdminOperateLogService.selectSysLumenAdminOperateLogById(id));
    }

    /**
     * 新增Lumen管理操作日志
     */
    @ApiOperation("新增Lumen管理操作日志")
    @PreAuthorize("@ss.hasPermi('orders:log:add')")
    @Log(title = "Lumen管理操作日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysLumenAdminOperateLog sysLumenAdminOperateLog) {
        return toAjax(sysLumenAdminOperateLogService.insertSysLumenAdminOperateLog(sysLumenAdminOperateLog));
    }

    /**
     * 修改Lumen管理操作日志
     */
    @ApiOperation("修改Lumen管理操作日志")
    @PreAuthorize("@ss.hasPermi('orders:log:edit')")
    @Log(title = "Lumen管理操作日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysLumenAdminOperateLog sysLumenAdminOperateLog) {
        return toAjax(sysLumenAdminOperateLogService.updateSysLumenAdminOperateLog(sysLumenAdminOperateLog));
    }

    /**
     * 删除Lumen管理操作日志
     */
    @ApiOperation("删除Lumen管理操作日志")
    @PreAuthorize("@ss.hasPermi('orders:log:remove')")
    @Log(title = "Lumen管理操作日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysLumenAdminOperateLogService.deleteSysLumenAdminOperateLogByIds(ids));
    }
}
