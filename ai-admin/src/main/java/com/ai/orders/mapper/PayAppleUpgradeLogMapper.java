package com.ai.orders.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.PayAppleUpgradeLog;

/**
 * apple restore logMapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface PayAppleUpgradeLogMapper extends BaseMapper<PayAppleUpgradeLog> {
    /**
     * 查询apple restore log
     * 
     * @param id apple restore log主键
     * @return apple restore log
     */
    public PayAppleUpgradeLog selectPayAppleUpgradeLogById(Long id);

    /**
     * 查询apple restore log列表
     * 
     * @param payAppleUpgradeLog apple restore log
     * @return apple restore log集合
     */
    public List<PayAppleUpgradeLog> selectPayAppleUpgradeLogList(PayAppleUpgradeLog payAppleUpgradeLog);

    /**
     * 新增apple restore log
     * 
     * @param payAppleUpgradeLog apple restore log
     * @return 结果
     */
    public int insertPayAppleUpgradeLog(PayAppleUpgradeLog payAppleUpgradeLog);

    /**
     * 修改apple restore log
     * 
     * @param payAppleUpgradeLog apple restore log
     * @return 结果
     */
    public int updatePayAppleUpgradeLog(PayAppleUpgradeLog payAppleUpgradeLog);

    /**
     * 删除apple restore log
     * 
     * @param id apple restore log主键
     * @return 结果
     */
    public int deletePayAppleUpgradeLogById(Long id);

    /**
     * 批量删除apple restore log
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePayAppleUpgradeLogByIds(Long[] ids);
}
