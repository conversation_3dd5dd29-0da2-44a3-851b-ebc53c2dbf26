package com.ai.orders.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.SysLumenAdminOperateLog;

/**
 * Lumen管理操作日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface SysLumenAdminOperateLogMapper extends BaseMapper<SysLumenAdminOperateLog> {
    /**
     * 查询Lumen管理操作日志
     *
     * @param id Lumen管理操作日志主键
     * @return Lumen管理操作日志
     */
    public SysLumenAdminOperateLog selectSysLumenAdminOperateLogById(Long id);

    /**
     * 查询Lumen管理操作日志列表
     *
     * @param sysLumenAdminOperateLog Lumen管理操作日志
     * @return Lumen管理操作日志集合
     */
    public List<SysLumenAdminOperateLog> selectSysLumenAdminOperateLogList(SysLumenAdminOperateLog sysLumenAdminOperateLog);

    /**
     * 新增Lumen管理操作日志
     *
     * @param sysLumenAdminOperateLog Lumen管理操作日志
     * @return 结果
     */
    public int insertSysLumenAdminOperateLog(SysLumenAdminOperateLog sysLumenAdminOperateLog);

    /**
     * 修改Lumen管理操作日志
     *
     * @param sysLumenAdminOperateLog Lumen管理操作日志
     * @return 结果
     */
    public int updateSysLumenAdminOperateLog(SysLumenAdminOperateLog sysLumenAdminOperateLog);

    /**
     * 删除Lumen管理操作日志
     *
     * @param id Lumen管理操作日志主键
     * @return 结果
     */
    public int deleteSysLumenAdminOperateLogById(Long id);

    /**
     * 批量删除Lumen管理操作日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysLumenAdminOperateLogByIds(Long[] ids);
}
