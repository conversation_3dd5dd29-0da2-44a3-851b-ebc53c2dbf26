package com.ai.orders.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.StripeFailedCancelSubscriptionRecords;

/**
 * 升级订阅时取消旧订阅失败记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface StripeFailedCancelSubscriptionRecordsMapper extends BaseMapper<StripeFailedCancelSubscriptionRecords> {
    /**
     * 查询升级订阅时取消旧订阅失败记录
     * 
     * @param id 升级订阅时取消旧订阅失败记录主键
     * @return 升级订阅时取消旧订阅失败记录
     */
    public StripeFailedCancelSubscriptionRecords selectStripeFailedCancelSubscriptionRecordsById(Long id);

    /**
     * 查询升级订阅时取消旧订阅失败记录列表
     * 
     * @param stripeFailedCancelSubscriptionRecords 升级订阅时取消旧订阅失败记录
     * @return 升级订阅时取消旧订阅失败记录集合
     */
    public List<StripeFailedCancelSubscriptionRecords> selectStripeFailedCancelSubscriptionRecordsList(StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords);

    /**
     * 新增升级订阅时取消旧订阅失败记录
     * 
     * @param stripeFailedCancelSubscriptionRecords 升级订阅时取消旧订阅失败记录
     * @return 结果
     */
    public int insertStripeFailedCancelSubscriptionRecords(StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords);

    /**
     * 修改升级订阅时取消旧订阅失败记录
     * 
     * @param stripeFailedCancelSubscriptionRecords 升级订阅时取消旧订阅失败记录
     * @return 结果
     */
    public int updateStripeFailedCancelSubscriptionRecords(StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords);

    /**
     * 删除升级订阅时取消旧订阅失败记录
     * 
     * @param id 升级订阅时取消旧订阅失败记录主键
     * @return 结果
     */
    public int deleteStripeFailedCancelSubscriptionRecordsById(Long id);

    /**
     * 批量删除升级订阅时取消旧订阅失败记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStripeFailedCancelSubscriptionRecordsByIds(Long[] ids);
}
