package com.ai.orders.mapper;

import java.util.List;

import com.ai.orders.domain.vo.OrderTradeRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.PayLogicPurchaseRecord;

/**
 * lummen购买记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface PayLogicPurchaseRecordMapper extends BaseMapper<PayLogicPurchaseRecord> {
    /**
     * 查询lummen购买记录
     * 
     * @param id lummen购买记录主键
     * @return lummen购买记录
     */
    public PayLogicPurchaseRecord selectPayLogicPurchaseRecordById(Long id);

    /**
     * 查询lummen购买记录列表
     * 
     * @param payLogicPurchaseRecord lummen购买记录
     * @return lummen购买记录集合
     */
    public List<PayLogicPurchaseRecord> selectPayLogicPurchaseRecordList(PayLogicPurchaseRecord payLogicPurchaseRecord);

    /**
     * 新增lummen购买记录
     * 
     * @param payLogicPurchaseRecord lummen购买记录
     * @return 结果
     */
    public int insertPayLogicPurchaseRecord(PayLogicPurchaseRecord payLogicPurchaseRecord);

    /**
     * 修改lummen购买记录
     * 
     * @param payLogicPurchaseRecord lummen购买记录
     * @return 结果
     */
    public int updatePayLogicPurchaseRecord(PayLogicPurchaseRecord payLogicPurchaseRecord);

    /**
     * 删除lummen购买记录
     * 
     * @param id lummen购买记录主键
     * @return 结果
     */
    public int deletePayLogicPurchaseRecordById(Long id);

    /**
     * 批量删除lummen购买记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePayLogicPurchaseRecordByIds(Long[] ids);

    List<OrderTradeRecordVo> selectOrderTradeRecordList(OrderTradeRecordVo orderTradeRecordVo);

}
