package com.ai.orders.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.StripeSubscriptionLog;

/**
 * stripe 订阅日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface StripeSubscriptionLogMapper extends BaseMapper<StripeSubscriptionLog> {
    /**
     * 查询stripe 订阅日志
     * 
     * @param id stripe 订阅日志主键
     * @return stripe 订阅日志
     */
    public StripeSubscriptionLog selectStripeSubscriptionLogById(Long id);

    /**
     * 查询stripe 订阅日志列表
     * 
     * @param stripeSubscriptionLog stripe 订阅日志
     * @return stripe 订阅日志集合
     */
    public List<StripeSubscriptionLog> selectStripeSubscriptionLogList(StripeSubscriptionLog stripeSubscriptionLog);

    /**
     * 新增stripe 订阅日志
     * 
     * @param stripeSubscriptionLog stripe 订阅日志
     * @return 结果
     */
    public int insertStripeSubscriptionLog(StripeSubscriptionLog stripeSubscriptionLog);

    /**
     * 修改stripe 订阅日志
     * 
     * @param stripeSubscriptionLog stripe 订阅日志
     * @return 结果
     */
    public int updateStripeSubscriptionLog(StripeSubscriptionLog stripeSubscriptionLog);

    /**
     * 删除stripe 订阅日志
     * 
     * @param id stripe 订阅日志主键
     * @return 结果
     */
    public int deleteStripeSubscriptionLogById(Long id);

    /**
     * 批量删除stripe 订阅日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStripeSubscriptionLogByIds(Long[] ids);
}
