package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * stripe 订阅日志对象 stripe_subscription_log
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "stripe_subscription_log", description = "stripe 订阅日志")
@TableName("stripe_subscription_log")
public class StripeSubscriptionLog extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** 订阅ID */
    @ApiModelProperty("订阅ID")
    @Excel(name = "订阅ID")
    private String subscriptionId;

    /** 订阅物品ID */
    @ApiModelProperty("订阅物品ID")
    @Excel(name = "订阅物品ID")
    private String subscriptionItemId;

    /** 预定阅ID */
    @ApiModelProperty("预定阅ID")
    @Excel(name = "预定阅ID")
    private String subscriptionScheduleId;

    /** customer id */
    @ApiModelProperty("customer id")
    @Excel(name = "customer id")
    private String customerId;

    /** stripe price_id */
    @ApiModelProperty("stripe price_id")
    @Excel(name = "stripe price_id")
    private String priceId;

    /** 价格类型 */
    @ApiModelProperty("价格类型")
    @Excel(name = "价格类型")
    private String priceType;

    /** stripe product id */
    @ApiModelProperty("stripe product id")
    @Excel(name = "stripe product id")
    private String productId;

    /** stripe product name */
    @ApiModelProperty("stripe product name")
    @Excel(name = "stripe product name")
    private String proudctName;

    /** 订阅状态 */
    @ApiModelProperty("订阅状态")
    @Excel(name = "订阅状态")
    private String subStatus;

    /** 价钱间隔 */
    @ApiModelProperty("价钱间隔")
    @Excel(name = "价钱间隔")
    private String subInterval;

    /** 订阅开始时间 */
    @ApiModelProperty("订阅开始时间")
    @Excel(name = "订阅开始时间")
    private Long startDate;

    /** 订阅每周期结束时间 */
    @ApiModelProperty("订阅每周期结束时间")
    @Excel(name = "订阅每周期结束时间")
    private Long currentPeriodEnd;

    /** 订阅每周期开始时间 */
    @ApiModelProperty("订阅每周期开始时间")
    @Excel(name = "订阅每周期开始时间")
    private Long currentPeriodStart;

    /** 逻辑订阅每周期结束时间 */
    @ApiModelProperty("逻辑订阅每周期结束时间")
    @Excel(name = "逻辑订阅每周期结束时间")
    private Long logicPeriodEnd;

    /** 逻辑订阅每周期开始时间 */
    @ApiModelProperty("逻辑订阅每周期开始时间")
    @Excel(name = "逻辑订阅每周期开始时间")
    private Long logicPeriodStart;

    /** 取消时间 */
    @ApiModelProperty("取消时间")
    @Excel(name = "取消时间")
    private Long cancelledAt;

    /** 取消原因 */
    @ApiModelProperty("取消原因")
    @Excel(name = "取消原因")
    private String cancelReason;

}
