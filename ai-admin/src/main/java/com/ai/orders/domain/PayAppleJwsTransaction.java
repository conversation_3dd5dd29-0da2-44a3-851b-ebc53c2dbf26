package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 苹果支付JWS交易记录对象 pay_apple_jws_transaction
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "pay_apple_jws_transaction", description = "苹果支付JWS交易记录")
@TableName("pay_apple_jws_transaction")
public class PayAppleJwsTransaction extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @ApiModelProperty("用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户登录名 */
    @ApiModelProperty("用户登录名")
    @Excel(name = "用户登录名")
    private String loginName;

    /** 原始购买的交易标识符 */
    @ApiModelProperty("原始购买的交易标识符")
    @Excel(name = "原始购买的交易标识符")
    private String originalTransactionId;

    /** 交易的唯一标识符 */
    @ApiModelProperty("交易的唯一标识符")
    @Excel(name = "交易的唯一标识符")
    private String transactionId;

    /** 购买交易的原因 */
    @ApiModelProperty("购买交易的原因")
    @Excel(name = "购买交易的原因")
    private String transactionReason;

    /** 用于将交易与您自己的服务上的客户关联起来的 UUID */
    @ApiModelProperty("用于将交易与您自己的服务上的客户关联起来的 UUID")
    @Excel(name = "用于将交易与您自己的服务上的客户关联起来的 UUID")
    private String appAccountToken;

    /** 应用程序的软件包标识符 */
    @ApiModelProperty("应用程序的软件包标识符")
    @Excel(name = "应用程序的软件包标识符")
    private String bundleId;

    /** 三字母 ISO 4217 货币代码 */
    @ApiModelProperty("三字母 ISO 4217 货币代码")
    @Excel(name = "三字母 ISO 4217 货币代码")
    private String currency;

    /** 服务器环境，沙盒或生产 */
    @ApiModelProperty("服务器环境，沙盒或生产")
    @Excel(name = "服务器环境，沙盒或生产")
    private String environment;

    /** 订阅到期或续订的 UNIX 时间 */
    @ApiModelProperty("订阅到期或续订的 UNIX 时间")
    @Excel(name = "订阅到期或续订的 UNIX 时间")
    private Long expiresDate;

    /** 描述交易是否由客户购买或通过家人共享提供 */
    @ApiModelProperty("描述交易是否由客户购买或通过家人共享提供")
    @Excel(name = "描述交易是否由客户购买或通过家人共享提供")
    private String inAppOwnershipType;

    /** 指示客户是否升级到另一个订阅 */
    @ApiModelProperty("指示客户是否升级到另一个订阅")
    @Excel(name = "指示客户是否升级到另一个订阅")
    private Boolean isUpgraded;

    /** 订阅优惠的付款方式 */
    @ApiModelProperty("订阅优惠的付款方式")
    @Excel(name = "订阅优惠的付款方式")
    private String offerDiscountType;

    /** 优惠代码或促销优惠标识符 */
    @ApiModelProperty("优惠代码或促销优惠标识符")
    @Excel(name = "优惠代码或促销优惠标识符")
    private String offerIdentifier;

    /** 促销优惠类型 */
    @ApiModelProperty("促销优惠类型")
    @Excel(name = "促销优惠类型")
    private Integer offerType;

    /** 原始交易的购买日期 */
    @ApiModelProperty("原始交易的购买日期")
    @Excel(name = "原始交易的购买日期")
    private Long originalPurchaseDate;

    /** 原始交易的购买日期（毫秒） */
    @ApiModelProperty("原始交易的购买日期")
    @Excel(name = "原始交易的购买日期", readConverterExp = "毫=秒")
    private Long originalPurchaseDateMs;

    /** 价格乘以 1000 的整数值 */
    @ApiModelProperty("价格乘以 1000 的整数值")
    @Excel(name = "价格乘以 1000 的整数值")
    private Long price;

    /** 应用内购买的产品标识符 */
    @ApiModelProperty("应用内购买的产品标识符")
    @Excel(name = "应用内购买的产品标识符")
    private String productId;

    /** 购买或续订费用的 UNIX 时间 */
    @ApiModelProperty("购买或续订费用的 UNIX 时间")
    @Excel(name = "购买或续订费用的 UNIX 时间")
    private Long purchaseDate;

    /** 购买或续订费用的 UNIX 时间（毫秒） */
    @ApiModelProperty("购买或续订费用的 UNIX 时间")
    @Excel(name = "购买或续订费用的 UNIX 时间", readConverterExp = "毫=秒")
    private Long purchaseDateMs;

    /** 用户购买的消耗品数量 */
    @ApiModelProperty("用户购买的消耗品数量")
    @Excel(name = "用户购买的消耗品数量")
    private Integer quantity;

    /** 退还交易或撤销的时间 */
    @ApiModelProperty("退还交易或撤销的时间")
    @Excel(name = "退还交易或撤销的时间")
    private Long revocationDate;

    /** 退还交易或撤销的时间（毫秒） */
    @ApiModelProperty("退还交易或撤销的时间")
    @Excel(name = "退还交易或撤销的时间", readConverterExp = "毫=秒")
    private Long revocationDateMs;

    /** 退款或撤销的原因 */
    @ApiModelProperty("退款或撤销的原因")
    @Excel(name = "退款或撤销的原因")
    private Integer revocationReason;

    /** App Store 签署 JWS 数据的时间（毫秒） */
    @ApiModelProperty("App Store 签署 JWS 数据的时间")
    @Excel(name = "App Store 签署 JWS 数据的时间", readConverterExp = "毫=秒")
    private Long signedDateMs;

    /** 签名日期 */
    @ApiModelProperty("签名日期")
    @Excel(name = "签名日期")
    private Long signedDate;

    /** 购买相关的 App Store 店面的三位字母代码 */
    @ApiModelProperty("购买相关的 App Store 店面的三位字母代码")
    @Excel(name = "购买相关的 App Store 店面的三位字母代码")
    private String storefront;

    /** App Store 店面的唯一标识符 */
    @ApiModelProperty("App Store 店面的唯一标识符")
    @Excel(name = "App Store 店面的唯一标识符")
    private String storefrontId;

    /** 订阅组的标识符 */
    @ApiModelProperty("订阅组的标识符")
    @Excel(name = "订阅组的标识符")
    private String subscriptionGroupIdentifier;

    /** 应用内购买的类型 */
    @ApiModelProperty("应用内购买的类型")
    @Excel(name = "应用内购买的类型")
    private String type;

    /** 跨设备订阅购买事件的唯一标识符 */
    @ApiModelProperty("跨设备订阅购买事件的唯一标识符")
    @Excel(name = "跨设备订阅购买事件的唯一标识符")
    private String webOrderLineItemId;

}
