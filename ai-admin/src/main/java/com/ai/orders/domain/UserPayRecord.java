package com.ai.orders.domain;

import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户支付记录实体类
 */
@Data
@TableName("user_pay_record")
@ApiModel(value = "user_pay_record", description = "用户支付记录")
public class UserPayRecord extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @ApiModelProperty("主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 登录名称
     */
    @ApiModelProperty("登录名称")
    private String loginName;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google
     */
    @ApiModelProperty("平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google")
    private Integer platform;

    /**
     * 来源
     */
    @ApiModelProperty("来源")
    private String source;

    /**
     * 详情描述，例如：Text to image
     */
    @ApiModelProperty("详情描述，例如：Text to image")
    private String detail;

    /**
     * 优惠码
     */
    @ApiModelProperty("优惠码")
    private String couponCode;

    /**
     * 折扣百分比
     */
    @ApiModelProperty("折扣百分比")
    private Integer percentOff;

    /**
     * 用户购买价格
     */
    @ApiModelProperty("用户购买价格")
    private Long amount;


    private Long amountExcludingTax;

    /**
     * 折扣总价
     */
    @ApiModelProperty("折扣总价")
    private Long afterDiscountAmount;

    private String currency;

    /**
     * 外部交易ID（用于幂等校验）
     * Stripe: payment_intent_id 或 invoice_id
     * PayPal: order_id 或 subscription_id
     * Apple: original_transaction_id
     * Google: purchase_token
     * Backend: 自定义业务ID
     */
    @ApiModelProperty("外部交易ID（用于幂等校验）")
    private String externalTransactionId;

    /**
     * 外部订单号（辅助幂等校验）
     */
    @ApiModelProperty("外部订单号（辅助幂等校验）")
    private String externalOrderId;

    /**
     * 支付状态
     */
    @ApiModelProperty("支付状态")
    private String paymentStatus;

    public void setCurrency(String currency) {
        // 转大写
        if (currency != null) {
            currency = currency.toUpperCase();
        }
        this.currency = currency;
    }
}
