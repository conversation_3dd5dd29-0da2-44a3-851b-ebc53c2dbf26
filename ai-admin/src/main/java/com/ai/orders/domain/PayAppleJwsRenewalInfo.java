package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * App订阅信息表对象 pay_apple_jws_renewal_info
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "pay_apple_jws_renewal_info", description = "App订阅信息表")
@TableName("pay_apple_jws_renewal_info")
public class PayAppleJwsRenewalInfo extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 ID */
    private Long id;

    /** 每条通知分配一个唯一标识符。使用此值来识别和忽略重复的通知。 */
    @ApiModelProperty("每条通知分配一个唯一标识符。使用此值来识别和忽略重复的通知。")
    @Excel(name = "每条通知分配一个唯一标识符。使用此值来识别和忽略重复的通知。")
    private String notificationUuid;

    /** 下一个计费期续订的产品标识符 */
    @ApiModelProperty("下一个计费期续订的产品标识符")
    @Excel(name = "下一个计费期续订的产品标识符")
    private String autoRenewProductId;

    /** 自动续订订阅的续订状态 */
    @ApiModelProperty("自动续订订阅的续订状态")
    @Excel(name = "自动续订订阅的续订状态")
    private Integer autoRenewStatus;

    /** 订阅的货币代码 */
    @ApiModelProperty("订阅的货币代码")
    @Excel(name = "订阅的货币代码")
    private String currency;

    /** 客户有资格获得的挽回优惠 ID 列表[1,2] */
    @ApiModelProperty("客户有资格获得的挽回优惠 ID 列表[1,2]")
    @Excel(name = "客户有资格获得的挽回优惠 ID 列表[1,2]")
    private String eligibleWinBackOfferIds;

    /** 服务器环境，沙盒或生产 */
    @ApiModelProperty("服务器环境，沙盒或生产")
    @Excel(name = "服务器环境，沙盒或生产")
    private String environment;

    /** 订阅过期的原因 */
    @ApiModelProperty("订阅过期的原因")
    @Excel(name = "订阅过期的原因")
    private Integer expirationIntent;

    /** 订阅续订的计费宽限期到期时间 */
    @ApiModelProperty("订阅续订的计费宽限期到期时间")
    @Excel(name = "订阅续订的计费宽限期到期时间")
    private Long gracePeriodExpiresDate;

    /** 是否处于账单重试期 */
    @ApiModelProperty("是否处于账单重试期")
    @Excel(name = "是否处于账单重试期")
    private Boolean isInBillingRetryPeriod;

    /** 优惠折扣的付款方式 */
    @ApiModelProperty("优惠折扣的付款方式")
    @Excel(name = "优惠折扣的付款方式")
    private String offerDiscountType;

    /** 优惠代码或促销标识符 */
    @ApiModelProperty("优惠代码或促销标识符")
    @Excel(name = "优惠代码或促销标识符")
    private String offerIdentifier;

    /** 订阅优惠的类型 */
    @ApiModelProperty("订阅优惠的类型")
    @Excel(name = "订阅优惠的类型")
    private Integer offerType;

    /** 原始购买的交易标识符 */
    @ApiModelProperty("原始购买的交易标识符")
    @Excel(name = "原始购买的交易标识符")
    private String originalTransactionId;

    /** 自动续订订阅是否会涨价 */
    @ApiModelProperty("自动续订订阅是否会涨价")
    @Excel(name = "自动续订订阅是否会涨价")
    private Integer priceIncreaseStatus;

    /** 应用内购买的产品标识符 */
    @ApiModelProperty("应用内购买的产品标识符")
    @Excel(name = "应用内购买的产品标识符")
    private String productId;

    /** 一系列订阅购买中最早的开始日期 */
    @ApiModelProperty("一系列订阅购买中最早的开始日期")
    @Excel(name = "一系列订阅购买中最早的开始日期")
    private Long recentSubscriptionStartDate;

    /** 最新的自动续订订阅到期日期 */
    @ApiModelProperty("最新的自动续订订阅到期日期")
    @Excel(name = "最新的自动续订订阅到期日期")
    private Long renewalDate;

    /** 下一个计费期续订价格 */
    @ApiModelProperty("下一个计费期续订价格")
    @Excel(name = "下一个计费期续订价格")
    private Long renewalPrice;

    /** App Store 签署 JWS 数据的时间戳 */
    @ApiModelProperty("App Store 签署 JWS 数据的时间戳")
    @Excel(name = "App Store 签署 JWS 数据的时间戳")
    private Long signedDate;

}
