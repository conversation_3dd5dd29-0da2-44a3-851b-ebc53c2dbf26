package com.ai.orders.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "subscription_current", description = "当前订阅")
public class SubscriptionCurrentDto {

    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    private String loginName;

    /** 平台唯一标识（如 Stripe 的 subscription_id 或 Apple 的 receipt_id） */
    @ApiModelProperty("平台唯一标识")
    private String subscriptionId;

    /** 订阅每周期开始时间 */
    @ApiModelProperty("订阅每周期开始时间")
    private Long currentPeriodStart;

    /** 订阅每周期结束时间 */
    @ApiModelProperty("订阅每周期结束时间")
    private Long currentPeriodEnd;

    /** 会员类型 */
    @ApiModelProperty("会员类型")
    private String planLevel;

    /** 价格间隔 */
    @ApiModelProperty("价格间隔")
    private String priceInterval;

    /** 会员生效时间 */
    @ApiModelProperty("会员生效时间")
    private Long vipBeginTime;

    /** 会员过期时间 */
    @ApiModelProperty("会员过期时间")
    private Long vipEndTime;

    /** 订阅平台 */
    @ApiModelProperty("订阅平台")
    private String vipPlatform;

    /** 自动续订状态（1 表示启用，0 表示关闭） */
    @ApiModelProperty("自动续订状态")
    private Integer autoRenewStatus;

    /** 原始交易id */
    @ApiModelProperty("原始交易id")
    private String originalTransactionId;

    /** 交易id */
    @ApiModelProperty("交易id")
    private String  transactionId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vipBeginTimeDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vipEndTimeDate;

    @ApiModelProperty("会员名称")
    private String subscriptionName;
}
