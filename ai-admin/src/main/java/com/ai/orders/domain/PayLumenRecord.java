package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

import java.time.LocalDateTime;

/**
 * lumen实时记录对象 pay_lumen_record
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "pay_lumen_record", description = "lumen实时记录")
@TableName("pay_lumen_record")
public class PayLumenRecord extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** stripe customer */
    @ApiModelProperty("stripe customer")
    @Excel(name = "stripe customer")
    private String customerId;

    /** 支付记录ID */
    @ApiModelProperty("支付记录ID")
    @Excel(name = "支付记录ID")
    private  Long  payLogicPurchaseRecordId;

    /** lumen 到期时间 */
    @ApiModelProperty("lumen 到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "lumen 到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long currentPeriodEnd;

    /** lumen 获得时间 */
    @ApiModelProperty("lumen 获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "lumen 获得时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long currentPeriodStart;

    /** 1: recharge, 2: vip，3:gift */
    @ApiModelProperty("1: recharge, 2: vip，3:gift")
    @Excel(name = "1: recharge, 2: vip，3:gift")
    private Integer type;

    /** 逻辑订阅每周期结束时间 */
    @ApiModelProperty("逻辑订阅每周期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "逻辑订阅每周期结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long logicPeriodEnd;

    /** 逻辑订阅每周期开始时间 */
    @ApiModelProperty("逻辑订阅每周期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "逻辑订阅每周期开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long logicPeriodStart;

    /** lumen原有数量 */
    @ApiModelProperty("lumen原有数量")
    @Excel(name = "lumen原有数量")
    private Long lumenQty;

    /** lumen剩余数量 */
    @ApiModelProperty("lumen剩余数量")
    @Excel(name = "lumen剩余数量")
    private Long lumenLeftQty;

    /** 是否为混合 */
    @ApiModelProperty("是否为混合")
    @Excel(name = "是否为混合")
    private Integer mixed;

    /** 是否无效，存在cancel的情况 */
    @ApiModelProperty("是否无效，存在cancel的情况")
    @Excel(name = "是否无效，存在cancel的情况")
    private Integer invalid;

    /** 原始交易id */
    @ApiModelProperty("原始交易id")
    @Excel(name = "原始交易id")
    private String originalTransactionId;

    /** 交易id */
    @ApiModelProperty("交易id")
    @Excel(name = "交易id")
    private String  transactionId;

    @ApiModelProperty("订阅平台")
    @Excel(name = "订阅平台")
    private String vipPlatForm;

    /** lumen 到期时间 */
    @ApiModelProperty("lumen 到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "lumen 到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime  currentPeriodEnDate;

    /** lumen 获得时间 */
    @ApiModelProperty("lumen 获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "lumen 获得时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime  currentPeriodStartDate;


    /** lumen 获得时间 */
    @ApiModelProperty("赠送会员 理由 ")
    @TableField(exist = false)
    private String  detail;


}
