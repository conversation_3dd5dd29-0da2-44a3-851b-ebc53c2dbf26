package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 存储 Stripe 商品和价格信息的对象 stripe_product
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "stripe_product", description = "存储 Stripe 商品和价格信息的")
@TableName("stripe_product")
public class StripeProduct extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** Stripe 生成的产品ID */
    @ApiModelProperty("Stripe 生成的产品ID")
    @Excel(name = "Stripe 生成的产品ID")
    private String stripeProductId;

    /** Stripe 生成的价格ID */
    @ApiModelProperty("Stripe 生成的价格ID")
    @Excel(name = "Stripe 生成的价格ID")
    private String stripePriceId;

    /** standard，pro */
    @ApiModelProperty("standard，pro")
    @Excel(name = "standard，pro")
    private String planLevel;

    /** lumen */
    @ApiModelProperty("lumen")
    @Excel(name = "lumen")
    private Long lumen;

    /** plan, one */
    @ApiModelProperty("plan, one")
    @Excel(name = "plan, one")
    private String productType;

    /** year, month */
    @ApiModelProperty("year, month")
    @Excel(name = "year, month")
    private String priceInterval;

    /** standard month 1--&gt;pro year  4 */
    private Integer vipLevel;

    /** 商品描述信息 */
    private String mark;

}
