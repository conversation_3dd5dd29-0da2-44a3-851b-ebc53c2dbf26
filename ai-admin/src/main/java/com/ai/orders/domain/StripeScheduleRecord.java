package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

import java.time.LocalDateTime;

/**
 * stripe 预定阅记录对象 stripe_schedule_record
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "stripe_schedule_record", description = "stripe 预定阅记录")
@TableName("stripe_schedule_record")
public class StripeScheduleRecord extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** 订阅ID */
    @ApiModelProperty("订阅ID")
    @Excel(name = "订阅ID")
    private String subscriptionId;

    /** 预定阅ID */
    @ApiModelProperty("预定阅ID")
    @Excel(name = "预定阅ID")
    private String subscriptionScheduleId;

    /** customer id */
    @ApiModelProperty("customer id")
    @Excel(name = "customer id")
    private String customerId;

    /** stripe price_id */
    @ApiModelProperty("stripe price_id")
    @Excel(name = "stripe price_id")
    private String priceId;

    /** $column.columnComment */
    @ApiModelProperty("${comment}")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String scheduleStatus;

    /** sub_interval */
    @ApiModelProperty("sub_interval")
    @Excel(name = "sub_interval")
    private String subInterval;

    /** schedule开始时间 */
    @ApiModelProperty("schedule开始时间")
    private Long startDate;

    /** schedule开始时间 */
    @ApiModelProperty("schedule开始时间")
    private Long endDate;

    /** 取消时间 */
    @ApiModelProperty("取消时间")
    private Long cancelledAt;

    /** schedule开始时间 */
    @ApiModelProperty("schedule开始时间")
    @Excel(name = "schedule开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDateTime;

    /** schedule开始时间 */
    @ApiModelProperty("schedule开始时间")
    @Excel(name = "schedule开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDateTime;

    /** 取消时间 */
    @ApiModelProperty("取消时间")
    @Excel(name = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelledAtTime;

}
