package com.ai.orders.domain;

import java.math.BigDecimal;
import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * apple购买逻辑对象 pay_apple_purchase_record
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "pay_apple_purchase_record", description = "apple购买逻辑")
@TableName("pay_apple_purchase_record")
public class PayApplePurchaseRecord extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** 与客户关联起来的 UUID */
    @ApiModelProperty("与客户关联起来的 UUID")
    @Excel(name = "与客户关联起来的 UUID")
    private String appAccountToken;

    /** 原始购买的交易标识符 */
    @ApiModelProperty("原始购买的交易标识符")
    @Excel(name = "原始购买的交易标识符")
    private String originalTransactionId;

    /** 交易的唯一标识符 */
    @ApiModelProperty("交易的唯一标识符")
    @Excel(name = "交易的唯一标识符")
    private String transactionId;

    /** st应用内购买的产品标识符 */
    @ApiModelProperty("st应用内购买的产品标识符")
    @Excel(name = "st应用内购买的产品标识符")
    private String productId;

    /** 购买或续订费用的 UNIX 时间 */
    @ApiModelProperty("购买或续订费用的 UNIX 时间")
    @Excel(name = "购买或续订费用的 UNIX 时间")
    private Long purchaseDate;

    /** 原始购买时间unix */
    @ApiModelProperty("原始购买时间unix")
    @Excel(name = "原始购买时间unix")
    private Long originalPurchaseDate;

    /** 订阅到期或续订的 UNIX 时间 */
    @ApiModelProperty("订阅到期或续订的 UNIX 时间")
    @Excel(name = "订阅到期或续订的 UNIX 时间")
    private Long expiresDate;

    /** 逻辑订阅每周期结束时间 */
    @ApiModelProperty("逻辑订阅每周期结束时间")
    @Excel(name = "逻辑订阅每周期结束时间")
    private Long logicPeriodEnd;

    /** 逻辑订阅每周期开始时间 */
    @ApiModelProperty("逻辑订阅每周期开始时间")
    @Excel(name = "逻辑订阅每周期开始时间")
    private Long logicPeriodStart;

    /** lumen 总数 */
    @ApiModelProperty("lumen 总数")
    @Excel(name = "lumen 总数")
    private Integer lumenQty;

    /** 购买数量 */
    @ApiModelProperty("购买数量")
    @Excel(name = "购买数量")
    private Integer count;

    /** 价格乘以 1000 的整数值 */
    @ApiModelProperty("价格乘以 1000 的整数值")
    @Excel(name = "价格乘以 1000 的整数值")
    private Integer price;

    /** 价格 */
    @ApiModelProperty("价格")
    @Excel(name = "价格")
    private BigDecimal amount;

    /** 是否取消 */
    @ApiModelProperty("是否取消")
    @Excel(name = "是否取消")
    private Boolean cancel;

    /** 退还交易或撤销的时间 */
    @ApiModelProperty("退还交易或撤销的时间")
    @Excel(name = "退还交易或撤销的时间")
    private Long revocationDate;

    /** stripe，ios，android */
    @ApiModelProperty("stripe，ios，android")
    @Excel(name = "stripe，ios，android")
    private String vipPlatForm;

}
