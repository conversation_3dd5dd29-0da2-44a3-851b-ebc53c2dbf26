package com.ai.orders.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("会员Lumen点数")
@Data
public class UserLumensVo {

    @ApiModelProperty("每日免费点数日期(只记录年月日)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dailyLumensTime;

    @ApiModelProperty("每日免费点数")
    private Integer dailyLumens;

    @ApiModelProperty("会员点数")
    private Long vipLumens;

    @ApiModelProperty("充值点数")
    private Long rechargeLumens;

    @ApiModelProperty("剩余每日免费点数")
    private Long leftDailyLumens;

    @ApiModelProperty("剩余会员点数")
    private Long leftVipLumens;

    @ApiModelProperty("剩余充值点数")
    private Long leftRechargeLumens;
}
