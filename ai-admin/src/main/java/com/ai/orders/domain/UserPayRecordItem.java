package com.ai.orders.domain;

import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户支付记录详情实体类
 */
@Data
@TableName("user_pay_record_item")
@ApiModel(value = "user_pay_record_item", description = "用户支付记录详情")
public class UserPayRecordItem extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @ApiModelProperty("主键")
    @TableId(value = "id")
    private Long id;

    /**
     * 登录名称
     */
    @ApiModelProperty("登录名称")
    private String loginName;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 支付记录ID
     */
    @ApiModelProperty("支付记录ID")
    private Long recordId;

    /**
     * 平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google
     */
    @ApiModelProperty("平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google")
    private Integer platform;

    /**
     * 产品类型：plan 计划, one 购买lumen
     */
    @ApiModelProperty(" 产品类型：plan 计划, one 购买lumen")
    private String productType;

    /**
     * 计划等级：standard/pro
     */
    @ApiModelProperty("计划等级：standard/pro")
    private String planLevel;

    /**
     * 价格间隔：month/year
     */
    @ApiModelProperty("价格间隔：month/year")
    private String priceInterval;

    /**
     * 折扣百分比
     */
    @ApiModelProperty("折扣百分比")
    private Integer percentOff;

    /**
     * 单位Lumen数量
     */
    @ApiModelProperty("单位Lumen数量")
    private Integer unitLumen;

    /**
     * 总Lumen数量
     */
    @ApiModelProperty("总Lumen数量")
    private Integer totalLumen;

    @ApiModelProperty("总Lumen数量")
    private Integer giftLumen;

    @ApiModelProperty("总Lumen数量")
    private Long totalAmountExcludingTax;

    /**
     * 单位金额
     */
    @ApiModelProperty("单位金额")
    private Long unitAmount;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private Long totalAmount;

    /**
     * 购买数量
     */
    @ApiModelProperty("购买数量")
    private Integer qty;
}
