package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 会员restore记录对象 pay_apple_restore_log
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "pay_apple_restore_log", description = "会员restore记录")
@TableName("pay_apple_restore_log")
public class PayAppleRestoreLog extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long fromUserId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String fromLoginName;

    /** restore 后的user */
    @ApiModelProperty("restore 后的user")
    @Excel(name = "restore 后的user")
    private Long toUserId;

    /** restore 后的login name */
    @ApiModelProperty("restore 后的login name")
    @Excel(name = "restore 后的login name")
    private String toLoginName;

    /** 源事物ID */
    @ApiModelProperty("源事物ID")
    @Excel(name = "源事物ID")
    private String originalTransactionId;

    /** 触发restore 的事物 */
    @ApiModelProperty("触发restore 的事物")
    @Excel(name = "触发restore 的事物")
    private String triggerTransactionId;

}
