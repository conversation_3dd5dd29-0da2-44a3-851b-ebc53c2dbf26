package com.ai.defined.utils;

import com.ai.common.constant.LogicConstants;
import com.ai.common.utils.ip.IpUtils;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CountryResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.Optional;

@Service
public class GeoIpCountryService {
    @Resource
    private DatabaseReader databaseReaderCountry;

    public Optional<String> getCountry(String ip) {
        try {
            // 内网不查询
            if (IpUtils.internalIp(ip)) {
                return Optional.of(LogicConstants.INNER_IP);
            }
            InetAddress ipAddress = InetAddress.getByName(ip);
            CountryResponse response = databaseReaderCountry.country(ipAddress);
            return Optional.ofNullable(response.getCountry().getName());
        } catch (Exception e) {
            e.printStackTrace();
            return Optional.of(LogicConstants.UNKNOWN_IP);
        }
    }
}
