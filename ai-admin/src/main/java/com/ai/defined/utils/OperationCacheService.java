package com.ai.defined.utils;

import com.ai.admin.domain.vo.ModelNumVo;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.core.redis.RedisCache;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.ErrorCode;
import com.ai.common.enums.ModelType;
import com.ai.common.utils.JsonUtils;
import com.ai.common.utils.StringUtils;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.domain.vo.ErrorCodeVo;
import com.ai.task.domain.vo.DayMaxTaskSizeVo;
import com.ai.task.domain.vo.GuaranteesInstanceVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OperationCacheService {

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    @Resource
    private RedisCache redisCache;

    public List<DayMaxTaskSizeVo> getDayMaxTaskSizeList(int pageNum, int pageSize) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        List<DayMaxTaskSizeVo> dayMaxTaskSizeList = new ArrayList<>();

        // 获取所有匹配的 Redis 键
        Set<String> keys = (Set<String>) redisCachePiclumen.keys(LogicParamsCons.DAY_MAX_TASK_SIZE + "*");

        // 对 Redis 键进行分页
        List<String> keyList = new ArrayList<>(keys);
        int total = keyList.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);

        // 如果分页范围超出数据大小，直接返回空列表
        if (fromIndex >= total) {
            return dayMaxTaskSizeList;
        }

        // 处理分页后的键
        for (String key : keyList.subList(fromIndex, toIndex)) {
            Set<ZSetOperations.TypedTuple<String>> cacheSetSet = redisCachePiclumen.getZSetAllVaule(key);
            dayMaxTaskSizeList.addAll(cacheSetSet.stream().map(
                    cacheSet -> {
                        DayMaxTaskSizeVo vo = new DayMaxTaskSizeVo();
                        vo.setScore(cacheSet.getScore());
                        vo.setKey(key.split(":")[1]);
                        vo.setDate(LocalDate.parse(cacheSet.getValue(), formatter));
                        return vo;
                    }
            ).collect(Collectors.toList()));
        }

        return dayMaxTaskSizeList;
    }

    public TableDataInfo getAllErrorCodes(Integer pageNum, Integer pageSize, String code, String description, String message) {
        List<ErrorCodeVo> errorCodeVoList = new ArrayList<>();

        // 遍历枚举值，按条件筛选
        for (ErrorCode errorCode : ErrorCode.values()) {
            boolean matchesCode = StringUtils.isBlank(code) || errorCode.getCode().contains(code);
            boolean matchesDescription = StringUtils.isBlank(description) || errorCode.getDescription().contains(description);
            boolean matchesMessage = StringUtils.isBlank(message) || errorCode.getMessage().contains(message);

            if (matchesCode && matchesDescription && matchesMessage) {
                ErrorCodeVo errorCodeVo = new ErrorCodeVo();
                errorCodeVo.setCode(errorCode.getCode());
                errorCodeVo.setMessage(errorCode.getMessage());
                errorCodeVo.setDescription(errorCode.getDescription());
                errorCodeVoList.add(errorCodeVo);
            }
        }

        // 计算分页的起始和结束索引
        Integer totalSize = errorCodeVoList.size();
        Integer fromIndex = (pageNum - 1) * pageSize;
        Integer toIndex = Math.min(fromIndex + pageSize, totalSize);

        // 校验起始索引是否有效
        if (fromIndex >= totalSize || fromIndex < 0) {
            return new TableDataInfo(new ArrayList<>(),errorCodeVoList.size()); // 返回空列表
        }
        // 返回分页后的子列表
        List<ErrorCodeVo> errorCodeVos = errorCodeVoList.subList(fromIndex, toIndex);
        return   new TableDataInfo(errorCodeVos,errorCodeVoList.size());
    }


    public  int getEnumCount() {
        return ErrorCode.values().length;
    }

    public TableDataInfo getModelNum(Integer pageNum, Integer pageSize, String modelId, String startDate, String endDate) {
        List<ModelNumVo> modelNumVoList = getAllModelNumLis(modelId,startDate,endDate);
        // 过滤条件
        if (StringUtils.isNotBlank(modelId)) {
            modelNumVoList = modelNumVoList.stream()
                    .filter(modelNum -> modelId.equals(modelNum.getModelId()))
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            try {
                LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                modelNumVoList = modelNumVoList.stream()
                        .filter(modelNum -> {
                            LocalDate date = modelNum.getDate();
                            return date != null && !date.isBefore(start) && !date.isAfter(end);
                        })
                        .collect(Collectors.toList());
            } catch (DateTimeParseException e) {
                log.warn("时间范围解析失败: startDate={}, endDate={}", startDate, endDate, e);
            }
        }

        // 计算分页的起始和结束索引
        int totalSize = modelNumVoList.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalSize);

        // 校验起始索引是否有效
        if (fromIndex >= totalSize || fromIndex < 0) {
            return new TableDataInfo(new ArrayList<>(), totalSize); // 返回空列表
        }

        // 返回分页后的子列表
        List<ModelNumVo> paginatedList = modelNumVoList.subList(fromIndex, toIndex);
        return new TableDataInfo(paginatedList, totalSize);
    }

    public List<ModelNumVo> getAllModelNumLis(String modelId, String startDate, String endDate) {
        Map<String, String> cacheMap = redisCache.getCacheMap(LogicParamsCons.MODEL_NUM);
        List<ModelNumVo> modelNumVoList = new ArrayList<>();

        for (Map.Entry<String, String> entry : cacheMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if (com.ai.common.utils.StringUtils.isNotBlank(value)) {
                try {
                    // 解析 JSON 字符串为 ModelNumVo 列表
                    List<ModelNumVo> modelNums = JsonUtils.writeToList(value, ModelNumVo.class);
                    for (ModelNumVo modelNum : modelNums) {
                        // 将 key 转换为 LocalDate 并赋值到 ModelNumVo 的 date 字段
                        try {
                            LocalDate date = LocalDate.parse(key, DateTimeFormatter.ofPattern("yyyyMMdd"));
                            modelNum.setDate(date);
                            modelNum.setModelName(ModelType.getValueByLabel(modelNum.getModelId()));
                        } catch (DateTimeParseException e) {
                            log.warn("日期解析失败: key={}", key, e);
                        }
                    }
                    // 将处理后的列表添加到最终结果中
                    modelNumVoList.addAll(modelNums);
                } catch (JsonProcessingException e) {
                    log.info("解析 Redis 缓存失败", e);
                }
            }
        }

        // 过滤条件
        if (StringUtils.isNotBlank(modelId)) {
            modelNumVoList = modelNumVoList.stream()
                    .filter(modelNum -> modelId.equals(modelNum.getModelId()))
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            try {
                LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                modelNumVoList = modelNumVoList.stream()
                        .filter(modelNum -> {
                            LocalDate date = modelNum.getDate();
                            return date != null && !date.isBefore(start) && !date.isAfter(end);
                        })
                        .collect(Collectors.toList());
            } catch (DateTimeParseException e) {
                log.warn("时间范围解析失败: startDate={}, endDate={}", startDate, endDate, e);
            }
        }
        return modelNumVoList;
    }

}
