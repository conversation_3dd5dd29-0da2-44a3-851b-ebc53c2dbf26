package com.ai.defined.utils;

import com.maxmind.geoip2.DatabaseReader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;

@Configuration
public class GeoIpCityConfig {

    private final ResourceLoader resourceLoaderCity;

    public GeoIpCityConfig(ResourceLoader resourceLoader) {
        this.resourceLoaderCity = resourceLoader;
    }

    @Bean
    public DatabaseReader databaseReaderCity() throws IOException {
        Resource resource = resourceLoaderCity.getResource("classpath:GeoLite2-City.mmdb");
        return new DatabaseReader.Builder(resource.getInputStream()).build();
    }
}