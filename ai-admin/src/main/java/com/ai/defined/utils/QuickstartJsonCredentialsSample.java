package com.ai.defined.utils;

import com.ai.common.utils.DoubleMathUtils;
import com.google.analytics.data.v1beta.BetaAnalyticsDataClient;
import com.google.analytics.data.v1beta.BetaAnalyticsDataSettings;
import com.google.analytics.data.v1beta.DateRange;
import com.google.analytics.data.v1beta.Dimension;
import com.google.analytics.data.v1beta.Metric;
import com.google.analytics.data.v1beta.Row;
import com.google.analytics.data.v1beta.RunReportRequest;
import com.google.analytics.data.v1beta.RunReportResponse;
import com.google.api.client.util.Value;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.oauth2.GoogleCredentials;
import org.springframework.core.io.ResourceLoader;

import java.io.FileInputStream;

public class QuickstartJsonCredentialsSample {

  @Value("${google.analytics.credentials}")
  private static String credentialsPath="classpath:piclumen-c034b-b0ffa477b5ee-web.json";
  public static void main(String... args) throws Exception {

    String propertyId = "459693876";
//    String propertyId = "445844306";

    String credentialsJsonPath = "C:\\Users\\<USER>\\Documents\\java-docs-samples\\google-analytics-data\\src\\main\\java\\piclumen-new.json";
//    String credentialsJsonPath = "C:\\Users\\<USER>\\Documents\\ai_manager\\ai-admin\\src\\main\\resources\\piclumen-c034b-b0ffa477b5ee-web.json";
    sampleRunReport(propertyId, credentialsJsonPath);
  }

  // This is an example snippet that calls the Google Analytics Data API and runs a simple report
  // on the provided GA4 property id.
  static void sampleRunReport(String propertyId, String credentialsJsonPath) throws Exception {
    // [START analyticsdata_json_credentials_initialize]
    // Explicitly use service account credentials by specifying
    // the private key file.

    credentialsPath="piclumen-new.json";
//    credentialsPath="piclumen-c034b-b0ffa477b5ee-web.json";
    GoogleCredentials credentials =
//        GoogleCredentials.fromStream(new FileInputStream(credentialsJsonPath))
        GoogleCredentials.fromStream(ResourceLoader.class.getClassLoader().getResourceAsStream(credentialsPath))
            .createScoped(
                "https://www.googleapis.com/auth/analytics.readonly",
                "https://www.googleapis.com/auth/analytics");

    BetaAnalyticsDataSettings betaAnalyticsDataSettings =
        BetaAnalyticsDataSettings.newBuilder()
            .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
            .build();

    try (BetaAnalyticsDataClient analyticsData =
        BetaAnalyticsDataClient.create(betaAnalyticsDataSettings)) {
      // [END analyticsdata_json_credentials_initialize]

      // [START analyticsdata_json_credentials_run_report]
      RunReportRequest request =
          RunReportRequest.newBuilder()
              .setProperty("properties/" + propertyId)
              .addDimensions(Dimension.newBuilder().setName("date"))
//              .addMetrics(Metric.newBuilder().setName("activeUsers"))
              .addMetrics(Metric.newBuilder().setName("activeUsers"))
                  .addMetrics(Metric.newBuilder().setName("active7DayUsers"))
                  .addMetrics(Metric.newBuilder().setName("active28DayUsers"))
                  .addMetrics(Metric.newBuilder().setName("newUsers"))
                  .addMetrics(Metric.newBuilder().setName("dauPerMau"))

                  .addDimensions(Dimension.newBuilder().setName("platform"))

//              .addDateRanges(DateRange.newBuilder().setStartDate("2024-11-26").setEndDate("today"))
//                  .addDimensions(Dimension.newBuilder().setName("Piclumen"))
                  .addDateRanges(
                          DateRange.newBuilder().setStartDate("2025-03-13").setEndDate("2025-03-13"))
//                  .addDateRanges(DateRange.newBuilder().setStartDate("7daysAgo").setEndDate("yesterday"))
              .build();

      // Make the request.
      RunReportResponse response = analyticsData.runReport(request);
      // [END analyticsdata_json_credentials_run_report]

      // [START analyticsdata_json_credentials_print_report]
//      System.out.println(response);
      System.out.println("Report result:");
      // Iterate through every row of the API response.
      int rowCount = 0;
      for (Row row : response.getRowsList()) {
        Double v = Double.parseDouble(row.getMetricValues(4).getValue());
        Double i = Double.parseDouble(row.getMetricValues(0).getValue());
//        Double div = DoubleMathUtils.div(i, v, 0);
//        long round = Math.round(div);
//        rowCount += Integer.parseInt(row.getMetricValues(0).getValue());
        System.out.printf(
            "日期：%s, 日活:%s  周活:%s  首次访问量:%s  " +
//                    "月活:%s " +
                    " 平台:%s  %n", row.getDimensionValues(0).getValue(), row.getMetricValues(0).getValue(),
                row.getMetricValues(1).getValue(),
                row.getMetricValues(3).getValue(),

//                round,
//                row.getMetricValues(2).getValue()
                row.getDimensionValues(1).getValue()
        );
      }
//      System.out.println(rowCount);
    }
  }
}
