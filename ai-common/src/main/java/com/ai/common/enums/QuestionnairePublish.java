package com.ai.common.enums;

public enum QuestionnairePublish {

    not(0, "未发布"),
    cancel(1, "取消发布"),
    published(5, "已发布");

    private Integer value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    QuestionnairePublish(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
