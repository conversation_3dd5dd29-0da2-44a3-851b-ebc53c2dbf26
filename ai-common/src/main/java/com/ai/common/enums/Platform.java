package com.ai.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public enum Platform {

    WEB("web"),  // WEB 平台
    IOS("ios"),         // iOS 平台
    ANDROID("android"); // Android 平台

    private final String platformName;

    public final static Set<String> allValues = Set.of(Arrays.stream(values()).map(Platform::getPlatformName).toArray(String[]::new));

    // 构造方法
    Platform(String platformName) {
        this.platformName = platformName;
    }

    // 获取平台名称的方法
    public String getPlatformName() {
        return platformName;
    }

    // 根据平台名称获取对应的枚举
    public static Platform fromString(String platformName) {
        for (Platform platform : Platform.values()) {
            if (platform.platformName.equalsIgnoreCase(platformName)) {
                return platform;
            }
        }
        throw new IllegalArgumentException("Unknown platform: " + platformName);
    }

    public static Platform getByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return WEB;
        }
        return Arrays.stream(values()).filter(type -> type.platformName.equalsIgnoreCase(value)).findFirst().orElse(WEB);
    }

    public static boolean mobilePlatform(String platform) {
        if (StringUtils.isBlank(platform)) {
            return false;
        }
        return IOS.getPlatformName().equalsIgnoreCase(platform) || ANDROID.getPlatformName().equalsIgnoreCase(platform);
    }

    public static boolean isAllPlatformSelected(String platforms) {
        if (StringUtils.isBlank(platforms)) {
            return false;
        }

        // 先拆分，去除空白，转换小写，放到Set去重
        Set<String> inputSet = Arrays.stream(platforms.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        // allValues 已经是全部 platformName 的集合，这里转小写方便比较
        Set<String> allPlatformNames = allValues.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        // 判断传入的集合是否包含全部平台
        return inputSet.containsAll(allPlatformNames);
    }
}
