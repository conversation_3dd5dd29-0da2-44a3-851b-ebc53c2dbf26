package com.ai.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum PaymentType {
    PLAN("plan"),
    ONE("one");

    private final String type;

    PaymentType(String type) {
        this.type = type;
    }

    @JsonValue
    public String getType() {
        return type;
    }

    public static PaymentType fromString(String type) {
        for (PaymentType paymentType : PaymentType.values()) {
            if (paymentType.getType().equalsIgnoreCase(type)) {
                return paymentType;
            }
        }
        throw new IllegalArgumentException("Invalid payment type: " + type);
    }

    @JsonCreator
    public static PaymentType fromValue(String value) {
        for (PaymentType paymentType : PaymentType.values()) {
            if (paymentType.type.equalsIgnoreCase(value)) {
                return paymentType;
            }
        }
        throw new IllegalArgumentException("Unknown PaymentType: " + value);
    }
}