package com.ai.common.enums;

public enum ModelType {

    realistic("realistic", "34ec1b5a-8962-4a93-b047-68cec9691dc2"),
    anime("anime", "cb4af9c7-41b0-47d3-944a-221446c7b8bc"),
    lineart("lineart", "2f0d593a-47db-42e6-b90b-e4534df65a98"),
    flux("flux","e40d01af-dec2-49dd-8944-f2aae4ba0b05"),
    pony("pony","14a399de-69d9-4e3b-961d-e95b35853557"),
    art("art","23887bba-507e-4249-a0e3-6951e4027f2b"),

    fluxDev("fluxDev","08e4f71a-416d-4f1e-a853-6b0178af1f09");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    ModelType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static String getValueByLabel(String label) {
        for (ModelType modelType : ModelType.values()) {
            if (modelType.getLabel().equals(label)) {
                return modelType.getValue();
            }
        }
       return "";
    }
}
