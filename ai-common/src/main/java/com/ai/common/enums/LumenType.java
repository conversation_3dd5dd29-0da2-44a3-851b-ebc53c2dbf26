package com.ai.common.enums;

public enum LumenType {

    recharge(1, "充值"),
    vip(2, "vip赠送"),
    gift(3, "活动赠送");

    private Integer value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    LumenType(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
