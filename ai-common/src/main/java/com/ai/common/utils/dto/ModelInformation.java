package com.ai.common.utils.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;


@Data
public class ModelInformation {

    private List<ModelAbout> modelMessage;

    @ToString
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ModelAbout {

        @ApiModelProperty("模型id")
        private String modelId;

        @ApiModelProperty("模型名称")
        private String modelDisplay;

        @ApiModelProperty("模型类型")
        private String modelType;

        @ApiModelProperty("默认超分降噪指数")
        private Double defaultHdFixDenoise;

        @ApiModelProperty("模型图标")
        private String modelAvatar;

        @ApiModelProperty("模型描述")
        private String modelDesc;

        @ApiModelProperty("模型排序")
        private int modelOrder;

        @ApiModelProperty("模型详情")
        private DefaultConfig defaultConfig;

        @ApiModelProperty("模型所支持的风格类型")
        private List<SupportStyle> supportStyleList;

    }

    @ToString
    @Data
    public static class DefaultConfig {

        @ApiModelProperty("宽")
        private int width;

        @ApiModelProperty("高")
        private int height;

        @ApiModelProperty("种子（一串随机数）")
        private Long seed;

        @ApiModelProperty("迭代步数")
        private int steps;

        @ApiModelProperty("提示词引导系数")
        private double cfg;

        @ApiModelProperty("采样器名称")
        private String samplerName;

        @ApiModelProperty("调度器")
        private String scheduler;

        @ApiModelProperty("降噪幅度")
        private double denoise;

        @ApiModelProperty("正向提示器")
        private String positivePrompt;

        @ApiModelProperty("反向提示词")
        private String negativePrompt;

        @ApiModelProperty("高清修复降噪幅度")
        private double hiresFixDenoise;

        @ApiModelProperty("高清修复倍数")
        private double hiresScale;

        @ApiModelProperty("模型扩展能力")
        private ModelAbility modelAbility;
    }

    @ToString
    @Data
    public static class ModelAbility {

        @ApiModelProperty("0.0 - 1.0 之间，默认 0 不生效")
        private double animeStyleControl;

    }

    @ToString
    @Data
    public static class SupportStyle {

        @ApiModelProperty("风格标签")
        private String label;

        @ApiModelProperty("风格标签头像")
        private String avatar;

        @ApiModelProperty("风格标签值")
        private String value;

        @ApiModelProperty("排序")
        private Integer sort;

        @ApiModelProperty("默认权重")
        private Double weight;
    }

}
