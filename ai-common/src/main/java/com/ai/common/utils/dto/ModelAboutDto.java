package com.ai.common.utils.dto;

import com.ai.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ModelAboutDto {

    private Long id;

    @ApiModelProperty("模型来源类型：0-工作流模式，1-API模式（第三方）")
    private Integer modelOriginType;

    @ApiModelProperty("模型id")
    private String modelId;

    @ApiModelProperty("模型名称")
    private String modelDisplay;

    @ApiModelProperty("模型类型")
    private String modelType;

    @ApiModelProperty("默认超分降噪指数")
    private Double defaultHdFixDenoise;

    @ApiModelProperty("模型图标")
    private String modelAvatar;

    @ApiModelProperty("模型描述")
    private String modelDesc;

    @ApiModelProperty("模型排序")
    private int modelOrder;

    @ApiModelProperty("模型详情")
    private ModelInformation.DefaultConfig defaultConfig;

    @ApiModelProperty("模型所支持的风格类型")
    private List<ModelInformation.SupportStyle> supportStyleList;

    @ApiModelProperty("平台")
    private List<String> platforms;

    @ApiModelProperty("ios排序")
    private Integer iosOrder;

    @ApiModelProperty("android排序")
    private Integer  androidOrder;
}
