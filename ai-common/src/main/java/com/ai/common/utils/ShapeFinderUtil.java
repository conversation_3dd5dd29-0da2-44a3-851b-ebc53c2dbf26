package com.ai.common.utils;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public final class ShapeFinderUtil {
    @Getter
    static class Shape {
        int width;
        int height;
        double shapeRatio;
        String ratioStr;

        public Shape(int width, int height, double shapeRatio) {
            this.width = width;
            this.height = height;
            this.shapeRatio = shapeRatio;
        }

        public Shape(int width, int height, double shapeRatio, String ratioStr) {
            this.width = width;
            this.height = height;
            this.shapeRatio = shapeRatio;
            this.ratioStr = ratioStr;
        }

    }

    private static final List<Shape> SHAPE_ALL = Arrays.asList(
            new Shape(1024, 1024, 1024 / 1024D),
            new Shape(1536, 640, 1536 / 640D),
            new Shape(640, 1536, 640 / 1536D),
            new Shape(1472, 704, 1472 / 704D),
            new Shape(704, 1472, 704 / 1472D),
            new Shape(1280, 640, 1280 / 640D),
            new Shape(640, 1280, 640 / 1280D),
            new Shape(1344, 768, 1344 / 768D),
            new Shape(768, 1344, 768 / 1344D, "9:16"),
            new Shape(1216, 768, 1216 / 768D),
            new Shape(768, 1216, 768 / 1216D),
            new Shape(1216, 832, 1216 / 768D),
            new Shape(832, 1216, 832 / 1216D),
            new Shape(1152, 896, 1152 / 896D),
            new Shape(896, 1152, 896 / 1152D),
            new Shape(1088, 960, 1088 / 960D),
            new Shape(960, 1088, 960 / 1088D)
    );

    public static Shape findClosestShape(int imageWidth, int imageHeight) {
        double targetRatio = (double) imageWidth / imageHeight;
        Shape closestShape = null;
        double minDiff = Double.MAX_VALUE;

        for (Shape shape : SHAPE_ALL) {
            double shapeRatio = shape.getShapeRatio();
            double diff = Math.abs(targetRatio - shapeRatio);

            if (diff < minDiff) {
                minDiff = diff;
                closestShape = shape;
            }
        }
        return closestShape;
    }

    public static boolean isAssignableRatio(int imageWidth, int imageHeight, String ratioStr) {
        Shape closestShape = findClosestShape(imageWidth, imageHeight);
        return closestShape != null &&  ratioStr.equals(closestShape.getRatioStr());
    }
}