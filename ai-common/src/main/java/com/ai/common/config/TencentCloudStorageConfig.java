package com.ai.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "tencent-cloud.storage")
@Data
public class TencentCloudStorageConfig {

    private String secretId;
    private String secretKey;
    private String region;
    private String bucketName;
    private String bucketNameOld;
    private String baseSuffix;
    private String accelerateSuffix;
    private String miniRule;
    private String baseDomain;
    private String accelerateDomain;
}