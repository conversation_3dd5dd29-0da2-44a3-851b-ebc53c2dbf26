package com.ai.common.core.redis;

import com.alibaba.excel.util.StringUtils;
import io.netty.util.internal.StringUtil;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * spring redis(c端 piclumen Reids操作 数据库) 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisCachePiclumen {

    @Resource
    public RedisTemplate redisTemplateForPiclumen;


    @Resource
    public StringRedisTemplate redisStringRedisTemplatePiclumen;




    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplateForPiclumen.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        redisTemplateForPiclumen.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplateForPiclumen.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        return redisTemplateForPiclumen.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplateForPiclumen.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplateForPiclumen.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplateForPiclumen.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        return redisTemplateForPiclumen.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplateForPiclumen.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplateForPiclumen.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplateForPiclumen.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplateForPiclumen.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplateForPiclumen.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplateForPiclumen.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplateForPiclumen.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplateForPiclumen.opsForHash();
        return opsForHash.get(key, hKey);
    }

    public Object getDataFromHash(String key, String field) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplateForPiclumen.opsForHash().get(key, field);
    }

    /**
     * 批量获取hash的数据
     * @param key
     * @param hKey
     * @return
     * @param <T>
     */
    public <T> List<T> getCacheMapMultiValue(final String key, final Collection hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplateForPiclumen.opsForHash();
        return opsForHash.multiGet(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplateForPiclumen.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplateForPiclumen.opsForHash().delete(key, hKey) > 0;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplateForPiclumen.keys(pattern);
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public DataType getType(final String pattern) {
        return  redisTemplateForPiclumen.type(pattern);
    }

    public  Set<String> getZSetAll(final String key) {
        return  redisTemplateForPiclumen.opsForZSet().range(key,0,-1);
    }

    public  Set<ZSetOperations.TypedTuple<String>> getZSetAllVaule(final String key) {
        return  redisTemplateForPiclumen.opsForZSet().rangeWithScores(key,0,-1);
    }

    public RedisTemplate getRedisTemplate(){
        return redisTemplateForPiclumen;
    }


    /**
     * 获取list结果集
     * @param key
     * @return
     */
    public List<Object> getList(String key) {
        // 获取整个列表数据
        List<Object> list = redisTemplateForPiclumen.opsForList().range(key, 0, -1);
        return list;
    }

    /**
     * 移除list中（从头到尾（从左到右）第一个元素）
     * @param key
     * @param value
     * @return
     */
    public void listRemoveValue(String key,Object value) {
        redisTemplateForPiclumen.opsForList().remove(key, 1, value);
    }

    public String stringGet(String key) {
        return redisStringRedisTemplatePiclumen.opsForValue().get(key);
    }

    public void stringSet(String key, String value, long timeOutSec , TimeUnit unit) {
        redisStringRedisTemplatePiclumen.opsForValue().set(key, value, timeOutSec, unit);
    }

    public void stringSet(String key, String value) {
        redisStringRedisTemplatePiclumen.opsForValue().set(key, value);
    }

    //zset获取元素
    public Object getDataFromZset(String key,String value){
        return redisTemplateForPiclumen.opsForZSet().score(key, value);
    }

    public T get(String key) {
        ValueOperations<String, Object> valueOperations = redisTemplateForPiclumen.opsForValue();
        return (T) valueOperations.get(key);
    }

    public<T> void set(String key, T value) {
        ValueOperations<String, Object> valueOperations = redisTemplateForPiclumen.opsForValue();
        valueOperations.set(key, value);
    }

    public void addOBjectDataToSet(String key, Object value) {
        redisTemplateForPiclumen.opsForSet().add(key, value);
    }

    public Long getSetSize(String key) {
        return redisTemplateForPiclumen.opsForSet().size(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean unlink(final String key) {
        return redisTemplateForPiclumen.unlink(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean unlinkBatch(final Collection collection) {
        return redisTemplateForPiclumen.unlink(collection) > 0;
    }

    public Long incrementForHash(String key, String hashKey, long value) {
        return redisTemplateForPiclumen.opsForHash().increment(key, hashKey, value);
    }

    public Long increment(String key, long delta) {
        return redisTemplateForPiclumen.opsForValue().increment(key, delta);
    }
}
