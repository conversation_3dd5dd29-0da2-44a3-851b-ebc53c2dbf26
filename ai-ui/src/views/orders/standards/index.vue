<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会员类型" prop="vipType">
        <el-select v-model="queryParams.vipType" placeholder="请选择会员类型" clearable>
          <el-option
            v-for="dict in dict.type.vip_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以批量下载" prop="batchDownload">
        <el-select v-model="queryParams.batchDownload" placeholder="请选择是否可以批量下载" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以超分" prop="upscale">
        <el-select v-model="queryParams.upscale" placeholder="请选择是否可以超分" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以局部重绘" prop="inpaint">
        <el-select v-model="queryParams.inpaint" placeholder="请选择是否可以局部重绘" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以局部扩图" prop="expand">
        <el-select v-model="queryParams.expand" placeholder="请选择是否可以局部扩图" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以线稿上色" prop="colorize">
        <el-select v-model="queryParams.colorize" placeholder="请选择是否可以线稿上色" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以去背景" prop="removeBg">
        <el-select v-model="queryParams.removeBg" placeholder="请选择是否可以去背景" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否展示历史页" prop="historyExplore">
        <el-select v-model="queryParams.historyExplore" placeholder="请选择是否展示历史页" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以翻译" prop="translation">
        <el-select v-model="queryParams.translation" placeholder="请选择是否可以翻译" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可以增强" prop="enhance">
        <el-select v-model="queryParams.enhance" placeholder="请选择是否可以增强" clearable>
          <el-option
            v-for="dict in dict.type.is_del"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['orders:standards:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['orders:standards:edit']"
        >修改</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['orders:standards:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['orders:standards:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="standardsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="会员类型" align="center" prop="vipType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vip_type" :value="scope.row.vipType"/>
        </template>
      </el-table-column>
      <el-table-column label="每日免费点数" align="center" prop="dailyLumens" />
      <el-table-column label="会员点数" align="center" prop="monthlyLumens" />
      <el-table-column label="历史页图片保留时长" align="center" prop="creationHistory" />
      <el-table-column label="排队任务数" align="center" prop="taskQueue" />
      <el-table-column label="并发任务数" align="center" prop="concurrentJobs" />
      <el-table-column label="是否可以批量下载" align="center" prop="batchDownload">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.batchDownload"/>
        </template>
      </el-table-column>
      <el-table-column label="批量生图数" align="center" prop="imagesPerBatch" />
      <el-table-column label="用户收藏夹张数标准" align="center" prop="collectNum" />
      <el-table-column label="是否可以超分" align="center" prop="upscale">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.upscale"/>
        </template>
      </el-table-column>
      <el-table-column label="是否可以局部重绘" align="center" prop="inpaint">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.inpaint"/>
        </template>
      </el-table-column>
      <el-table-column label="是否可以局部扩图： 0 否 1 是" align="center" prop="expand">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.expand"/>
        </template>
      </el-table-column>
      <el-table-column label="是否可以线稿上色" align="center" prop="colorize">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.colorize"/>
        </template>
      </el-table-column>
      <el-table-column label="是否可以去背景" align="center" prop="removeBg">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.removeBg"/>
        </template>
      </el-table-column>
      <el-table-column label="是否展示历史页" align="center" prop="historyExplore">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.historyExplore"/>
        </template>
      </el-table-column>
      <el-table-column label="是否可以翻译" align="center" prop="translation">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.translation"/>
        </template>
      </el-table-column>
      <el-table-column label="是否可以增强" align="center" prop="enhance">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.enhance"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['orders:standards:edit']"
          >修改</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['orders:standards:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员资源标准对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员类型" prop="vipType">
          <el-select v-model="form.vipType" placeholder="请选择会员类型">
            <el-option
              v-for="dict in dict.type.vip_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="每日免费点数" prop="dailyLumens">
          <el-input v-model="form.dailyLumens" placeholder="请输入每日免费点数" />
        </el-form-item>

        <el-form-item label="会员点数" prop="monthlyLumens">
          <el-input v-model="form.monthlyLumens" placeholder="请输入会员点数" />
        </el-form-item>
        <el-form-item label="历史页图片保留时长" prop="creationHistory">
          <el-input v-model="form.creationHistory" placeholder="请输入历史页图片保留时长" />
        </el-form-item>
        <el-form-item label="排队任务数" prop="taskQueue">
          <el-input v-model="form.taskQueue" placeholder="请输入排队任务数" />
        </el-form-item>
        <el-form-item label="并发任务数" prop="concurrentJobs">
          <el-input v-model="form.concurrentJobs" placeholder="请输入并发任务数" />
        </el-form-item>
        <el-form-item label="是否可以批量下载" prop="batchDownload">
          <el-select v-model="form.batchDownload" placeholder="请选择是否可以批量下载">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="批量生图数" prop="imagesPerBatch">
          <el-input v-model="form.imagesPerBatch" placeholder="请输入批量生图数" />
        </el-form-item>
        <el-form-item label="用户收藏夹张数标准" prop="collectNum">
          <el-input v-model="form.collectNum" placeholder="请输入用户收藏夹张数标准" />
        </el-form-item>
        <el-form-item label="是否可以超分" prop="upscale">
          <el-select v-model="form.upscale" placeholder="请选择是否可以超分">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可以局部重绘" prop="inpaint">
          <el-select v-model="form.inpaint" placeholder="请选择是否可以局部重绘">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可以局部扩图： 0 否 1 是" prop="expand">
          <el-select v-model="form.expand" placeholder="请选择是否可以局部扩图： 0 否 1 是">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可以线稿上色" prop="colorize">
          <el-select v-model="form.colorize" placeholder="请选择是否可以线稿上色">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可以去背景" prop="removeBg">
          <el-select v-model="form.removeBg" placeholder="请选择是否可以去背景">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否展示历史页" prop="historyExplore">
          <el-select v-model="form.historyExplore" placeholder="请选择是否展示历史页">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可以翻译" prop="translation">
          <el-select v-model="form.translation" placeholder="请选择是否可以翻译">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可以增强" prop="enhance">
          <el-select v-model="form.enhance" placeholder="请选择是否可以增强">
            <el-option
              v-for="dict in dict.type.is_del"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStandards, getStandards, delStandards, addStandards, updateStandards } from "@/api/orders/standards";

export default {
  name: "Standards",
  dicts: ['is_del', 'vip_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会员资源标准表格数据
      standardsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vipType: null,
        batchDownload: null,
        upscale: null,
        inpaint: null,
        expand: null,
        colorize: null,
        removeBg: null,
        historyExplore: null,
        translation: null,
        enhance: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会员资源标准列表 */
    getList() {
      this.loading = true;
      listStandards(this.queryParams).then(response => {
        this.standardsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        vipType: null,
        dailyLumens: null,
        monthlyLumens: null,
        creationHistory: null,
        taskQueue: null,
        concurrentJobs: null,
        batchDownload: null,
        imagesPerBatch: null,
        upscale: null,
        inpaint: null,
        expand: null,
        colorize: null,
        removeBg: null,
        historyExplore: null,
        translation: null,
        enhance: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会员资源标准";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getStandards(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会员资源标准";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStandards(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStandards(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除会员资源标准编号为"' + ids + '"的数据项？').then(function() {
        return delStandards(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orders/standards/export', {
        ...this.queryParams
      }, `standards_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
