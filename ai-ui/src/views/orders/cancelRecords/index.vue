<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="loginName">
        <el-input
          v-model="queryParams.loginName"
          placeholder="请输入用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Stripe 客户 ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入Stripe 客户 ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="取消失败的 老的订阅 ID" prop="oldSubscriptionId">
        <el-input
          v-model="queryParams.oldSubscriptionId"
          placeholder="请输入取消失败的 老的订阅 ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已经升级成功订阅 ID" prop="newSubscriptionId">
        <el-input
          v-model="queryParams.newSubscriptionId"
          placeholder="请输入已经升级成功订阅 ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['orders:cancelRecords:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['orders:cancelRecords:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['orders:cancelRecords:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['orders:cancelRecords:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cancelRecordsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键 ID" align="center" prop="id" />
      <el-table-column label="用户 ID" align="center" prop="userId" />
      <el-table-column label="用户账号" align="center" prop="loginName" />
      <el-table-column label="Stripe 客户 ID" align="center" prop="customerId" />
      <el-table-column label="取消失败的 老的订阅 ID" align="center" prop="oldSubscriptionId" />
      <el-table-column label="已经升级成功订阅 ID" align="center" prop="newSubscriptionId" />
      <el-table-column label="新产品价格 ID" align="center" prop="newPriceId" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['orders:cancelRecords:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['orders:cancelRecords:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改升级订阅时取消旧订阅失败记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户 ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户 ID" />
        </el-form-item>
        <el-form-item label="用户账号" prop="loginName">
          <el-input v-model="form.loginName" placeholder="请输入用户账号" />
        </el-form-item>
        <el-form-item label="Stripe 客户 ID" prop="customerId">
          <el-input v-model="form.customerId" placeholder="请输入Stripe 客户 ID" />
        </el-form-item>
        <el-form-item label="取消失败的 老的订阅 ID" prop="oldSubscriptionId">
          <el-input v-model="form.oldSubscriptionId" placeholder="请输入取消失败的 老的订阅 ID" />
        </el-form-item>
        <el-form-item label="已经升级成功订阅 ID" prop="newSubscriptionId">
          <el-input v-model="form.newSubscriptionId" placeholder="请输入已经升级成功订阅 ID" />
        </el-form-item>
        <el-form-item label="新产品价格 ID" prop="newPriceId">
          <el-input v-model="form.newPriceId" placeholder="请输入新产品价格 ID" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCancelRecords, getCancelRecords, delCancelRecords, addCancelRecords, updateCancelRecords } from "@/api/orders/cancelRecords";

export default {
  name: "CancelRecords",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 升级订阅时取消旧订阅失败记录表格数据
      cancelRecordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 新产品价格 ID时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        loginName: null,
        customerId: null,
        oldSubscriptionId: null,
        newSubscriptionId: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户 ID不能为空", trigger: "blur" }
        ],
        loginName: [
          { required: true, message: "用户账号不能为空", trigger: "blur" }
        ],
        customerId: [
          { required: true, message: "Stripe 客户 ID不能为空", trigger: "blur" }
        ],
        oldSubscriptionId: [
          { required: true, message: "取消失败的 老的订阅 ID不能为空", trigger: "blur" }
        ],
        newSubscriptionId: [
          { required: true, message: "已经升级成功订阅 ID不能为空", trigger: "blur" }
        ],
        newPriceId: [
          { required: true, message: "新产品价格 ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询升级订阅时取消旧订阅失败记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listCancelRecords(this.queryParams).then(response => {
        this.cancelRecordsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        loginName: null,
        customerId: null,
        oldSubscriptionId: null,
        newSubscriptionId: null,
        newPriceId: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        del: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加升级订阅时取消旧订阅失败记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCancelRecords(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改升级订阅时取消旧订阅失败记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCancelRecords(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCancelRecords(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除升级订阅时取消旧订阅失败记录编号为"' + ids + '"的数据项？').then(function() {
        return delCancelRecords(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orders/cancelRecords/export', {
        ...this.queryParams
      }, `cancelRecords_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
