<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="loginName">
        <el-input
          v-model="queryParams.loginName"
          placeholder="请输入用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="平台唯一标识" prop="subscriptionId">
        <el-input
          v-model="queryParams.subscriptionId"
          placeholder="请输入平台唯一标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="平台原交易id" prop="originalTransactionId">
        <el-input
          v-model="queryParams.originalTransactionId"
          placeholder="请输入原交易id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="交易id" prop="transactionId">
        <el-input
          v-model="queryParams.transactionId"
          placeholder="请输入交易id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员类型" prop="planLevel">
        <el-select v-model="queryParams.planLevel" placeholder="请选择会员类型" clearable>
          <el-option
            v-for="dict in dict.type.vip_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="价格间隔" prop="priceInterval">
        <el-select v-model="queryParams.priceInterval" placeholder="请选择价格间隔" clearable>
          <el-option
            v-for="dict in dict.type.stripe_schedule_record_interval"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订阅平台" prop="vipPlatform">
        <el-select v-model="queryParams.vipPlatform" placeholder="请选择订阅平台" clearable>
          <el-option
            v-for="dict in dict.type.vip_platform_dict"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="自动续订状态" prop="autoRenewStatus">
        <el-select v-model="queryParams.autoRenewStatus" placeholder="请选择自动续订状态" clearable>
          <el-option
            v-for="dict in dict.type.auto_renew_status_dict"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否有效" prop="invalid">
        <el-select v-model="queryParams.invalid" placeholder="请选择是否有效" clearable>
          <el-option
            v-for="dict in dict.type.invalid_dict"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="会员生效时间">
        <el-date-picker
          v-model="daterangeVipBeginTime"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
     <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['orders:current:add']"
        >赠送会员</el-button>
      </el-col>
        <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['orders:current:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['orders:current:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['orders:current:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="currentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="用户账号" align="center" prop="loginName" />
      <el-table-column label="平台唯一标识" align="center" prop="subscriptionId" />
      <el-table-column label="原交易id" align="center" prop="originalTransactionId" />
      <el-table-column label="交易id" align="center" prop="transactionId" />
      <el-table-column label="订阅每周期开始时间" align="center" prop="currentPeriodStart" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.currentPeriodStart, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订阅每周期结束时间" align="center" prop="currentPeriodEnd" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.currentPeriodEnd, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会员类型" align="center" prop="planLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vip_type" :value="scope.row.planLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="价格间隔" align="center" prop="priceInterval">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stripe_schedule_record_interval" :value="scope.row.priceInterval"/>
        </template>
      </el-table-column>
      <el-table-column label="会员生效时间" align="center" prop="vipBeginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.vipBeginTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会员过期时间" align="center" prop="vipEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.vipEndTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
  <el-table-column label="订阅平台" align="center" prop="vipPlatform">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vip_platform_dict" :value="scope.row.vipPlatform"/>
        </template>
      </el-table-column>
      <el-table-column label="是否有效" align="center" prop="invalid">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.invalid_dict" :value="scope.row.invalid"/>
        </template>
      </el-table-column>
      <el-table-column label="自动续订状态" align="center" prop="autoRenewStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.auto_renew_status_dict" :value="scope.row.autoRenewStatus"/>
        </template>
      </el-table-column>  
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['orders:current:edit']"
          >修改</el-button> -->
          
          <el-button
            v-if="scope.row.vipPlatform ==='stripe' && scope.row.invalid===0 "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleInvalid(scope.row)"
            v-hasPermi="['orders:current:edit']"
          >设置会员为无效</el-button>
          <el-button
            v-if="scope.row.vipPlatform ==='gift'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleRemove(scope.row)"
            v-hasPermi="['orders:current:remove']"
          >删除赠送会员</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改当前订阅对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item> -->
        <el-form-item label="用户账号" prop="loginName">
          <el-select v-model="form.loginName" filterable remote reserve-keyword placeholder="搜索用户账号"
            :remote-method="remoteMethod" :loading="loadingLoginName">
            <el-option v-for="item in options" :key="item.loginName" :label="item.loginName" :value="item.loginName">
            </el-option>
          </el-select>
          <!-- <el-input v-model="form.loginName" placeholder="请输入用户账号" /> -->
        </el-form-item>
        <!-- <el-form-item label="平台唯一标识" prop="subscriptionId">
          <el-input v-model="form.subscriptionId" placeholder="请输入平台唯一标识" />
        </el-form-item> -->
        <!-- <el-table-column label="订阅每周期开始时间" align="center" prop="currentPeriodStart" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.currentPeriodStart, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
        <el-form-item label="订阅每周期结束时间" prop="currentPeriodEnd">
          <el-input v-model="form.currentPeriodEnd" placeholder="请输入订阅每周期结束时间" />
        </el-form-item> -->
        <el-form-item label="会员类型" prop="planLevel">
          <el-select v-model="form.planLevel" placeholder="请选择会员类型">
            <el-option
              v-for="dict in dict.type.vip_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="价格间隔" prop="priceInterval">
          <el-select v-model="form.priceInterval" placeholder="请选择价格间隔">
            <el-option
              v-for="dict in dict.type.stripe_schedule_record_interval"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会员生效时间" prop="vipBeginTimeDate">
          <el-date-picker clearable v-model="form.vipBeginTimeDate" type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择会员生效时间">
          </el-date-picker>
        </el-form-item>
          <el-form-item label="会员过期时间" prop="vipEndTimeDate">
          <el-date-picker clearable v-model="form.vipEndTimeDate" type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择会员过期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="赠送会员名称" prop="subscriptionName">
          <el-input v-model="form.subscriptionName" placeholder="请输入赠送会员名称" />
        </el-form-item>
        <!-- <el-form-item label="订阅平台" prop="vipPlatform">
          <el-input v-model="form.vipPlatform" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="自动续订状态" prop="autoRenewStatus">
          <el-input v-model="form.autoRenewStatus" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCurrent, getCurrent, delCurrent, addCurrent, updateCurrent,removeCurrentOne,setInvalid } from "@/api/orders/current";
import { getUserByLoginName } from "@/api/admin/user";

export default {
  name: "Current",
  dicts: ['stripe_schedule_record_interval', 'vip_type','vip_platform_dict','auto_renew_status_dict', 'invalid_dict'],
  data() {
    return {
      options: [],
      loadingLoginName: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 当前订阅表格数据
      currentList: [],
      daterangeVipBeginTime:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        loginName: null,
        subscriptionId: null,
        planLevel: null,
        priceInterval: null,
        vipBeginTime: null,
        vipPlatform: null,
        autoRenewStatus: null,
        originalTransactionId: null,
        transactionId: null,
        invalid:null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subscriptionName: [
          { required: true, message: "赠送会员名称不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户id不能为空", trigger: "blur" }
        ],
        loginName: [
          { required: true, message: "用户账号不能为空", trigger: "blur" }
        ],
        planLevel: [
          { required: true, message: "会员类型不能为空", trigger: "blur" }
        ],
        priceInterval: [
          { required: true, message: "价格间隔不能为空", trigger: "blur" }
        ],
        subscriptionId: [
          { required: true, message: "平台唯一标识不能为空", trigger: "blur" }
        ],
        vipBeginTimeDate: [
          { required: true, message: '会员生效时间不能为空', trigger: 'blur' }
        ],
        vipEndTimeDate: [
          { required: true, message: '会员过期时间不能为空', trigger: 'blur' },
          { validator: this.validateEndTime, trigger: 'blur' }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleInvalid(row){
      const id = row.id
      this.$modal.confirm('是否确认设置用户:'+row.loginName+' 当前订阅编号为"' + id + '"的数据项为无效？').then(function() {
        return setInvalid(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("设置无效成功");
      }).catch(() => {});
    },
    validateEndTime(rule, value, callback) {
    if (value && this.form.vipBeginTimeDate) {
      // 转换为时间戳进行比较
      const startTime = new Date(this.form.vipBeginTimeDate).getTime();
      const endTime = new Date(value).getTime();
      if (endTime <= startTime) {
        callback(new Error('结束时间必须大于开始时间'));
      } else {
        callback(); // 验证通过
      }
    } else {
      callback(); // 如果没有开始时间，跳过验证
    }
  },
    remoteMethod(query) {
      if (query !== '') {
        getUserByLoginName(query).then(response => {
          this.options = response.data;
        });
      } else {
        this.options = [];
      }
    },
    parseTime(time, format) {
      if (!time) return ''; // 如果时间无效，直接返回空字符串

      // 确保 time 是字符串，并且是秒级时间戳，转换为毫秒级
      const timestamp = typeof time === 'string' && time.length === 10 ? parseInt(time) * 1000 : time;

      // 创建 Date 对象并检查其有效性
      const date = new Date(timestamp);

      // 如果日期无效，返回空字符串
      if (isNaN(date.getTime())) {
        return ''; // 或者你可以返回 '无效时间' 等提示
      }

      // 格式化日期为 yyyy-MM-dd HH:mm:ss
      if (format === 'yyyy-MM-dd HH:mm:ss') {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 如果没有匹配的 format，返回默认格式
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      const formatted = parts.map(part => part.value).join('');
      return formatted;
    },
    /** 查询当前订阅列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeVipBeginTime && '' != this.daterangeVipBeginTime) {
        this.queryParams.params["beginVipBegin"] = this.daterangeVipBeginTime[0];
        this.queryParams.params["endVipBegin"] = this.daterangeVipBeginTime[1];
      }
      listCurrent(this.queryParams).then(response => {
        this.currentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        loginName: null,
        subscriptionId: null,
        currentPeriodStart: null,
        currentPeriodEnd: null,
        planLevel: null,
        priceInterval: null,
        vipBeginTime: null,
        vipEndTime: null,
        vipPlatform: null,
        autoRenewStatus: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        subscriptionName:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeVipBeginTime = [];

      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "赠送会员 ";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCurrent(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改当前订阅";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCurrent(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
             // 根据选中的 loginName 来获取对应的用户信息
            const selectedUser = this.options.find(item => item.loginName === this.form.loginName);
            if (selectedUser) {
              // 自动赋值 userId
              this.form.userId = selectedUser.id;  // 假设选中的对象包含 userId
            }
            addCurrent(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleRemove(row){
      const id = row.id
      this.$modal.confirm('是否确认删除当前订阅编号为"' + id + '"的赠送会员数据项？').then(function() {
        return removeCurrentOne(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除当前订阅编号为"' + ids + '"的数据项？').then(function() {
        return delCurrent(ids);
      }).then(() => { 
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orders/current/export', {
        ...this.queryParams
      }, `current_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
