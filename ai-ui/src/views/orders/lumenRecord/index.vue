<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="loginName">
        <el-input v-model="queryParams.loginName" placeholder="请输入用户账号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="stripe customer" prop="customerId">
        <el-input v-model="queryParams.customerId" placeholder="请输入stripe customer" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="lumen 获得时间">
        <el-date-picker v-model="daterangeCurrentPeriodStart" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
          <el-option v-for="dict in dict.type.lumen_record_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="平台原交易id" prop="originalTransactionId">
        <el-input v-model="queryParams.originalTransactionId" placeholder="请输入原交易id" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="交易id" prop="transactionId">
        <el-input v-model="queryParams.transactionId" placeholder="请输入交易id" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="订阅平台" prop="vipPlatForm">
        <el-select v-model="queryParams.vipPlatForm" placeholder="请选择订阅平台" clearable>
          <el-option v-for="dict in dict.type.vip_platform_dict" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否有效" prop="invalid">
        <el-select v-model="queryParams.invalid" placeholder="请选择是否有效" clearable>
          <el-option v-for="dict in dict.type.invalid_dict" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['orders:lumenRecord:add']">赠送lumen</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['orders:lumenRecord:edit']">修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['orders:lumenRecord:remove']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['orders:lumenRecord:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="lumenRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="用户账号" align="center" prop="loginName" />
      <el-table-column label="stripe customer" align="center" prop="customerId" />
      <el-table-column label="原交易id" align="center" prop="originalTransactionId" />
      <el-table-column label="交易id" align="center" prop="transactionId" />
      <el-table-column label="lumen 到期时间" align="center" prop="currentPeriodEnd" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.currentPeriodEnd, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="lumen 获得时间" align="center" prop="currentPeriodStart" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.currentPeriodStart, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.lumen_record_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="lumen原有数量" align="center" prop="lumenQty" />
      <el-table-column label="lumen剩余数量" align="center" prop="lumenLeftQty" />
      <el-table-column label="订阅平台" align="center" prop="vipPlatForm">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vip_platform_dict" :value="scope.row.vipPlatForm" />
        </template>
      </el-table-column>
      <el-table-column label="是否为混合" align="center" prop="mixed" />
      <el-table-column label="是否有效" align="center" prop="invalid">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.invalid_dict" :value="scope.row.invalid" />
        </template>
      </el-table-column> <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleReduceLumen(scope.row)"
            v-hasPermi="['orders:lumenRecord:edit']">减少lumen</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleGiftLumen(scope.row)"
            v-hasPermi="['orders:lumenRecord:edit']">赠送lumen</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleInvalid(scope.row)"
            v-hasPermi="['orders:lumenRecord:edit']">设置无效</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleLogList(scope.row)"
            v-hasPermi="['orders:lumenRecord:edit']">查看操作日志</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改lumen实时记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户账号" prop="loginName">
          <el-select v-model="form.loginName" filterable remote reserve-keyword placeholder="搜索用户账号"
            :remote-method="remoteMethod" :loading="loadingLoginName">
            <el-option v-for="item in options" :key="item.loginName" :label="item.loginName" :value="item.loginName">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="lumen 获得时间" prop="currentPeriodStartDate">
          <el-date-picker clearable v-model="form.currentPeriodStartDate" type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择lumen 获得时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="lumen 到期时间" prop="currentPeriodEnDate">
          <el-date-picker clearable v-model="form.currentPeriodEnDate" type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择lumen 到期时间"
            @change="handleEndDateChange"></el-date-picker>
        </el-form-item>
        <el-form-item label="原因" prop="detail">
          <el-input v-model="form.detail" placeholder="请输入原因" />
        </el-form-item>
        <el-form-item label="赠送lumen数量" prop="lumenQty">
          <el-input v-model="form.lumenQty" placeholder="请输入赠送lumen数量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 减少或赠送lumen对话框 -->
    <el-dialog :title="title" :visible.sync="openLumen" width="500px" append-to-body  @close="cancelLumen">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户账号" prop="loginName">
          <el-input v-model="form.loginName" :disabled="true" />
        </el-form-item>
        <el-form-item label="lumen剩余数量" prop="lumenLeftQty">
          <el-input v-model="form.lumenLeftQty" :disabled="true" />
        </el-form-item>
        <el-form-item v-if="!this.gift && !this.isInvalid  " label="减少lumen量" prop="lumen">
          <el-input v-model="form.lumen" />
        </el-form-item>
        <el-form-item v-if="this.gift && !this.isInvalid " label="赠送lumen量" prop="giftLumen">
          <el-input v-model="form.giftLumen" />
        </el-form-item>
        <el-form-item label="原因" prop="detail">
          <el-input v-model="form.detail" placeholder="请输入原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="!this.gift && !this.isInvalid" @click="submitReduceLumen">减少lumen</el-button>
        <el-button type="primary" v-if="this.gift && !this.isInvalid" @click="submitGiftLumen">赠送lumen</el-button>
        <el-button type="primary" v-if="this.isInvalid" @click="submitInvalid">设置无效</el-button>
        <el-button @click="cancelLumen">取 消</el-button>
      </div>
    </el-dialog>



    <el-dialog :title="title" :visible.sync="openLog" width="800px" append-to-body>
      <template>
        <el-table :data="logList" height="250" border style="width: 100%">
          <el-table-column prop="operName" label="操作人" width="100">
          </el-table-column>
          <el-table-column prop="deptName" label="操作部门" width="100">
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" width="180">
          </el-table-column>
          <el-table-column prop="lumen" label="更改lumen量" width="80">
          </el-table-column>
          <el-table-column prop="beforeChangeLumen" label="更改前lumen量" width="100">
          </el-table-column>
          <el-table-column prop="afterChangeLumen" label="更改后lumen量" width="100">
          </el-table-column>
          <el-table-column label="类型" align="center" prop="type">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.lumen_admin_operate_log_type" :value="scope.row.type" />
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { listLumenRecord, getLumenRecord, delLumenRecord, addLumenRecord, updateLumenRecord, reduceLumen, getGiftLumen, setLumenInvalid, getLumenAdminOperateLogList } from "@/api/orders/lumenRecord";
import { getUserByLoginName } from "@/api/admin/user";

export default {
  name: "LumenRecord",
  dicts: ['lumen_record_type', 'lumen_admin_operate_log_type', 'vip_platform_dict', 'invalid_dict'],
  data() {
    return {
      loadingLoginName: false,
      isInvalid:false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      options: [],
      // 总条数
      total: 0,
      // lumen实时记录表格数据
      lumenRecordList: [],
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openLumen: false,
      gift: false,
      openLog: false,
      // 是否无效，存在cancel的情况时间范围
      daterangeCurrentPeriodStart: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        loginName: null,
        customerId: null,
        currentPeriodStart: null,
        type: null,
        vipPlatForm: null,
        originalTransactionId: null,
        transactionId: null,
        invalid: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        detail: [
          { required: true, message: "原因不能为空", trigger: "blur" }
        ],
        loginName: [
          { required: true, message: "用户账号不能为空", trigger: "blur" }
        ],
        lumenQty: [
          { required: true, message: "lumen原有数量不能为空", trigger: "blur" }
        ],
        lumenLeftQty: [
          { required: true, message: "lumen剩余数量不能为空", trigger: "blur" }
        ],
        currentPeriodStartDate: [
          { required: true, message: '获得时间不能为空', trigger: 'blur' }
        ],
        currentPeriodEnDate: [
          { required: true, message: '到期时间不能为空', trigger: 'blur' },
          { validator: this.validateEndTime, trigger: 'blur' }
        ],
        lumen: [
          {
            required: true,
            message: "请输入减少的 lumen 数量",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              const lumenLeftQty = this.form.lumenLeftQty;
              if (!Number.isInteger(+value) || value <= 0) {
                callback(new Error("减少的 lumen 量必须是正整数"));
              } else if (value > lumenLeftQty) {
                callback(new Error(`减少的 lumen 量不能大于剩余数量 ${lumenLeftQty}`));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        giftLumen: [
          {
            required: true,
            message: "请输入减少的 lumen 数量",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              const lumenLeftQty = this.form.lumenLeftQty;
              if (!Number.isInteger(+value) || value <= 0) {
                callback(new Error("减少的 lumen 量必须是正整数"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleEndDateChange(val) {
    if (!val) return;
    // 把日期字符串替换为 07:59:59
    const dateOnly = val.split(' ')[0];
    this.form.currentPeriodEnDate = `${dateOnly} 07:59:59`;
  },
    validateEndTime(rule, value, callback) {
      if (value && this.form.currentPeriodStartDate) {
        // 转换为时间戳进行比较
        const startTime = new Date(this.form.currentPeriodStartDate).getTime();
        const endTime = new Date(value).getTime();
        if (endTime <= startTime) {
          callback(new Error('结束时间必须大于开始时间'));
        } else {
          callback(); // 验证通过
        }
      } else {
        callback(); // 如果没有开始时间，跳过验证
      }
    },
    remoteMethod(query) {
      if (query !== '') {
        getUserByLoginName(query).then(response => {
          this.options = response.data;
        });
      } else {
        this.options = [];
      }
    },
    handleLogList(row) {
      getLumenAdminOperateLogList(row.id).then(response => {
        this.openLog = true;
        this.logList = response.data;
      });
    },
    submitGiftLumen() {
      const giftFrom = {
         id:this.form.id,
         lumen:this.form.giftLumen,
         detail:this.form.detail
      }
      getGiftLumen(giftFrom).then(response => {
        this.openLumen = false;
        this.getList();
      });
    },
    submitReduceLumen() {
      const giftFrom = {
         id:this.form.id,
         lumen:this.form.lumen,
         detail:this.form.detail
      }
      reduceLumen(giftFrom).then(response => {
        this.openLumen = false;
        this.getList();
      });
    },
    handleInvalid(row) {
      this.reset();
      const id = row.id;
      this.form.id =  row.id;
      this.form.loginName = row.loginName;
      this.form.lumenLeftQty = row.lumenLeftQty;
      this.openLumen = true;
      this.isInvalid = true;

    },
    handleReduceLumen(row) {
      this.reset();
      const id = row.id
      getLumenRecord(id).then(response => {
        this.form = response.data;
        this.openLumen = true;
        this.gift = false;
      });
    },
    handleGiftLumen(row) {
      this.reset();
      const id = row.id
      getLumenRecord(id).then(response => {
        this.form = response.data;
        this.openLumen = true;
        this.gift = true;
      });
    },
    cancelLumen() {
      this.openLumen = false;
      this.isInvalid  =false;
      this.gift = false;
    },
    parseTime(time, format) {
      if (!time) return ''; // 如果时间无效，直接返回空字符串

      // 确保 time 是字符串，并且是秒级时间戳，转换为毫秒级
      const timestamp = typeof time === 'string' && time.length === 10 ? parseInt(time) * 1000 : time;

      // 创建 Date 对象并检查其有效性
      const date = new Date(timestamp);

      // 如果日期无效，返回空字符串
      if (isNaN(date.getTime())) {
        return ''; // 或者你可以返回 '无效时间' 等提示
      }

      // 格式化日期为 yyyy-MM-dd HH:mm:ss
      if (format === 'yyyy-MM-dd HH:mm:ss') {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 如果没有匹配的 format，返回默认格式
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      const formatted = parts.map(part => part.value).join('');
      return formatted;
    },
    /** 查询lumen实时记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCurrentPeriodStart && '' != this.daterangeCurrentPeriodStart) {
        this.queryParams.params["beginCurrentPeriodStart"] = this.daterangeCurrentPeriodStart[0];
        this.queryParams.params["endCurrentPeriodStart"] = this.daterangeCurrentPeriodStart[1];
      }
      listLumenRecord(this.queryParams).then(response => {
        this.lumenRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.options = [];

      this.form = {
        id: null,
        userId: null,
        loginName: null,
        customerId: null,
        currentPeriodEnd: null,
        currentPeriodStart: null,
        type: null,
        logicPeriodEnd: null,
        logicPeriodStart: null,
        lumenQty: null,
        lumenLeftQty: null,
        mixed: null,
        invalid: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        lumen: null,
        giftLumen: null,
        detail: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCurrentPeriodStart = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.mixed = 0;
      this.form.invalid = 0;
      this.form.type = 3;
      this.open = true;
      this.title = "赠送lumen";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLumenRecord(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改lumen实时记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLumenRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 根据选中的 loginName 来获取对应的用户信息
            const selectedUser = this.options.find(item => item.loginName === this.form.loginName);
            if (selectedUser) {
              // 自动赋值 userId
              this.form.userId = selectedUser.id;  // 假设选中的对象包含 userId
            }
            this.form.mixed = 0;
            this.form.invalid = 0;
            this.form.type = 3;
            addLumenRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除lumen实时记录编号为"' + ids + '"的数据项？').then(function () {
        return delLumenRecord(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 设置无效按钮操作 */
    submitInvalid() {
      const giftFrom = {
         id:this.form.id,
         detail:this.form.detail,
         lumen:this.form.lumenLeftQty
      }
      setLumenInvalid(giftFrom).then(response => {
        this.openLumen = false;
        this.isInvalid = false;
        this.getList();
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orders/lumenRecord/export', {
        ...this.queryParams
      }, `lumenRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
