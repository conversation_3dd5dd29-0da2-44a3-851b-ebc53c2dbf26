<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="loginName">
        <el-input
          v-model="queryParams.loginName"
          placeholder="请输入用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订阅ID" prop="subscriptionId">
        <el-input
          v-model="queryParams.subscriptionId"
          placeholder="请输入订阅ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预定阅ID" prop="subscriptionScheduleId">
        <el-input
          v-model="queryParams.subscriptionScheduleId"
          placeholder="请输入预定阅ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="customer id" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入customer id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="stripe price_id" prop="priceId">
        <el-input
          v-model="queryParams.priceId"
          placeholder="请输入stripe price_id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预订阅状态" prop="scheduleStatus">
        <el-select v-model="queryParams.scheduleStatus" placeholder="请选择预订阅状态" clearable>
          <el-option
            v-for="dict in dict.type.stripe_schedule_record_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="价格间隔" prop="subInterval">
        <el-select v-model="queryParams.subInterval" placeholder="请选择价格间隔" clearable>
          <el-option
            v-for="dict in dict.type.stripe_schedule_record_interval"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="schedule开始时间">
        <el-date-picker
          v-model="daterangeStartDate"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['orders:scheduleLog:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['orders:scheduleLog:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['orders:scheduleLog:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['orders:scheduleLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scheduleLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="用户账号" align="center" prop="loginName" />
      <el-table-column label="订阅ID" align="center" prop="subscriptionId" />
      <el-table-column label="预定阅ID" align="center" prop="subscriptionScheduleId" />
      <el-table-column label="customer id" align="center" prop="customerId" />
      <el-table-column label="stripe price_id" align="center" prop="priceId" />
      <el-table-column label="预订阅状态" align="center" prop="scheduleStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stripe_schedule_record_status" :value="scope.row.scheduleStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="价格间隔" align="center" prop="subInterval">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stripe_schedule_record_interval" :value="scope.row.subInterval"/>
        </template>
      </el-table-column>
      <el-table-column label="schedule开始时间" align="center" prop="startDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="schedule结束时间" align="center" prop="endDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="取消时间" align="center" prop="cancelledAt" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['orders:scheduleLog:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['orders:scheduleLog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改stripe预定阅记录日志对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="用户账号" prop="loginName">
          <el-input v-model="form.loginName" placeholder="请输入用户账号" />
        </el-form-item>
        <el-form-item label="订阅ID" prop="subscriptionId">
          <el-input v-model="form.subscriptionId" placeholder="请输入订阅ID" />
        </el-form-item>
        <el-form-item label="预定阅ID" prop="subscriptionScheduleId">
          <el-input v-model="form.subscriptionScheduleId" placeholder="请输入预定阅ID" />
        </el-form-item>
        <el-form-item label="customer id" prop="customerId">
          <el-input v-model="form.customerId" placeholder="请输入customer id" />
        </el-form-item>
        <el-form-item label="stripe price_id" prop="priceId">
          <el-input v-model="form.priceId" placeholder="请输入stripe price_id" />
        </el-form-item>
        <el-form-item label="预订阅状态" prop="scheduleStatus">
          <el-select v-model="form.scheduleStatus" placeholder="请选择预订阅状态">
            <el-option
              v-for="dict in dict.type.stripe_schedule_record_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="价格间隔" prop="subInterval">
          <el-select v-model="form.subInterval" placeholder="请选择价格间隔">
            <el-option
              v-for="dict in dict.type.stripe_schedule_record_interval"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="schedule开始时间" prop="startDate">
          <el-date-picker clearable
            v-model="form.startDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择schedule开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="schedule开始时间" prop="endDate">
          <el-input v-model="form.endDate" placeholder="请输入schedule开始时间" />
        </el-form-item>
        <el-form-item label="取消时间" prop="cancelledAt">
          <el-input v-model="form.cancelledAt" placeholder="请输入取消时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listScheduleLog, getScheduleLog, delScheduleLog, addScheduleLog, updateScheduleLog } from "@/api/orders/scheduleLog";

export default {
  name: "ScheduleLog",
  dicts: ['stripe_schedule_record_interval', 'stripe_schedule_record_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // stripe预定阅记录日志表格数据
      scheduleLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 取消时间时间范围
      daterangeStartDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        loginName: null,
        subscriptionId: null,
        subscriptionScheduleId: null,
        customerId: null,
        priceId: null,
        scheduleStatus: null,
        subInterval: null,
        startDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subscriptionScheduleId: [
          { required: true, message: "预定阅ID不能为空", trigger: "blur" }
        ],
        customerId: [
          { required: true, message: "customer id不能为空", trigger: "blur" }
        ],
        priceId: [
          { required: true, message: "stripe price_id不能为空", trigger: "blur" }
        ],
        scheduleStatus: [
          { required: true, message: "预订阅状态不能为空", trigger: "change" }
        ],
        subInterval: [
          { required: true, message: "价格间隔不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime(time, format) {
      if (!time) return ''; // 如果时间无效，直接返回空字符串

      // 确保 time 是字符串，并且是秒级时间戳，转换为毫秒级
      const timestamp = typeof time === 'string' && time.length === 10 ? parseInt(time) * 1000 : time;

      // 创建 Date 对象并检查其有效性
      const date = new Date(timestamp);

      // 如果日期无效，返回空字符串
      if (isNaN(date.getTime())) {
        return ''; // 或者你可以返回 '无效时间' 等提示
      }

      // 格式化日期为 yyyy-MM-dd HH:mm:ss
      if (format === 'yyyy-MM-dd HH:mm:ss') {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 如果没有匹配的 format，返回默认格式
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      const formatted = parts.map(part => part.value).join('');
      return formatted;
    },
    /** 查询stripe预定阅记录日志列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeStartDate && '' != this.daterangeStartDate) {
        this.queryParams.params["beginStartDate"] = this.daterangeStartDate[0];
        this.queryParams.params["endStartDate"] = this.daterangeStartDate[1];
      }
      listScheduleLog(this.queryParams).then(response => {
        this.scheduleLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        loginName: null,
        subscriptionId: null,
        subscriptionScheduleId: null,
        customerId: null,
        priceId: null,
        scheduleStatus: null,
        subInterval: null,
        startDate: null,
        endDate: null,
        cancelledAt: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeStartDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加stripe预定阅记录日志";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getScheduleLog(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改stripe预定阅记录日志";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateScheduleLog(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addScheduleLog(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除stripe预定阅记录日志编号为"' + ids + '"的数据项？').then(function() {
        return delScheduleLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orders/scheduleLog/export', {
        ...this.queryParams
      }, `scheduleLog_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
