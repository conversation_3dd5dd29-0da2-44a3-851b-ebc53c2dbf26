<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="apple 生成的产品ID" prop="appleProductId">
        <el-input
          v-model="queryParams.appleProductId"
          placeholder="请输入apple 生成的产品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="lumen" prop="lumen">
        <el-input
          v-model="queryParams.lumen"
          placeholder="请输入lumen"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="价格间隔" prop="priceInterval">
        <el-select v-model="queryParams.priceInterval" placeholder="请选择价格间隔" clearable>
          <el-option
            v-for="dict in dict.type.stripe_schedule_record_interval"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['orders:appleProduct:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['orders:appleProduct:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['orders:appleProduct:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['orders:appleProduct:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="appleProductList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="apple 生成的产品ID" align="center" prop="appleProductId" />
      <el-table-column label="standard，pro" align="center" prop="planLevel" />
      <el-table-column label="lumen" align="center" prop="lumen" />
      <el-table-column label="plan, one" align="center" prop="productType" />
      <el-table-column label="价格间隔" align="center" prop="priceInterval">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stripe_schedule_record_interval" :value="scope.row.priceInterval"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="standard month 1--&gt;pro year  4" align="center" prop="vipLevel" /> -->
      <el-table-column label="商品描述信息" align="center" prop="mark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['orders:appleProduct:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['orders:appleProduct:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改存储 apple 商品和价格信息的对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="apple 生成的产品ID" prop="appleProductId">
          <el-input v-model="form.appleProductId" placeholder="请输入apple 生成的产品ID" />
        </el-form-item>
        <el-form-item label="standard，pro" prop="planLevel">
          <el-input v-model="form.planLevel" placeholder="请输入standard，pro" />
        </el-form-item>
        <el-form-item label="lumen" prop="lumen">
          <el-input v-model="form.lumen" placeholder="请输入lumen" />
        </el-form-item>
        <el-form-item label="year, month" prop="priceInterval">
          <el-select v-model="form.priceInterval" placeholder="请选择year, month">
            <el-option
              v-for="dict in dict.type.stripe_schedule_record_interval"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="standard month 1--&gt;pro year  4" prop="vipLevel">
          <el-input v-model="form.vipLevel" placeholder="请输入standard month 1--&gt;pro year  4" />
        </el-form-item>
        <el-form-item label="商品描述信息" prop="mark">
          <el-input v-model="form.mark" placeholder="请输入商品描述信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAppleProduct, getAppleProduct, delAppleProduct, addAppleProduct, updateAppleProduct } from "@/api/orders/appleProduct";

export default {
  name: "AppleProduct",
  dicts: ['stripe_schedule_record_interval'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储 apple 商品和价格信息的表格数据
      appleProductList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appleProductId: null,
        lumen: null,
        priceInterval: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        appleProductId: [
          { required: true, message: "apple 生成的产品ID不能为空", trigger: "blur" }
        ],
        productType: [
          { required: true, message: "plan, one不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询存储 apple 商品和价格信息的列表 */
    getList() {
      this.loading = true;
      listAppleProduct(this.queryParams).then(response => {
        this.appleProductList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        appleProductId: null,
        planLevel: null,
        lumen: null,
        productType: null,
        priceInterval: null,
        vipLevel: null,
        mark: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储 apple 商品和价格信息的";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAppleProduct(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储 apple 商品和价格信息的";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAppleProduct(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAppleProduct(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除存储 apple 商品和价格信息的编号为"' + ids + '"的数据项？').then(function() {
        return delAppleProduct(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orders/appleProduct/export', {
        ...this.queryParams
      }, `appleProduct_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
