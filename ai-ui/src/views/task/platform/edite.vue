<template>
    <div class="app-container">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" >
            <el-divider>基本参数</el-divider>

            <el-form-item label="模型id" prop="modelId" >
                <el-input v-model="form.modelId" placeholder="请输入模型id" :disabled="!isAdd"/>
            </el-form-item>
            <el-form-item label="模型名称" prop="modelDisplay">
                <el-input v-model="form.modelDisplay" placeholder="请输入模型名称" />
            </el-form-item>
            <el-form-item label="模型图标" prop="modelAvatar">
                <image-upload v-model="form.modelAvatar" :limit="1"/>
            </el-form-item>
            <el-form-item label="模型描述" prop="modelDesc">
                <el-input v-model="form.modelDesc" placeholder="请输入模型描述" />
            </el-form-item>
            <el-form-item label="模型排序(web)" prop="modelOrder">
                <el-input-number v-model="form.modelOrder" placeholder="请输入模型排序" />
            </el-form-item>
            <el-form-item label="模型排序(ios)" prop="iosOrder">
                <el-input-number v-model="form.iosOrder" placeholder="请输入模型排序" />
            </el-form-item>
            <el-form-item label="模型排序(android)" prop="androidOrder">
                <el-input-number v-model="form.androidOrder" placeholder="请输入模型排序" />
            </el-form-item>
            <el-form-item label="平台" prop="platform">
                <el-checkbox-group v-model="platformArray">
                    <el-checkbox v-for="dict in dict.type.no_ga_platform_dict" :key="dict.value" :label="dict.value">
                        {{ dict.label }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="张数扣点系数" prop="coefficientByNum">
                <el-input-number v-model="form.coefficientByNum" />
            </el-form-item>
            <el-form-item label="像素扣点系数" prop="coefficientByPixel">
                <el-input-number v-model="form.coefficientByPixel" />
            </el-form-item>
            <el-form-item label="模型类型" prop="modelType">
                    <el-input v-model="form.modelType" placeholder="请输入模型类型" />
                </el-form-item>
                
            <el-form-item label="模型详情 defaultConfig 信息" prop="defaultConfig">
                    <el-input v-model="form.defaultConfig" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }"
                        placeholder="请输入内容" />
                </el-form-item>
            <el-form-item label="模型来源" prop="modelOriginType">
                <el-select v-model="form.modelOriginType" placeholder="请选择模型来源"  >
                    <el-option v-for="dict in dict.type.model_origin_type" :key="dict.value" :label="dict.label" 
                        :value="parseInt(dict.value)"></el-option>
                </el-select>
            </el-form-item>
 
            <!-- 只有模型来源为 0 工作流模式才显示 -->
            <template v-if="isWorkflowMode">
                <el-divider>模型参数(算法提供)</el-divider>

                <el-form-item label="模型类型" prop="modelType">
                    <el-input v-model="form.modelType" placeholder="请输入模型类型" />
                </el-form-item>
                <el-form-item label="默认超分降噪指数" prop="defaultHdFixDenoise">
                    <el-input v-model="form.defaultHdFixDenoise" placeholder="请输入默认超分降噪指数" />
                </el-form-item>
                <el-form-item label="模型所支持的风格类型列表" prop="supportStyleList">
                    <el-input v-model="form.supportStyleList" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }"
                        placeholder="请输入内容" />
                </el-form-item>
            </template>
            <el-form-item>
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { getMoleInfo,saveMoleAbout } from "@/api/task/model";
// import { getToken } from "@/utils/auth";

export default {
    name: "EditArtStyle",
    dicts: ['no_ga_platform_dict', 'model_origin_type'],
    data() {
        return {
            // 表单参数
            form: {},
            isViewMode: true, // 是否只能查看
            isAdd: false,
            // 表单校验
            rules: {
                modelId : [
                   { required: true, message: "模型ID不能为空", trigger: "change" }
                ],
                modelDisplay: [
                    { required: true, message: "模型名称不能为空", trigger: "change" }
                ],
                modelOrder: [
                    { required: true, message: "模型排序(web)不能为空", trigger: "change" }
                ],
                androidOrder: [
                    { required: true, message: "模型排序(android)不能为空", trigger: "change" }
                ],
                iosOrder: [
                    { required: true, message: "模型排序(ios)不能为空", trigger: "change" }
                ],platform :[
                    { required: true, message: "模型平台不能为空", trigger: "change" }
                ],coefficientByNum:[
                    { required: true, message: "张数扣点系数不能为空", trigger: "change" }
                ],modelOriginType:[
                    { required: true, message: "模型来源不能为空", trigger: "change" }
                ],coefficientByPixel:[
                    { required: true, message: "像素扣点系数不能为空", trigger: "change" }
                ]
            },
        };
    },
    created() {
        const id = this.$route.params.id;
        if (!id) {
            // 新增模式
            this.reset();
        } else {
            // 编辑模式
            this.getInfo(id);
        }
    },
    computed: {
        platformArray: {
            get() {
                return this.form.platform
                    ? (typeof this.form.platform === 'string' ? this.form.platform.split(',') : this.form.platform)
                    : [];
            },
            set(val) {
                this.form.platform = val.join(',')
            }
        },
    isWorkflowMode() {
      // 只有模型来源为 0（工作流模式）时才返回 true
      return this.form.modelOriginType === 0;
    }
    },
    methods: {
        generateUUID() {
            // 返回一个符合 UUID v4 格式的字符串
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8); // 保证 UUID 版本为 4，variant 为 10xx
                return v.toString(16);
            });
        },
        reset() {
            this.isAdd = true,
                this.isViewMode = false,
                this.form = {
                    id: null,
                    modelId: null,
                    modelDisplay: null,
                    modelType: null,
                    defaultHdFixDenoise: null,
                    modelAvatar: null,
                    modelDesc: null,
                    androidOrder:0,
                    iosOrder:0,
                    modelOrder: 1,
                    platform: null,
                    defaultConfig: null,
                    supportStyleList: null,
                    modelOriginType: 0,
                    createTime: null,
                    updateTime: null,
                    createBy: null,
                    updateBy: null,
                    baseCost:1
                };
            // this.form.modelId = this.generateUUID();
            this.resetForm("form");
        },
        /** 获取详细信息 */
        getInfo(id) {
            getMoleInfo(id).then(response => {
                this.form = response.data;
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    saveMoleAbout(this.form).then(response => {
                        this.$modal.msgSuccess("保存成功");
                        this.$tab.closeOpenPage({ path: "/task/platform" });
                    });
                }
            });
        },
        // 取消按钮
        cancel() {
            this.$tab.closeOpenPage({ path: "/task/platform" });
        },
    }
};
</script>
