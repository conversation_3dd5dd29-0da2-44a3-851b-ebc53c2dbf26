<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="模型id" prop="modelId">
                <el-input v-model="queryParams.modelId" placeholder="请输入模型id" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="模型名称" prop="modelDisplay">
                <el-input v-model="queryParams.modelDisplay" placeholder="请输入模型名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="模型来源类型" prop="modelOriginType">
                <el-select v-model="queryParams.modelOriginType" placeholder="请选择模型来源类型" clearable>
                    <el-option v-for="dict in dict.type.model_origin_type" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['task:model:add']">新增</el-button>
                    <el-button type="primary" plain icon="el-icon-refresh" size="mini" @click="handleRedis"
                   >刷新缓存</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="id" align="center" prop="modelId" />
            <el-table-column label="模型名称" align="center" prop="modelDisplay" />
            <el-table-column label="模型描述" align="center" prop="modelDesc" />
            <el-table-column label="模型排序(web)" align="center" prop="modelOrder" />
            <el-table-column label="模型排序(iod)" align="center" prop="iosOrder" />
            <el-table-column label="模型排序(android)" align="center" prop="androidOrder" />

            <!-- 修改 platforms 列 -->
            <el-table-column label="平台" align="center">
                <template slot-scope="scope">
                    <!-- 将 platforms 数组转为逗号分隔的字符串 -->
                    <span>{{ scope.row.platforms.join(', ') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="模型头像" align="center" prop="modelAvatar" width="100">
                <template slot-scope="scope">
                    <image-preview :src="scope.row.modelAvatar" :width="50" :height="50" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['task:model:edit']">修改</el-button>
                        <el-button size="mini"  v-if="scope.row.modelOriginType > 0" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['task:model:remove']">删除</el-button>
                    <!-- 上架平台按钮：当 platforms 长度 < 3 时显示 -->
                    <!-- <el-button v-if="scope.row.platforms.length < 3" size="mini" type="text" icon="el-icon-edit"
                        @click="handleUp(scope.row)" v-hasPermi="['task:model:edit']">上架平台</el-button> -->
                    <!-- 下架平台按钮：当 platforms 长度 > 0 时显示 -->
                    <!-- <el-button v-if="scope.row.platforms.length > 0" size="mini" type="text" icon="el-icon-delete"
                        @click="handleDown(scope.row)" v-hasPermi="['task:model:edit']">下架平台</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" label-width="80px">
                <!-- 分配规则下拉框 -->
                <el-form-item label="平台" prop="modelGroup">
                    <el-select v-model="upPlatform" placeholder="请分平台">
                        <el-option v-for="item in upPlatformList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="addPlatform">上架平台</el-button>
                <el-button @click="cancelUp">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog :title="title" :visible.sync="downOpen" width="500px" append-to-body>
            <el-form ref="form" label-width="80px">
                <!-- 分配规则下拉框 -->
                <el-form-item label="平台" prop="modelGroup">
                    <el-select v-model="downPlatform" placeholder="请分平台">
                        <el-option v-for="item in downPlatformList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="removePlatform">下架平台</el-button>
                <el-button @click="cancelDown">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog :title="title" :visible.sync="redisOpen" width="500px" append-to-body>
            <el-form :model="resultForm" label-width="80px" :rules="resultRules" ref="resultForm">
                <el-form-item label="平台" prop="redisPlatform">
                    <el-select v-model="resultForm.redisPlatform" placeholder="请选择刷新平台" clearable>
                    <el-option v-for="dict in dict.type.no_ga_platform_dict" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="refreshSubmit">刷新</el-button>
                <el-button @click="redisOpen = false">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listModels, modelsAddPlatform, modelsRemovePlatform, deleteModelAbout,refresh } from "@/api/task/model";

export default {
    name: "Model",
    dicts: ['no_ga_platform_dict', 'model_origin_type'],
    data() {
        return {
            redisPlatform:'',
            redisOpen:false, 
            upId: '',
            upPlatform: null,
            platformList: ['web', 'ios', 'android'],
            upPlatformList: [],
            downOpen: false,
            downPlatform: null,
            downPlatformList: [],
            downId: '',
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 模型列表表格数据
            modelList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                modelName: null,
                modelGroup: null,
                mark: null,
                modelStatus: null,
                modelDisplay: null
            },
            resultForm: {},
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            },
            resultRules:{
                redisPlatform : [
                   { required: true, message: "平台不能为空", trigger: "change" }
                ],
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        refreshSubmit(){
            this.$refs["resultForm"].validate(valid => {
           if (valid) {
                refresh(this.resultForm.redisPlatform).then((response) => {
                this.$modal.msgSuccess("刷新'"+this.resultForm.redisPlatform+"'端 数据成功"  );     
                this.redisOpen = false;
            }
                );
            }
            });
        },
        handleRedis() {
            this.resultForm = {redisPlatform:null},
            this.title =  "刷新缓存",
            this.redisOpen = true;
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const id = row.id;
            this.$modal.confirm('是否确认此模板？').then(function () {
                return deleteModelAbout(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$router.push({ path: '/task/platform/add' });
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.$router.push({ path: '/task/platform/edit/' + row.id });
        },
        /** 查询模型列表列表 */
        getList() {
            this.loading = true;
            listModels(this.queryParams).then(response => {
                this.modelList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                modelName: null,
                modelGroup: null,
                mark: null,
                modelInstance: null,
                modelStatus: null,
                modelId: null,
                modelAvatar: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        handleUp(row) {
            this.upId = row.modelId;
            const list = row.platforms;
            // 生成 upPlatformList，排除掉 row.platforms 中的元素
            this.upPlatformList = this.platformList
                .filter(platform => !list.includes(platform))
                .map(platform => {
                    return { value: platform, label: platform };
                });
            this.open = true;
        },
        cancelUp() {
            this.upPlatformList = [];
            this.open = false;
            this.upId = '';
            this.upPlatform = null;
        },
        addPlatform() {
            modelsAddPlatform(this.upId, this.upPlatform).then((response) => {
                this.upPlatformList = [];
                this.open = false;
                this.upId = '';
                this.upPlatform = null;
                this.getList();
            });
        },
        handleDown(row) {
            this.downId = row.modelId;
            const list = row.platforms;
            // 生成 upPlatformList，排除掉 row.platforms 中的元素
            this.downPlatformList = list
                .map(platform => {
                    return { value: platform, label: platform };
                });
            this.downOpen = true;
        },
        cancelDown() {
            this.downPlatformList = [];
            this.downOpen = false;
            this.downId = '';
            this.downPlatform = null;
        },
        removePlatform() {
            modelsRemovePlatform(this.downId, this.downPlatform).then((response) => {
                this.downPlatformList = [];
                this.downOpen = false;
                this.downId = '';
                this.downPlatform = null;
                this.getList();
            });

        },

    }
};
</script>