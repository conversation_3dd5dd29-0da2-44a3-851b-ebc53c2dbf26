<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期">
        <el-date-picker
          v-model="daterangeDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="平台" prop="platform">
        <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable>
          <el-option
            v-for="dict in dict.type.kpi_platform_dict"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tracking:cost:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tracking:cost:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tracking:cost:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['tracking:cost:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-alert
      title="每日零点15 更新数据 会员总数不汇总赠送会员"
      type="info" show-icon class="mb10" :closable="false" />
    <el-table v-loading="loading" :data="costList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="日期" align="center" prop="date" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生效购买会员总数" align="center" prop="totalVipNum"   />
      <el-table-column label="会员Lumen总量" align="center" prop="totalVipLumenNum"   />
      <el-table-column label="会员Lumen消耗总量" align="center" prop="totalVipCostLumenNum"   />
      <el-table-column label="生效购买lumen人数" align="center" prop="totalRechargeNum"  />
      <el-table-column label="购买Lumen总量" align="center" prop="totalRechargeLumenNum"  />
      <el-table-column label="会员购买消耗总量" align="center" prop="totalRechargeCostLumenNum"   />
      <el-table-column label="生效赠送lumen人数" align="center" prop="totalGiftNum"   />
      <el-table-column label="赠送Lumen总量" align="center" prop="totalGiftLumenNum"  />
      <el-table-column label="赠送Lumen消耗总量" align="center" prop="totalGiftCostLumenNum" />
      <el-table-column label="平台" align="center" prop="platform">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kpi_platform_dict" :value="scope.row.platform"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tracking:cost:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tracking:cost:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改kpi 数据追踪-lumen消耗对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="date">
          <el-date-picker clearable
            v-model="form.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生效购买会员总数" prop="totalVipNum">
          <el-input v-model="form.totalVipNum" placeholder="请输入生效会员总数" />
        </el-form-item>
        <el-form-item label="会员Lumen总量" prop="totalVipLumenNum">
          <el-input v-model="form.totalVipLumenNum" placeholder="请输入会员Lumen总量" />
        </el-form-item>
        <el-form-item label="会员Lumen消耗总量" prop="totalVipCostLumenNum">
          <el-input v-model="form.totalVipCostLumenNum" placeholder="请输入会员Lumen消耗总量" />
        </el-form-item>
        <el-form-item label="生效购买lumen人数" prop="totalRechargeNum">
          <el-input v-model="form.totalRechargeNum" placeholder="请输入生效购买lumen人数" />
        </el-form-item>
        <el-form-item label="购买Lumen总量" prop="totalRechargeLumenNum">
          <el-input v-model="form.totalRechargeLumenNum" placeholder="请输入购买Lumen总量" />
        </el-form-item>
        <el-form-item label="会员购买消耗总量" prop="totalRechargeCostLumenNum">
          <el-input v-model="form.totalRechargeCostLumenNum" placeholder="请输入会员购买消耗总量" />
        </el-form-item>
        <el-form-item label="生效赠送lumen人数" prop="totalGiftNum">
          <el-input v-model="form.totalGiftNum" placeholder="请输入生效赠送lumen人数" />
        </el-form-item>
        <el-form-item label="赠送Lumen总量" prop="totalGiftLumenNum">
          <el-input v-model="form.totalGiftLumenNum" placeholder="请输入赠送Lumen总量" />
        </el-form-item>
        <el-form-item label="赠送Lumen消耗总量" prop="totalGiftCostLumenNum">
          <el-input v-model="form.totalGiftCostLumenNum" placeholder="请输入赠送Lumen消耗总量" />
        </el-form-item>
        <el-form-item label="平台" prop="platform">
          <el-select v-model="form.platform" placeholder="请选择平台">
            <el-option
              v-for="dict in dict.type.kpi_platform_dict"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCost, getCost, delCost, addCost, updateCost } from "@/api/tracking/cost";

export default {
  name: "Cost",
  dicts: ['kpi_platform_dict'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // kpi 数据追踪-lumen消耗表格数据
      costList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 平台(web|ios|android|gift)时间范围
      daterangeDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        date: null,
        platform: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        date: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        totalVipNum: [
          { required: true, message: "生效会员总数不能为空", trigger: "blur" }
        ],
        totalRechargeNum: [
          { required: true, message: "生效购买lumen人数不能为空", trigger: "blur" }
        ],
        totalGiftNum: [
          { required: true, message: "生效赠送lumen人数不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    emptyFormatter(row, column, cellValue) {
      return cellValue === null || cellValue === undefined || cellValue === 0 || cellValue === '0' ? '/' : cellValue;
    },
    /** 查询kpi 数据追踪-lumen消耗列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDate && '' != this.daterangeDate) {
        this.queryParams.params["beginDate"] = this.daterangeDate[0];
        this.queryParams.params["endDate"] = this.daterangeDate[1];
      }
      listCost(this.queryParams).then(response => {
        this.costList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        date: null,
        totalVipNum: null,
        totalVipLumenNum: null,
        totalVipCostLumenNum: null,
        totalRechargeNum: null,
        totalRechargeLumenNum: null,
        totalRechargeCostLumenNum: null,
        totalGiftNum: null,
        totalGiftLumenNum: null,
        totalGiftCostLumenNum: null,
        platform: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加kpi 数据追踪-lumen消耗";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCost(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改kpi 数据追踪-lumen消耗";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCost(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCost(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除kpi 数据追踪-lumen消耗编号为"' + ids + '"的数据项？').then(function() {
        return delCost(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tracking/cost/export', {
        ...this.queryParams
      }, `cost_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
