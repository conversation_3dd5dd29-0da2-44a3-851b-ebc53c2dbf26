<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="活动标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入活动标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="活动投稿开始时间">
        <el-date-picker v-model="daterangeBeginTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="活动状态标签" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择活动状态标签" clearable>
          <el-option v-for="dict in dict.type.comm_activity_status_dict" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="活动是否发布" prop="publish">
        <el-select v-model="queryParams.publish" placeholder="请选择活动是否发布" clearable>
          <el-option v-for="dict in dict.type.publish_dict" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否发放奖励" prop="reward">
        <el-select v-model="queryParams.reward" placeholder="请选择是否发放奖励" clearable>
          <el-option v-for="dict in dict.type.comm_activity_reward_dict" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['operation:com-activity:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['operation:com-activity:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['operation:com-activity:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['operation:com-activity:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="comActivityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="活动标题" align="center" prop="title">
        <template slot-scope="scope">
          {{ formatText(scope.row.title) }}
        </template>
      </el-table-column>
      <el-table-column label="活动投稿开始时间" align="center" prop="beginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动投稿结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.postEndTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动标签" align="center" prop="tags" />
      <el-table-column label="活动图片">
        <template slot-scope="scope">
          <img :src="scope.row.cover" alt="活动图片" style="width: 100%; height: 100%; object-fit:contain;" />
        </template></el-table-column>
      <el-table-column label="活动详情" align="center" prop="detail">
        <template slot-scope="scope">
          {{ formatText(scope.row.detail) }}
        </template>
      </el-table-column>
      <el-table-column label="更多规则详情" align="center" prop="ruleDetail">
        <template slot-scope="scope">
          {{ formatText(scope.row.ruleDetail) }}
        </template>
      </el-table-column>
      <el-table-column label="单用户投稿上限" align="center" prop="maxSubmissions" />
      <el-table-column label="活动状态标签" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.comm_activity_status_dict" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="活动是否发布" align="center" prop="publish">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.publish_dict" :value="scope.row.publish" />
        </template>
      </el-table-column>
      <el-table-column label="是否发放奖励" align="center" prop="reward">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.comm_activity_reward_dict" :value="scope.row.reward" />
        </template>
      </el-table-column>
      <el-table-column label="活动图片数量" align="center" prop="imageNum" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 3" size="mini" type="text" icon="el-icon-view"
            @click="handleView(scope.row)" v-hasPermi="['operation:com-activity:query']">查看详情</el-button>
          <el-button v-if="scope.row.status === 2" size="mini" type="text" icon="el-icon-star-off"
            @click="sendPrize(scope.row)" v-hasPermi="['operation:com-activity:edit']">发送奖励</el-button>
          <el-button v-if="scope.row.status === 2" size="mini" type="text" icon="el-icon-news"
            @click="openImgList(scope.row)" v-hasPermi="['operation:com-activity:edit']">评选活动</el-button>
          <el-button v-if="!scope.row.publish" size="mini" type="text" icon="el-icon-news"
            @click="handlePublish(scope.row)" v-hasPermi="['operation:com-activity:edit']">发布活动</el-button>
          <el-button v-if="scope.row.status < 3" size="mini" type="text" icon="el-icon-edit"
            @click="openRewardSetting(scope.row)" v-hasPermi="['operation:com-activity:edit']">设置奖励</el-button>
          <el-button v-if="!scope.row.publish" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)" v-hasPermi="['operation:com-activity:edit']">修改</el-button>
          <el-button v-if="!scope.row.publish" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)" v-hasPermi="['operation:com-activity:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改社区活动信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="活动标题" prop="title" maxlength="50" show-word-limit>
          <el-input v-model="form.title" placeholder="请输入活动标题" :disabled="isViewMode" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="活动投稿时间" prop="activityTime">
          <el-date-picker clearable v-model="form.activityTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="投稿开始时间" end-placeholder="投稿结束时间" placeholder="请选择活动投稿开始时间" :disabled="isViewMode">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动标签" prop="tags">
          <div class="fixed-tag-container">
            <div class="fixed-tag-item" v-for="(tag, index) in allTags" :key="tag.type"
              :class="{ active: selectedTags.includes(index) }" @click="toggleTagSelection(index)">
              <!-- 选中状态的勾选图标 -->
              <div class="check-icon" v-if="selectedTags.includes(index)">
                <i class="el-icon-check"></i>
              </div>
              <svg-icon :icon-class="tag.type" class="tag-icon" />
              <el-input v-model="tag.title" size="mini" class="tag-title-input" placeholder="请输入文字" :disabled="isViewMode" maxlength="30" show-word-limit
                 />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="活动详情" prop="detail">
          <el-input v-model="form.detail" type="textarea" placeholder="请输入内容" :disabled="isViewMode" maxlength="1000"
            show-word-limit :autosize="{ minRows: 8 }" />
        </el-form-item>
        <el-form-item label="活动图片" prop="cover">

          <el-upload v-if="!isViewMode" class="upload-demo" drag :action="uploadUrl" :headers="headers" multiple
            :on-success="handleUploadSuccess" :before-upload="beforeUpload" :show-file-list="false"
            accept=".jpg,.jpeg,.png">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              只能上传 jpg/png 文件
            </div>
          </el-upload>

          <el-image style="width: 200px; height: 200px" :src="form.cover"></el-image>
        </el-form-item>
        <el-form-item label="单用户投稿上限" prop="maxSubmissions">
          <el-input v-model="form.maxSubmissions" placeholder="请输入单用户投稿上限" :disabled="isViewMode" />
        </el-form-item>
        <el-form-item v-if="!isViewMode" label="更多规则详情" prop="ruleDetail">
          <template>
            <div style="border: 1px solid #ccc;">
              <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                :mode="mode" />
              <Editor style="height: 500px; overflow-y: hidden;" v-model="form.ruleDetail" :defaultConfig="editorConfig"
                :mode="mode" @onCreated="onCreated" :disabled="isViewMode" />
            </div>
          </template>
        </el-form-item>
      </el-form>
      <el-table v-if="isViewMode" :data="rewardList">
        <el-table-column label="用户名" prop="loginName" />
        <el-table-column label="作品" prop="fileUrl" width="150">
          <template slot-scope="scope">
            <img :src="scope.row.fileUrl" alt="作品图片" style="width: 100%; height: 100%; object-fit:contain;"
              @click="viewImage(scope.row.highThumbnailUrl || scope.row.fileUrl)" class="work-image" />
          </template>
        </el-table-column>
        <el-table-column label="点赞数" prop="likesNum" />
        <el-table-column label="评论数" prop="commentNum" />
        <el-table-column label="获得奖项" prop="levelName">
          <template slot-scope="scope">
            {{ scope.row.levelName }}
          </template>
        </el-table-column>
        <el-table-column label="奖励内容" prop="commActivityGiftContentDtoList" width="300">
          <template slot-scope="scope">
            <div v-for="(reward, index) in scope.row.commActivityGiftContentDtoList" :key="index">
              <div v-if="reward.rewardNum">奖品数量：{{ reward.rewardNum }}</div>
              <div>奖励类型：{{ reward.type }}</div>
              <div v-if="reward.planLevel">会员类型：{{ reward.planLevel }}</div>
              <div v-if="reward.priceInterval">时间间隔{{ reward.priceInterval }}</div>
              <div>奖励开始时间：{{ reward.startTime }}</div>
              <div>奖励结束时间：{{ reward.endTime }}</div>
              <div v-if="reward.mark">奖励说明：{{ reward.mark }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="!isViewMode" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="设置奖励" :visible.sync="dialogVisible" width="80%">
      <el-form label-width="120px" :rules="rewardSettingRules">
        <!-- 活动标题 -->
        <el-form-item label="活动标题">
          <el-input v-model="rewardSetting.activityTitle" placeholder="请输入活动标题" :disabled="true" />
        </el-form-item>
        <!-- 奖项设置 -->
        <div v-for="(prize, index) in rewardSetting.commActivityPrizeSettingDtoList" :key="index" class="reward-block">
          <el-card class="box-card" shadow="never">
            <div class="flex items-center">
              <!-- 奖项级别 -->
              <el-select v-model="prize.prizeLevel" style="width: 220px" placeholder="选择奖项" prop="prizeLevel">
                <el-option v-for="item in levels" :key="item.value" :label="item.label" :value="item.value">
                  <span class="option-item">
                    <img :src="item.icon" alt="icon" class="option-icon" />
                    <span class="option-label"> {{ item.label }}</span>
                  </span>
                </el-option>
              </el-select>
              <!-- 获奖人数 -->
               <span   tyle="margin-left: 10px; width: 80px"> 奖项人数： </span>
              <el-input v-model="prize.winnersNum" :min="1" style="margin-left: 10px; width: 80px" />
              <!-- 添加奖励内容 -->
              <el-button type="primary" icon="el-icon-plus" @click="addGiftContent(prize)" style="margin-left: 10px"
                circle />
              <!-- 删除奖项 -->
              <el-button v-if="rewardSetting.commActivityPrizeSettingDtoList.length > 1" type="danger"
                icon="el-icon-delete" @click="removePrize(index)" circle />
            </div>

            <!-- 奖励内容 -->
            <div v-for="(gift, giftIndex) in prize.commActivityGiftContentDtoList" :key="giftIndex">
              <el-card style="margin-top: 20px">
                <el-select v-model="gift.type" style="margin-left: 10px; width: 120px" placeholder="选择奖励类型"
                  @change="handleGiftTypeChange(prize)">
                  <el-option label="Lumen" value="lumens" />
                  <el-option label="VIP" value="vip" />
                </el-select>

                <!-- 奖励数量 -->
                <span   tyle="margin-left: 10px; width: 80px" v-if="gift.type === 'lumens'"> 奖励数量： </span>
                <el-input v-model="gift.rewardNum" :min="0" style="width: 100px" v-if="gift.type === 'lumens'" />

                <!-- 会员等级选择 -->
                <el-select v-if="gift.type === 'vip'" v-model="gift.planLevel" style="margin-left: 10px; width: 120px"
                  placeholder="选择会员等级">
                  <el-option label="pro" value="pro" />
                  <el-option label="standard" value="standard" />
                </el-select>

                <!-- 时间间隔选择 -->
                <el-select v-if="gift.type === 'vip'" v-model="gift.priceInterval"
                  style="margin-left: 10px; width: 100px" placeholder="选择会员间隔时间">
                  <el-option label="year" value="year" />
                  <el-option label="month" value="month" />
                </el-select>

                <!-- 时间选择 -->
                <el-date-picker v-model="gift.startTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="开始时间" style="margin-left: 10px" />
                <el-date-picker v-model="gift.endTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="结束时间" style="margin-left: 10px"    @change="handleEndDateChange"/>

                <!-- 奖项描述 -->
                <!-- <el-input
                v-model="gift.mark"
                placeholder="奖项描述"
                style="margin-left: 10px; width: 200px"
              /> -->
                <!-- 删除奖励内容 -->
                <el-button type="danger" icon="el-icon-delete" @click="removeGiftContent(prize, giftIndex)"
                  style="margin-left: 10px" circle /> </el-card>
            </div>
          </el-card>
        </div>

        <!-- 添加奖项 -->
        <el-button type="success" icon="el-icon-plus" plain @click="addPrize">添加奖项</el-button>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitRewardSetting">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="activityTitile" :visible.sync="dialogVisibleSelection" width="40%">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openRewardSelect"
            :disabled="disableClick" v-hasPermi="['operation:com-activity:query']">评选作品</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-minus" size="mini" @click="cancelPrizeLevel"
            :disabled="cancelDisableClick" v-hasPermi="['operation:com-activity:query']">取消奖励</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="postsImgList"></right-toolbar>
      </el-row>
      <!-- 顶部流程 Tabs -->
      <!-- <div class="step-flow">
    <div v-for="(step, index) in steps" :key="step.prizeLevel" :class="['step-item', { active: currentStep === index }]">
      <div class="step-icon">
        <img :src="step.icon" alt="奖项图标" style="width: 24px; height: 24px; margin-right: 8px;" />
        <span>{{ step.levelName }}</span>
      </div>
      <div class="step-info">
        <span class="winners-num">{{ step.winnersNum }} 个</span>
        <span class="used-num">{{ step.usedNum }} 已评选</span>
      </div>
    </div>
  </div> -->

      <!-- 作品展示列表 -->
      <el-table :data="postsImgList" stripe style="width: 100%" @selection-change="handlePostsSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="fileId" label="社区图片id" />
        <el-table-column prop="userName" label="用户名" />
        <el-table-column label="作品" width="150">
          <template slot-scope="scope">

            <!-- 判断 highMiniUrl 是否存在，若不存在则使用 thumbnailUrl -->
            <img :src="scope.row.highMiniUrl || scope.row.thumbnailUrl" alt="作品图"
              style="width: 100%; height: 100%; object-fit:contain;"
              @click="viewImage(scope.row.highThumbnailUrl || scope.row.fileUrl)" />
          </template>
        </el-table-column>
        <el-table-column prop="fileLikeNums" label="作品点赞" />
        <el-table-column prop="fileCommentNums" label="评论" />
        <el-table-column prop="levelName" label="奖项" />
        <el-table-column label="获得奖项" width="60">
          <template slot-scope="scope">
            <div v-if="scope.row.icon">
              <!-- 判断 highMiniUrl 是否存在，若不存在则使用 thumbnailUrl -->
              <img :src="scope.row.icon" alt="获得奖项" style="width: 100%; height: 100%; object-fit: contain;" />
            </div>
            <div v-else>
              <span>暂无获奖</span>
            </div>
          </template>
        </el-table-column>

      </el-table>
      <pagination layout="sizes, prev, next" :total="10000000" :page.sync="postsQueryParams.pageNum"
        :page-sizes="[5,10, 20, 30, 50]" :limit.sync="postsQueryParams.pageSize" @prev="prevClick" @next="nextClick"
        @pagination="getImgList()" />
    </el-dialog>
    <el-dialog title="评选作品" :visible.sync="dialogVisibleRewardSelection" width="1000px" append-to-body>
      <el-form ref="setPrizeFrom" :model="setPrizeFrom" label-width="80px">
        <el-select v-model="activityRewardSelectionReq.prizeLevel" style="width: 220px" placeholder="选择奖项"
          prop="prizeLevel">
          <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value">
            <span class="option-item">
              <img :src="item.icon" alt="icon" class="option-icon" />
              <span class="option-label">{{ item.label }}</span>
            </span>
          </el-option>
        </el-select>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setPrizeLevel">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>'

    </el-dialog>

    <el-dialog title="查看作品" :visible.sync="dialogVisibleImg" width="50%">
      <img :src="dialogImageUrl" alt="放大图" style="width: 100%; height: auto;" />
    </el-dialog>
  </div>
</template>

<script>
import {
  listComActivity, getComActivity, delComActivity, addComActivity, updateComActivity, getRewardSetting, getActivityPrizeTree,
  activityRewardSettings, toPublic, imgList, getActivityPrizeList, setActivityPrizeLevel, cancelActivityPrizeLevel, sendActivityPrize, geRewardSelection
} from "@/api/operation/com-activity";
import { getToken } from "@/utils/auth";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
const uploadUrl = process.env.VUE_APP_BASE_API + '/admin/album/upload'
const headers = {
  Authorization: "Bearer " + getToken(),
}
export default {
  name: "ComActivity",
  components: { Editor, Toolbar },
  dicts: ['comm_activity_status_dict', 'comm_activity_reward_dict', 'publish_dict'],
  data() {
    return {
      rewardList: [
      ],
      isViewMode: false, // 控制是否为查看模式
      dialogImageUrl: '',
      fileIds: [],
      selectOptions: [],
      setPrizeFrom: {

      },
      selectionSingle: true,
      // disableClick: true, 
      activityRewardSelectionReq: {
        activityId: null,
        prizeLevel: null,
        activityRewardSelectionDtoList: [],
      },
      dialogVisibleRewardSelection: false,
      dialogVisibleImg: false,
      currentStep: 1, // 0-4 可控
      steps: [
        { levelName: '一等奖', winnersNum: 3, usedNum: 2, 'icon': 'https://uploads.piclumen.com/manage/20250417/21/10ff34d9-3c3e-4195-9628-c9def157c79b.svg' }
      ],
      postsQueryParams: {
        pageNum: 0,
        activityId: null,
        pageSize: 10,
        markFileId: null,
        isNext: null
      },
      activityId: null,
      activityTitile: '',
      postsImgList: [
      ],
      dialogVisibleSelection: false,
      levels: [],
      dialogVisible: false,
      rewardSetting: {
        activityId: null,
        activityName: '',
        commActivityPrizeSettingDtoList: []

      },
      editor: null,
      html: '<p>hello</p>',
      toolbarConfig: {
      },
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {
            fieldName: "file",
            server: process.env.VUE_APP_BASE_API + '/admin/album/upload', // 上传图片的服务器地址
            headers: headers,
            customInsert(res, insertFn) {
              insertFn(res.url, res.originalFilename, res.url);
            },
          }

        }
      },
      mode: 'default', // or 'simple'
      selectedTags: [], // 确保 selectedTags 初始化为空数组
      allTags: [{ type: 'lumens', title: '' },
      { type: 'gifts', title: '' },
      { type: 'subscribe', title: '' },
      { type: 'money', title: '' }],
      selectedTagIndex: 0, // 默认选中第一个
      activityTime: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 社区活动信息表格数据
      comActivityList: [],
      testList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 活动图片数量时间范围
      daterangeBeginTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        beginTime: null,
        status: null,
        publish: null,
        reward: null,
      },
      uploadUrl: process.env.VUE_APP_BASE_API + '/admin/album/upload',
      headers: {
        Authorization: "Bearer " + getToken(),
      },

      // 表单参数
      form: {
      },
      // 设置奖励表单验证
      rewardSettingRules: {
        activityTitle: [
          { required: true, message: '活动标题不能为空', trigger: 'blur' }
        ],
        commActivityPrizeSettingDtoList: [
          {
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('奖项设置不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
      // 表单校验
      rules: {
        activityTime: [
          { required: true, message: "活动投稿时间不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!value || value.length !== 2) {
                callback(new Error("请正确选择活动投稿时间范围"));
                return;
              }
              const [start, end] = value;
              const now = new Date();
              const endTime = new Date(end);

              if (now > endTime) {
                callback(new Error("当前时间不在投稿时间范围内"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ], title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        detail: [
          { required: true, message: "详情不能为空", trigger: "blur" }
        ],
        maxSubmissions: [
          { required: true, message: "单用户投稿上限不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('等级不能为空'))
              } else if (!/^\d+$/.test(value)) {
                callback(new Error('等级必须是数字'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        ruleDetail: [,
          { required: true, message: "更多规则详情不能为空", trigger: "blur" }
        ], cover: [
          { required: true, message: "活动图片不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.initPrizeTree();
  },
  computed: {

    disableClick() {
      const list = (this.activityRewardSelectionReq?.activityRewardSelectionDtoList || [])
      if (list.length === 0) {
        return true
      }
      return list.some(({ isPize }) => isPize)
    },
    cancelDisableClick() {
      const list = (this.activityRewardSelectionReq?.activityRewardSelectionDtoList || [])
      if (list.length === 0) {
        return true
      }
      return !list.every(({ isPize }) => isPize)
    }
  },
  methods: {
    handleEndDateChange(val) {
    if (!val) return;

    // 如果 val 是字符串，需要转为 Date 类型
    const date = new Date(val);

    // 设置为北京时间 07:59:59（UTC +8）
    date.setHours(7);
    date.setMinutes(59);
    date.setSeconds(59);

    // 格式化为字符串 yyyy-MM-dd HH:mm:ss
    const pad = (n) => (n < 10 ? '0' + n : n);
    const formatted = `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;

    // 遍历所有奖项及其奖励内容，统一设置 gift.endTime（因为你用的是 v-for 嵌套结构）
    this.rewardSetting.commActivityPrizeSettingDtoList.forEach(prize => {
      prize.commActivityGiftContentDtoList.forEach(gift => {
        // 如果原始时间是一样的，说明是这个被改的项
        if (gift.endTime === val) {
          gift.endTime = formatted;
        }
      });
    });
  },
    handleView(row) {
      const id = row.id;
      this.isViewMode = true;
      getComActivity(id).then(response => {
        this.form = response.data;
        // 将字符串格式的 tags 转换为数组
        let tagList = [];
        try {
          tagList = JSON.parse(this.form.tags || '[]');
        } catch (e) {
          console.error("标签解析失败", e);
        }
        // 回显标签：从 form.tags 中取出对象，找出其在 allTags 中的索引
        if (Array.isArray(tagList)) {
          this.selectedTags = tagList.map(tagObj => {
            const index = this.allTags.findIndex(item => item.type === tagObj.type);
            if (index !== -1) {
              // 回显 title 到 allTags
              this.$set(this.allTags[index], 'title', tagObj.title || '');
            }
            return index;
          }).filter(index => index !== -1); // 过滤无效索引
        } else {
          this.selectedTags = [];
        }
        // 回显时间范围
        if (this.form.beginTime && this.form.postEndTime) {
          this.form.activityTime = [this.form.beginTime, this.form.postEndTime];
        }

        this.open = true;
        this.title = "查看社区活动信息";
      });
      geRewardSelection(id).then(response => {
        this.rewardList = response.data
      });
    },
    sendPrize(row) {
      const id = row.id;
      this.$modal.confirm('是否确认发送编号为"' + id + '"的社区活动奖励？').then(function () {
        return sendActivityPrize(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("发送成功");
      }).catch(() => { });
    },
    cancelPrizeLevel() {
      const that = this
      this.$modal.confirm('是否确认取消此作品的奖励？').then(async function () {
        await cancelActivityPrizeLevel(that.fileIds);
      }).then(() => {
        this.getImgList();
        this.$modal.msgSuccess("取消成功");
      }).catch(() => { });
    },
    setPrizeLevel() {
      console.log(this.activityRewardSelectionReq);
      setActivityPrizeLevel(this.activityRewardSelectionReq).then(response => {
        this.dialogVisibleRewardSelection = false;
        this.$modal.msgSuccess("设置成功");
        this.getImgList();
      })
    },
    openRewardSelect() {
      this.dialogVisibleRewardSelection = true;
      getActivityPrizeList(this.activityRewardSelectionReq.activityId).then(response => {
        // 假设接口返回的数据是一个数组
        const data = response.data;

        // 过滤掉 winnersNum 和 usedNum 相等的数据
        const filteredData = data.filter(item => item.winnersNum > item.usedNum);

        // 格式化为下拉选择框需要的数据
        const selectOptions = filteredData.map(item => ({
          label: item.levelName,  // 显示在下拉框中的名称
          value: item.prizeLevel, // 用于区分选项的值
          icon: item.icon         // 每个选项对应的图标
        }));

        // 可以继续使用 selectOptions 设置下拉框的数据
        this.selectOptions = selectOptions;
      });
    },
    openImgList(row) {
      this.postsQueryParams.activityId = row.id;
      this.postsQueryParams.pageSize = 10,
      this.postsQueryParams.markFileId = null,
      this.postsQueryParams.lastFileLikeNums = null,
      this.postsImgList = [],
      this.activityRewardSelectionReq = {
          activityId: row.id,
          prizeLevel: null,
          activityRewardSelectionDtoList: [],
        }
      this.getImgList();
      this.dialogVisibleSelection = true;
    },
    getImgList() {
      imgList(this.postsQueryParams).then(response => {
        if (response.rows.length === 0) {
          this.$modal.msgSuccess("未查询到数据 返回上个列表页面");
          return;
        }
        this.postsImgList = response.rows;
      });
    },
    nextClick() {
      this.postsQueryParams.markFileId = this.postsImgList[this.postsImgList.length - 1].fileId;
      this.postsQueryParams.lastFileLikeNums =   this.postsImgList[this.postsImgList.length - 1].fileLikeNums;
      this.postsQueryParams.isNext = true;
    },
    prevClick() {
      this.postsQueryParams.markFileId = this.postsImgList[0].fileId
      this.postsQueryParams.lastFileLikeNums =   this.postsImgList[0].fileLikeNums;
      this.postsQueryParams.isNext = false;
    },
    // 点击图片时，显示大图
    viewImage(imageUrl) {
      if (imageUrl) {
        this.dialogImageUrl = imageUrl;
        this.dialogVisibleImg = true;  // 显示对话框
      } else {
        this.$message.warning('该图片没有大图链接');
      }
    },
    /** 发布按钮操作 */
    handlePublish(row) {
      const id = row.id;
      this.$modal.confirm('是否确认发布编号为"' + id + '"的社区活动？').then(function () {
        return toPublic(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("发布成功");
      }).catch(() => { });
    },
    // 提交前检查是否有重复的奖项或奖励内容
    submitRewardSetting() {
      // 检查是否有重复奖项（根据 prizeLevel）
      const prizeLevels = this.rewardSetting.commActivityPrizeSettingDtoList.map(prize => prize.prizeLevel);
      const duplicatePrize = this.findDuplicate(prizeLevels);
      if (duplicatePrize) {
        const level = this.levels.find(item => item.value === duplicatePrize);
        this.$message.error(`奖项 "${level.label}" 重复，请修改。`);
        return;
      }

      // 检查是否有重复奖励内容（根据 type 和其他字段）
      for (const prize of this.rewardSetting.commActivityPrizeSettingDtoList) {
        const giftTypes = prize.commActivityGiftContentDtoList.map(gift => `${gift.type}`);
        const duplicateGift = this.findDuplicate(giftTypes);
        if (duplicateGift) {
          this.$message.error(`奖项内容 "${duplicateGift}" 重复，请修改。`);
          return;
        }

        // 判空验证：确保所有内容都不能为空
        if (!prize.prizeLevel || prize.winnersNum === undefined || prize.winnersNum === null || prize.winnersNum === '') {
          this.$message.error("奖项信息不能为空！");
          return;
        }

        for (const gift of prize.commActivityGiftContentDtoList) {
          if (!gift.type || gift.rewardNum === undefined || gift.rewardNum === null || gift.rewardNum === '') {
            this.$message.error("奖励内容不能为空！");
            return;
          }

          if (gift.type === 'vip' && (!gift.planLevel || !gift.priceInterval)) {
            this.$message.error("VIP奖励的会员等级和时间间隔不能为空！");
            return;
          }

          if (!gift.startTime || !gift.endTime) {
            this.$message.error("奖励的开始时间和结束时间不能为空！");
            return;
          }

          // 检查结束时间必须大于开始时间
          if (new Date(gift.endTime) <= new Date(gift.startTime)) {
            this.$message.error("结束时间必须大于开始时间！");
            return;
          }

          // 检查结束时间不能小于当前时间
          if (new Date(gift.endTime) <= new Date()) {
            this.$message.error("结束时间不能小于当前时间！");
            return;
          }
        }
      }

      // 如果没有重复项且验证通过，执行提交逻辑
      console.log('提交表单:', this.rewardSetting);
      activityRewardSettings(this.rewardSetting).then(response => {
        this.dialogVisible = false;
        this.$message.success('设置成功');
      });
    },

    // 用于检查数组中是否有重复项
    findDuplicate(arr) {
      const seen = new Set();
      for (const item of arr) {
        if (seen.has(item)) {
          return item; // 返回重复的项
        }
        seen.add(item);
      }
      return null; // 没有重复
    },
    // 删除奖项
    removePrize(index) {
      this.$confirm('确定删除该奖项吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.rewardSetting.commActivityPrizeSettingDtoList.splice(index, 1);
      });
    },

    // 删除奖励内容
    removeGiftContent(prize, giftIndex) {
      this.$confirm('确定删除该奖励内容吗？', '提示', {
        type: 'warning'
      }).then(() => {
        prize.commActivityGiftContentDtoList.splice(giftIndex, 1);
      });
    },
    initPrizeTree() {
      getActivityPrizeTree().then(response => {
        this.levels = response.data;
      });
    },
    openRewardSetting(row) {
      getRewardSetting(row.id).then(response => {
        this.rewardSetting = response.data;
        this.dialogVisible = true;
      });

    },
    handleGiftTypeChange(prize) {
      // 根据选择的奖励类型更新对应的默认数据
      if (prize.type === "vip") {
        prize.commActivityGiftContentDtoList.forEach(gift => {
          gift.planLevel = "Pro";
          gift.priceInterval = "year";
          gift.startTime = null;
          gift.endTime = null;
          gift.rewardNum = null; // 移除数量
        });
      } else if (prize.type === "lumens") {
        prize.commActivityGiftContentDtoList.forEach(gift => {
          gift.rewardNum = 100;
          gift.startTime = "2025-04-17 19:25:03";
          gift.endTime = "2025-06-17 19:25:03";
        });
      }
    },
    addGiftContent(prize) {
      // 为奖项添加新的奖励内容
      prize.commActivityGiftContentDtoList.push({
        rewardNum: 100,
        type: 'lumens',
        startTime: '',
        endTime: '',
        mark: ''
      });
    },
    addPrize() {
      // 添加新的奖项
      this.rewardSetting.commActivityPrizeSettingDtoList.push({
        prizeLevel: '',
        winnersNum: 1,
        commActivityGiftContentDtoList: []
      });
    },
    parseTime(time, format) {
      if (!time) return '';

      let date;

      // 判断是否是秒级时间戳（长度为 10 的字符串或数字）
      if ((typeof time === 'string' && /^\d{10}$/.test(time)) || (typeof time === 'number' && time.toString().length === 10)) {
        date = new Date(Number(time) * 1000); // 转为毫秒
      } else {
        date = new Date(time); // 直接当作普通时间处理
      }

      // 如果日期无效，返回空字符串
      if (isNaN(date.getTime())) {
        return '';
      }

      // 格式为 'yyyy-MM-dd HH:mm:ss' 时手动格式化
      if (format === 'yyyy-MM-dd HH:mm:ss') {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 否则使用默认格式化器
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      return parts.map(part => part.value).join('');
    },
    formatText(text) {
      if (!text) {
        return
      }
      // 简历或详情
      return text.length > 40 ? text.substring(0, 40) + '...' : text;

    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    // 切换标签选中状态
    toggleTagSelection(index) {
      // 确保 selectedTags 是一个有效的数组
      if (!Array.isArray(this.selectedTags)) {
        this.selectedTags = [];
      }

      const tagIndex = this.selectedTags.indexOf(index);
      if (tagIndex === -1) {
        // 如果没有选中，则添加
        this.selectedTags.push(index);
      } else {
        // 如果已经选中，则取消选中
        this.selectedTags.splice(tagIndex, 1);
      }
    },
    selectTag(index) {
      this.selectedTagIndex = index;
    },
    // 限制上传格式与大小
    beforeUpload(file) {
      console.log(file.type);
      const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/svg+xml' || file.type ===  'image/webp';

      if (!isJPGorPNG) {
        this.$message.error('上传图片只能是 JPG/PNG/svg/webp 格式!');
      }

      return isJPGorPNG;
    },
    // 上传成功后回填表单字段
    handleUploadSuccess(response) {
      // 假设后台返回的地址在 response.data.url
      if (response && response.url) {
        this.form.cover = response.url;
        this.$message.success('上传成功');
      } else {
        this.$message.error('上传失败，返回数据异常');
      }
    },

    /** 查询社区活动信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeBeginTime && '' != this.daterangeBeginTime) {
        this.queryParams.params["beginBeginTime"] = this.daterangeBeginTime[0];
        this.queryParams.params["endBeginTime"] = this.daterangeBeginTime[1];
      }
      listComActivity(this.queryParams).then(response => {
        this.comActivityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        activityTime: null,
        id: null,
        tags: null,
        title: null,
        beginTime: null,
        postEndTime: null,
        endTime: null,
        cover: null,
        detail: null,
        ruleDetail: null,
        maxSubmissions: 1,
        status: null,
        publish: null,
        reward: null,
        imageNum: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeBeginTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      // 判断是否选中了已发布的消息
      const hasPublished = selection.some(item => item.publish === true);

      // 如果选中了已发布的消息，则禁用删除按钮
      this.multiple = hasPublished ? true : !selection.length;
    },
    // 多选投稿作品
    handlePostsSelectionChange(selection) {
      this.activityRewardSelectionReq.activityRewardSelectionDtoList = selection.map(item => {
        return {
          userId: item.userId,
          loginName: item.loginName,
          fileId: item.fileId,
          fileUrl: item.fileUrl,
          likesNum: item.fileLikeNums,
          commentNum: item.fileCommentNums,
          miniThumbnailUrl:item.miniThumbnailUrl,
          isPize: !!item.prizeLevel
        };
      });
      this.fileIds = selection.map(item => item.fileId)
      // const hasSelection = selection.some(item => item.prizeLevel);
      // this.selectionMultiple = hasSelection ? true : !selection.length
      console.log(this.activityRewardSelectionReq.activityRewardSelectionDtoList);
      console.log(this.fileIds);

    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isViewMode = false;
      this.open = true;
      this.title = "添加社区活动信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isViewMode = false;

      const id = row.id || this.ids;

      getComActivity(id).then(response => {
        this.form = response.data;
        // 将字符串格式的 tags 转换为数组
        let tagList = [];
        try {
          tagList = JSON.parse(this.form.tags || '[]');
        } catch (e) {
          console.error("标签解析失败", e);
        }
        // 回显标签：从 form.tags 中取出对象，找出其在 allTags 中的索引
        if (Array.isArray(tagList)) {
          this.selectedTags = tagList.map(tagObj => {
            const index = this.allTags.findIndex(item => item.type === tagObj.type);
            if (index !== -1) {
              // 回显 title 到 allTags
              this.$set(this.allTags[index], 'title', tagObj.title || '');
            }
            return index;
          }).filter(index => index !== -1); // 过滤无效索引
        } else {
          this.selectedTags = [];
        }
        // 回显时间范围
        if (this.form.beginTime && this.form.postEndTime) {
          this.form.activityTime = [this.form.beginTime, this.form.postEndTime];
        }

        this.open = true;
        this.title = "修改社区活动信息";
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.beginTime = this.form.activityTime[0];
          this.form.postEndTime = this.form.activityTime[1];
          this.form.tags = this.selectedTags
            .map(index => {
              return this.allTags[index];  // 根据索引获取选中的标签对象
            });
          console.log(this.form.tags);
          this.form.tags = JSON.stringify(this.form.tags);
          if (this.form.id != null) {
            updateComActivity(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addComActivity(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除社区活动信息编号为"' + ids + '"的数据项？').then(function () {
        return delComActivity(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/com-activity/export', {
        ...this.queryParams
      }, `com-activity_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped>
.fixed-tag-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.fixed-tag-item {
  position: relative;
  display: flex;
  align-items: center;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  padding: 6px 10px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: 0.3s;
}

.fixed-tag-item.active {
  border-color: #00c2b1;
  box-shadow: 0 0 0 2px rgba(0, 194, 177, 0.2);
}

.check-icon {
  position: absolute;
  top: -6px;
  left: -6px;
  background-color: #00c2b1;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.tag-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
}

.tag-title-input {
  width: 150px;
}

.option-item {
  display: flex;
  align-items: center;
}

.option-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.option-label {
  font-size: 14px;
}

.step-flow {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  position: relative;
}

.step-item.active {
  color: #f3c200;
  font-weight: bold;
}

.step-icon {
  background-color: #fff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
}

.step-title {
  font-size: 14px;
  margin-bottom: 10px;
}

.step-count {
  font-size: 12px;
  display: flex;
  gap: 5px;
  color: #888;
}

.total {
  color: #555;
}

.used {
  color: #f44336;
}
</style>