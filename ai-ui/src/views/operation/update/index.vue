<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="95px">
      <el-form-item label="标题/简介" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入搜索内容" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="是否发布" prop="publish">
        <el-select v-model="queryParams.publish" placeholder="请选择是否发布" clearable>
          <el-option v-for="dict in dict.type.publish_dict" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="平台信息" prop="platform">
        <el-select v-model="queryParams.platform" placeholder="请选择平台信息" clearable>
          <el-option v-for="dict in dict.type.no_ga_platform_dict" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker v-model="daterangePublishTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['operation:update:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['operation:update:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['operation:update:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['operation:update:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-refresh-left" size="mini" @click="handleRefreshPublish"
          v-hasPermi="['operation:update:edit']">刷新已发布的数量到缓存</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="updateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="标题" align="center" prop="title" class-name="ellipsis-text">
        <template slot-scope="scope">
          {{ formatText(1, scope.row.title) }}
        </template>
      </el-table-column>
      <el-table-column label="简介" align="center" prop="introduction" class-name="ellipsis-text">
        <template slot-scope="scope">
          {{ formatText(2, scope.row.introduction) }}
        </template>
      </el-table-column>
      <el-table-column label="平台信息" align="center" prop="platform">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.no_ga_platform_dict" :value="scope.row.platform" />
        </template>
      </el-table-column>
      <el-table-column label="详情" align="center" prop="details">
      <template slot-scope="scope">
          {{ formatText(2, scope.row.details) }}
        </template>
      </el-table-column>
      <el-table-column label="是否发布" align="center" prop="publish">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.publish_dict" :value="scope.row.publish" />
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="publishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.publish" size="mini" type="text" icon="el-icon-edit"
            @click="handleQueryCard(scope.row)" v-hasPermi="['operation:update:query']">查看</el-button>
          <el-button v-if="!scope.row.publish" size="mini" type="text" icon="el-icon-edit"
            @click="handlePublish(scope.row)" v-hasPermi="['operation:update:edit']">发布</el-button>
          <el-button v-if="!scope.row.publish" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)" v-hasPermi="['operation:update:edit']">修改</el-button>
          <el-button v-if="!scope.row.publish" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)" v-hasPermi="['operation:update:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改系统更新消息对话框 -->

    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" maxlength="50"   show-word-limit />
        </el-form-item>
        <el-form-item label="平台信息" prop="platform">
          <el-select v-model="form.platform" placeholder="请选择平台信息">
            <el-option v-for="dict in dict.type.no_ga_platform_dict" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="简介" prop="introduction">
          <el-input v-model="form.introduction" autosize type="textarea" placeholder="请输入内容"  maxlength="120"   show-word-limit/>
        </el-form-item>
        <el-form-item label="详情" prop="details">
          <el-input v-model="form.details" type="textarea" :autosize="{ minRows: 4}" placeholder="请输入内容" maxlength="2000"   show-word-limit  ref="detailsInput" />
          <el-button icon="el-icon-smile" @click="toggleEmojiPicker">表情</el-button>
          <div v-if="showEmojiPicker" class="emoji-picker">
            <div class="emoji-list">
              <span v-for="(emoji, index) in facePositive" :key="emoji + index" class="emoji-item"
                @click="addEmoji(emoji)">
                {{ emoji }}
              </span>
            </div>
          </div>
        </el-form-item>

        <!--   <el-form-item label="详情" prop="details"> 
              <el-form :model="form.detailsForm" :rules="rules">
            <el-form-item>
              <el-input v-model="form.detailsForm.content" type="textarea" autosize placeholder="请输入内容" />
              <el-button icon="el-icon-smile" @click="toggleEmojiPicker">表情</el-button>
              <div v-if="showEmojiPicker" class="emoji-picker">
                <div class="emoji-list">
                  <span v-for="(emoji, index) in facePositive" :key="emoji + index" class="emoji-item"
                    @click="addEmoji(emoji)">
                    {{ emoji }}
                  </span>
                </div>
              </div>
            </el-form-item>
            <el-form-item v-for="domain in form.detailsForm.domains" :label="String(domain.number)" :key="domain.key"
              :prop="String(domain.number)" :rules="{
                required: true, message: '内容不能为空', trigger: 'blur'
              }">
              <div class="flex-item">
                <el-input v-model="domain.value"></el-input>
                <el-button icon="el-icon-smile" @click="toggleEmojiPickerDomain(domain)">表情</el-button>
                <el-button @click.prevent="removeDomain(domain)">删除</el-button>
                
              
              </div>
            </el-form-item>
            <div class="text-end">
              <el-button icon="el-icon-plus" @click="addDomain"></el-button>
            </div>
          </el-form>        </el-form-item>
 -->
      </el-form>
      <div slot="footer" class="dialog-footer">

        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="openQuery" width="550px" append-to-body>
      <div class="clearfix">
        <span style="font-weight: bold; text-align: center; display: block;">{{form.title }}</span>
        <br>
        <br>
        <span>{{form.introduction }}</span>
      </div>
      <el-divider></el-divider>

      <el-input  type="textarea"
      :disabled="true"
  :autosize="{ minRows: 4}"
  v-model="form.details"
      >
      </el-input>
      <br>
      <br>
      <div v-for="o in form.detailsForm.domains" :key="o.number" class="text item">
        {{ String(o.number) + '. ' + o.value }}
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listUpdate, getUpdate, delUpdate, addUpdate, updateUpdate, toPublish, refreshPublish } from "@/api/operation/update";
// 定义外部常量
const facePositive = [
  '😀', '😁', '😂', '🤣', '😃', '😄', '😅', '😆', '😉', '😊', '😋', '😎', '😍', '😘', '😗',
  '😙', '😚', '🙂', '🤗', '😀', '😁', '😂', '🤣', '😃', '😄', '😅', '😆', '😉', '😊', '😋', '😎',
  '😍', '😘', '😗', '😙', '😚', '🙂', '🤗', '🤔', '😐', '😑', '😶', '🙄', '😏', '😣', '😥', '😮',
  '🤐', '😯', '😪', '😫', '😴', '😌', '🤓', '😛', '😜', '😝', '🤤', '😒', '😓', '😔', '😕', '🙃',
  '🤑', '😲', '☹️', '🙁', '😖', '😞', '😟', '😤', '😢', '😭', '😦', '😧', '😨', '😩', '😬', '😰',
  '😱', '😳', '😵', '😡', '😠', '😇', '🤠', '🤡', '🤥', '🤕', '🤢', '🤧', '😈', '👿', '👹', '👺',
  '💀', '☠️', '👻', '👽', '👾', '🤖', '💩', '😺', '😸', '🙈', '🙉', '🙊', '👦', '👧', '👨', '👩',
  '👴', '👵', '👶', '👼', '👨‍⚕️', '👩‍⚕️', '👨‍🎓', '👩‍🎓', '👨‍🏫', '👩‍🏫', '👨‍⚖️', '👩‍⚖️', '👨‍🌾', '👩‍🌾', '👨‍🍳', '👩‍🍳',
  '👨‍🔧', '👩‍🔧', '👨‍🏭', '👩‍🏭', '👨‍💼', '👩‍💼', '👨‍🔬', '👩‍🔬', '👨‍💻', '👩‍💻', '👨‍🎤', '👨‍🎨', '👩‍🎤','👨‍🔬', '👩‍🔬', '👨‍💻',
  '👩‍💻', '👨‍🎤', '👩‍🎤', '👨‍🎨', '👩‍🎨', '👨‍✈️', '👩‍✈️', '👨‍🚀', '👩‍🚀', '👨‍🚒', '👩‍🚒', '👮', '👮‍♂️', '👮‍♀️', '🕵️', '🕵️‍♂️',
  '🕵️‍♀️', '💂', '💂‍♂️', '💂‍♀️', '👷', '👷‍♂️', '👷‍♀️', '👳', '👳‍♂️', '👳‍♀️', '👱', '👱‍♂️', '👱‍♀️', '🎅', '🤶', '👸',
  '🤴', '👰', '🤵', '🤰', '👲','🙍', '🙍‍♂️', '🙍‍♀️', '🙎', '🙎‍♂️', '🙎‍♀️', '🙅', '🙅‍♂️', '🙅‍♀️', '🙆', '🙆‍♂️', 
  '🙆‍♀️', '💁', '💁‍♂️', '💁‍♀️', '🙋', '🙋‍♂️', '🙋‍♀️', '🙇', '🙇‍♂️', '🙇‍♀️', '🤦', '🤦‍♂️', '🤦‍♀️', '🤷', '🤷‍♂️', '🤷‍♀️',
  '💆', '💆‍♂️', '💆‍♀️', '💇', '💇‍♂️', '💇‍♀️', '🚶', '🚶‍♂️', '🚶‍♀️', '🏃', '🏃‍♂️', '🏃‍♀️', '💃', '🕺', '👯', '👯‍♂️', 
  '👯‍♀️', '🕴️', '🗣️', '👤', '👥', '🤺', '🏇', '⛷️', '🏂', '🏌️', '🏌️‍♂️', '🏌️‍♀️', '🏄', '🏄‍♂️', '🏄‍♀️', '🚣',
  '🚣‍♂️', '🚣‍♀️', '🏊', '🏊‍♂️', '🏊‍♀️', '⛹️', '⛹️‍♂️', '⛹️‍♀️', '🏋️', '🏋️‍♂️', '🏋️‍♀️', '🚴', '🚴‍♂️', '🚴‍♀️', '🚵', '🚵‍♂️', 
  '🚵‍♀️', '🏎️', '🏍️', '🤸', '🤸‍♂️', '🤸‍♀️', '🤼', '🤼‍♂️', '🤼‍♀️', '🤽', '🤽‍♂️', '🤽‍♀️', '🤾', '🤾‍♂️', '🤾‍♀️', '🤹',
  '🤹‍♂️', '🤹‍♀️', '👫', '👬', '👭', '💏', '💑', '👪', '💪', '🤳', '👈', '👉', '☝️', '👆', '🖕', '👇',
  '✌️', '🤞', '🖖', '🤘', '🤙', '🖐️', '✋', '👌', '👍', '👎', '✊', '👊', '🤛', '🤜', '🤚', '👋',
  '👏', '✍️', '👐', '🙌', '🙏', '🤝', '💅', '👂', '👃', '👣', '👀', '👁️', '👁️‍🗨️', '👅', '👄', '💋', 
  '💘', '❤️', '💓', '💔', '💕', '💖', '💗', '💙', '💚', '💛', '💜', '🖤', '💝', '💞', '💟', '❣️',
  '💌', '💤', '💢', '💣', '💥', '💦', '💨', '💫', '💬', '🗨️', '🗯️', '💭', '🕳️', '👓', '🕶️', '👔', 
  '👕', '👖', '👗', '👘', '👙', '👚', '👛', '👜', '👝', '🛍️', '🎒', '👞', '👟', '👠', '👡', '👢', 
  '👑', '👒', '🎩', '🎓', '⛑️', '📿', '💄', '💍', '💎'
];

export default {
  name: "Update",
  dicts: ['publish_dict', 'no_ga_platform_dict'],

  data() {
    return {
      domainIndex: null,
      isContent: false,
      facePositive,
      showEmojiPicker: false,
      openQuery: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统更新消息表格数据
      updateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情时间范围
      daterangePublishTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        publish: null,
        publishTime: null
      },
      // 表单参数
      form: {
        details: '',
        detailsForm: {
          content: '',
          domains: [{
            number: 1,
            value: ''

          }]
        }

      },
      rules: {
      // 表单校验
      title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        introduction: [
          { required: true, message: "简介不能为空", trigger: "blur" }
        ],
        details: [
          { required: true, message: "详情不能为空", trigger: "blur" }
        ],
        platform: [
          { required: true, message: "平台信息不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    toggleEmojiPickerDomain(item) {
      this.domainIndex = this.form.detailsForm.domains.indexOf(item)
      this.isContent = false;
      this.showEmojiPicker = !this.showEmojiPicker; // 切换表情选择框的显示和隐藏

    },
    toggleEmojiPicker() {
      this.isContent = true;
      this.showEmojiPicker = !this.showEmojiPicker; // 切换表情选择框的显示和隐藏
    },
    addEmoji(emoji) {
      const inputElement = this.$refs.detailsInput.$el.querySelector('textarea');

      if (this.isContent) {
        const cursorPos = inputElement.selectionStart; // 获取光标位置
        const beforeText = this.form.details.slice(0, cursorPos); // 获取光标前的文本
        const afterText = this.form.details.slice(cursorPos); // 获取光标后的文本

        // 将表情插入光标位置
        this.form.details = beforeText + emoji + afterText;

        // 设置光标位置在表情后面
        this.$nextTick(() => {
          inputElement.selectionStart = inputElement.selectionEnd = cursorPos + emoji.length;
        });
      } else {
        if (this.domainIndex !== -1) {
          this.form.detailsForm.domains[this.domainIndex].value += emoji;
        }
      }

      this.showEmojiPicker = false; // 隐藏表情选择器
    }
    ,
    clearEmojiPicker() {
      const container = this.$refs.emojiContainer;
      if (container) {
        container.innerHTML = ''; // 清除现有的表情选择器
      }
    },
    handleRefreshPublish() {
      refreshPublish().then(response => {
        this.$modal.msgSuccess("刷新成功");
      });
    },
    /** 查看按钮操作 */
    handleQueryCard(row) {
      this.reset();
      const id = row.id
      getUpdate(id).then(response => {
        this.form = response.data;
        this.openQuery = true;
      });
    },
    parseTime(time, format) {
      if (!time) return '';
      const date = new Date(time);
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      const formatted = parts.map(part => part.value).join('');
      return formatted;
    },
    /** 发布按钮操作 */
    handlePublish(row) {
      const id = row.id;
      this.$modal.confirm('是否确认发布编号为"' + id + '"的系统更新消息？').then(function () {
        return toPublish(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("发布成功");
      }).catch(() => { });
    },
    formatText(type, text) {
      if (!text) {
        return
      }
      if (type === 1) {
        // 标题
        return text.length > 40 ? text.substring(0, 40) + '...' : text;
      } else {
        // 简历或详情
        return text.length > 120 ? text.substring(0, 120) + '...' : text;
      }
    },
    removeDomain(item) {
      var index = this.form.detailsForm.domains.indexOf(item)
      if (index !== -1) {
        this.form.detailsForm.domains.splice(index, 1)
      }
    },
    addDomain() {
      var len = this.form.detailsForm.domains.length;
      console.log(len);
      this.form.detailsForm.domains.push({
        value: '',
        key: Date.now(),

        number: len + 1
      });
    },
    /** 查询系统更新消息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePublishTime && '' != this.daterangePublishTime) {
        this.queryParams.params["beginPublishTime"] = this.daterangePublishTime[0];
        this.queryParams.params["endPublishTime"] = this.daterangePublishTime[1];
      }
      listUpdate(this.queryParams).then(response => {
        this.updateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        introduction: null,
        details: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        publish: null,
        publishTime: null,
        details: '',
        detailsForm: {
          content: '',
          domains: [{
            number: 1,
            value: ''

          }]
        }
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePublishTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      // 判断是否选中了已发布的消息
      const hasPublished = selection.some(item => item.publish === true);

      // 如果选中了已发布的消息，则禁用删除按钮
      this.multiple = hasPublished ? true : !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加系统更新消息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getUpdate(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改系统更新消息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateUpdate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUpdate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除系统更新消息编号为"' + ids + '"的数据项？').then(function () {
        return delUpdate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/update/export', {
        ...this.queryParams
      }, `update_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.flex-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
}

.text-end {
  text-align: right;
  margin-top: 12px;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.box-card {
  width: 480px;
}

.emoji-picker {
  max-height: 200px; /* 设置最大高度 */
  position: absolute;
  bottom: 60px;
  overflow-y: auto;  /* 超出部分显示滚动条 */
  /* 可以调整为合适的位置 */
  right: 20px;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
  z-index: 1000;
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 5px;
}

.emoji-item {
  font-size: 20px;
  cursor: pointer;
  text-align: center;
}

.emoji-item:hover {
  background-color: #f0f0f0;
  border-radius: 5px;
}
</style>