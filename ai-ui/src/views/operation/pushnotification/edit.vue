<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>修改推送消息</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="推送类型" prop="pushType">
          <el-select v-model="form.pushType" placeholder="请选择" style="width: 200px">
            <el-option :value="1" label="文本" />
            <el-option :value="2" label="大图" />
          </el-select>
        </el-form-item>

        <el-form-item label="推送时间" prop="pushTime">
          <el-date-picker
            v-model="form.pushTime"
            type="datetime"
            placeholder="选择推送时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="form.priority" :min="1" :max="999" style="width: 200px" />
        </el-form-item>

        <el-form-item label="scheme url" prop="schemeUrl">
          <el-select v-model="form.schemeUrl" placeholder="请选择scheme url" style="width: 200px">
            <el-option
              v-for="item in schemeOptions"
              :key="item.name"
              :label="item.description"
              :value="item.toolType"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="推送国家" prop="pushScope">
          <el-tree
            ref="countryTree"
            :data="countryOptions"
            show-checkbox
            node-key="id"
            :props="defaultProps"
            @check="handleCheckChange"
            class="country-tree"
          />
        </el-form-item>

        <!-- 多语言内容 -->
        <div v-for="(lang, index) in languages" :key="index" class="language-section">
          <div class="language-header">
            <span class="language-label">{{ lang.name }}</span>
          </div>
          <el-form-item
            :label="'标题'"
            :prop="'contents.' + index + '.title'"
            :rules="[{ required: true, message: `请输入${lang.name}标题`, trigger: 'blur' }]"
          >
            <el-input
              v-model="form.contents[index].title"
              :placeholder="`请输入${lang.name}标题`"
              :suffix-icon="'el-icon-document-copy'"
              @click.native.stop
            />
          </el-form-item>
          <el-form-item
            :label="'内容'"
            :prop="'contents.' + index + '.body'"
            :rules="[{ required: true, message: `请输入${lang.name}内容`, trigger: 'blur' }]"
          >
            <el-input
              v-model="form.contents[index].body"
              :placeholder="`请输入${lang.name}内容`"
              :suffix-icon="'el-icon-document-copy'"
              @click.native.stop
            />
          </el-form-item>
        </div>

        <el-form-item>
          <el-button type="primary" @click="submitForm">确认</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getPushNotification, updatePushNotification } from '@/api/operation/pushnotification'
import request from '@/utils/request'

export default {
  name: 'EditPushNotification',
  data() {
    const validatePushScope = (rule, value, callback) => {
      if (!this.form.pushScope || this.form.pushScope.length === 0) {
        callback(new Error('请选择推送国家'))
      } else {
        callback()
      }
    }

    return {
      // 表单参数
      form: {
        id: undefined,
        pushType: undefined,
        pushTime: undefined,
        priority: 1,
        schemeUrl: '',
        pushScope: [],
        picUrl: '',
        contents: [
          { lang: 'en', title: '', body: '' },
          { lang: 'de_DE', title: '', body: '' },
          { lang: 'ja_JP', title: '', body: '' },
          { lang: 'es', title: '', body: '' },
          { lang: 'pt_BR', title: '', body: '' },
          { lang: 'fr_FR', title: '', body: '' },
          { lang: 'ko', title: '', body: '' },
          { lang: 'it', title: '', body: '' },
          { lang: 'zh-TW', title: '', body: '' }
        ]
      },
      // 表单校验
      rules: {
        pushType: [{ required: true, message: '请选择推送类型', trigger: 'change' }],
        pushTime: [{ required: true, message: '请选择推送时间', trigger: 'change' }],
        priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }],
        schemeUrl: [{ required: true, message: '请输入scheme url', trigger: 'blur' }],
        pushScope: [{ validator: validatePushScope, trigger: 'change' }]
      },
      languages: [
        { code: 'en', name: '英语' },
        { code: 'de_DE', name: '德语' },
        { code: 'ja_JP', name: '日语' },
        { code: 'es', name: '西班牙语' },
        { code: 'pt_BR', name: '葡萄牙语' },
        { code: 'fr_FR', name: '法语' },
        { code: 'ko', name: '韩语' },
        { code: 'it', name: '意大利语' },
        { code: 'zh-TW', name: '繁体中文' }
      ],
      countryOptions: [],
      defaultProps: {
        children: 'children',
        label: 'country'
      }
    }
  },
  created() {
    const id = this.$route.params.id
    this.getInfo(id)
    this.loadCountryList()
  },
  methods: {
    /** 获取详细信息 */
    getInfo(id) {
      getPushNotification(id).then(response => {
        const data = response.data
        this.form = {
          id: data.id,
          pushType: data.pushType,
          pushTime: data.pushTime,
          priority: data.priority,
          schemeUrl: data.schemeUrl,
          pushScope: data.pushScope ? data.pushScope.split(',') : [],
          picUrl: data.picUrl,
          contents: this.languages.map(lang => {
            const content = data.contents.find(c => c.languageCode === lang.code)
            return {
              lang: lang.code,
              title: content ? content.title : '',
              body: content ? content.body : ''
            }
          })
        }
      })
    },
    /** 加载国家列表 */
    async loadCountryList() {
      try {
        const res = await getCountryList()
        if (res.code === 200 && res.data) {
          this.countryOptions = this.formatCountryData(res.data)
        } else {
          this.$message.error(res.msg || '获取国家列表失败')
        }
      } catch (error) {
        console.error('获取国家列表失败:', error)
        this.$message.error('获取国家列表失败')
      }
    },
    /** 格式化国家数据为树形结构 */
    formatCountryData(data) {
      const priorities = [...new Set(data.map(item => item.priority))].sort((a, b) => a - b)
      const priorityGroups = priorities.map(priority => ({
        id: `p${priority}`,
        country: `优先级${priority}`,
        priority: priority,
        children: []
      }))

      data.forEach(item => {
        const country = {
          id: item.id,
          country: item.country,
          priority: item.priority
        }
        const group = priorityGroups.find(g => g.priority === item.priority)
        if (group) {
          group.children.push(country)
        }
      })

      return priorityGroups
    },
    /** 处理选中变化 */
    handleCheckChange(data, checked) {
      const checkedNodes = this.$refs.countryTree.getCheckedNodes()
      const selectedCountries = checkedNodes.filter(node => !node.children)
      this.form.pushScope = selectedCountries.map(node => node.country)
      this.$refs.form.validateField('pushScope')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const data = {
            pushMessage: {
              id: this.form.id,
              pushType: this.form.pushType,
              pushTime: this.form.pushTime,
              priority: this.form.priority,
              schemeUrl: this.form.schemeUrl,
              pushScope: this.form.pushScope.join(','),
              thumbnail: this.form.thumbnail,
              picUrl: this.form.picUrl
            },
            pushMessageContentList: this.form.contents.map(content => ({
              languageCode: content.lang,
              title: content.title,
              body: content.body
            }))
          }

          updatePushNotification(data).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess('修改成功')
              setTimeout(() => {
                this.cancel()
              }, 500)
            } else {
              this.$modal.msgError(response.msg || '修改失败')
            }
          }).catch(() => {
            this.$modal.msgError('修改失败')
          })
        }
      })
    },
    /** 取消按钮 */
    cancel() {
      this.$router.push('/operation/pushnotification')
    }
  }
}
</script>

<style scoped>
.language-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
.language-header {
  margin-bottom: 15px;
}
.language-label {
  font-weight: bold;
  color: #409EFF;
}
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
  display: inline-block;
}
.image-uploader:hover {
  border-color: #409EFF;
}
.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.uploaded-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}
.image-size-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
.country-tree {
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
}
.country-tree .el-tree-node__content {
  height: 32px;
}
.country-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa;
}
</style>
