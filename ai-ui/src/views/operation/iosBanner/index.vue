<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="跳转类型" prop="jumpType">
        <el-select v-model="queryParams.jumpType" placeholder="请选择跳转类型" clearable>
          <el-option v-for="dict in dict.type.banner_jump_type_dict" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="图片状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择图片状态" clearable>
          <el-option v-for="dict in dict.type.publish_dict" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['operation:iosBanner:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['operation:iosBanner:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['operation:iosBanner:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['operation:iosBanner:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="iosBannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键id" align="center" prop="id" />
      <el-table-column label="图片" align="center" prop="imgUrl" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.imgUrl" :width="50" :height="50"  />
        </template>
      </el-table-column>
      <el-table-column label="图片顺序" align="center" prop="sort" />
      <el-table-column label="跳转类型" align="center" prop="jumpType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.banner_jump_type_dict" :value="scope.row.jumpType" />
        </template>
      </el-table-column>
      <el-table-column label="跳转标题" align="center" prop="title" />
      <!-- <el-table-column label="跳转id" align="center" prop="jumpId" /> -->
      <!-- <el-table-column label="图片状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.publish_dict" :value="scope.row.status" />
        </template>
      </el-table-column> -->
      <el-table-column label="启用状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="true"
            :inactive-value="false"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="更新者" align="center" prop="updateBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:iosBanner:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['operation:iosBanner:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改ios_社区首页banner图配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="图片链接" prop="imgUrl">
          <image-upload v-model="form.imgUrl" :limit="1"   custom-tip="图片尽量为360 * 150 的大小"/>
        </el-form-item>

        <el-form-item label="图片顺序" prop="sort">
          <el-input-number v-model="form.sort" placeholder="请输入图片顺序" />
        </el-form-item>

        <el-form-item label="图片状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in dict.type.publish_dict" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="跳转类型" prop="jumpType">
          <el-select v-model="form.jumpType" placeholder="请选择跳转类型">
            <el-option v-for="dict in dict.type.banner_jump_type_dict" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>

        <el-form-item  :label="dynamicJumpLabel" prop="jumpId" v-if="form.jumpType && form.jumpType !== 'tools'">
          <el-select v-model="form.jumpId"  :placeholder="'请选择' + dynamicJumpLabel">
            <el-option v-for="item in filteredJumpIdList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>


      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listIosBanner, getIosBanner, delIosBanner, addIosBanner, updateIosBanner, getJumpIdTree } from "@/api/operation/iosBanner";

export default {
  name: "IosBanner",
  dicts: ['banner_jump_type_dict', 'publish_dict'],
  data() {
    return {
      jumpIdList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ios_社区首页banner图配置表格数据
      iosBannerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jumpType: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        imgUrl: [
          { required: true, message: "图片链接不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "图片顺序不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        jumpType: [
          { required: true, message: "跳转类型不能为空", trigger: "change" }
        ],
        jumpId: [
          { required: true, message: "跳转id不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.buildJumpIdTree();
  },
  computed: {
      // 动态 label，例如“活动”
  dynamicJumpLabel() {
    const match = this.dict.type.banner_jump_type_dict.find(
      item => item.value === this.form.jumpType
    );
    return match ? match.label : '跳转标题';
  },
    filteredJumpIdList() {
      return this.jumpIdList.filter(item => item.jumpType === this.form.jumpType);
    }
  },
  methods: {
    handleStatusChange(row) {
      let title ="";
      if (row.title){
        title = row.title;
      }
      let text = row.status ? "开启" : "关闭" + title;
      this.$modal.confirm('确认要   "' + text + '"吗？').then(() => {
        return updateIosBanner(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.status = !row.status;
      });
    },
    parseTime(time, format) {
      if (!time) return '';
      let date;
      // 判断是否是秒级时间戳（长度为 10 的字符串或数字）
      if ((typeof time === 'string' && /^\d{10}$/.test(time)) || (typeof time === 'number' && time.toString().length === 10)) {
        date = new Date(Number(time) * 1000); // 转为毫秒
      } else {
        date = new Date(time); // 直接当作普通时间处理
      }
      // 如果日期无效，返回空字符串
      if (isNaN(date.getTime())) {
        return '';
      }
      // 格式为 'yyyy-MM-dd HH:mm:ss' 时手动格式化
      if (format === 'yyyy-MM-dd HH:mm:ss') {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      // 否则使用默认格式化器
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      return parts.map(part => part.value).join('');
    },
    buildJumpIdTree() {
      getJumpIdTree().then(response => {
        this.jumpIdList = response.data;
      });
    },
    /** 查询ios_社区首页banner图配置列表 */
    getList() {
      this.loading = true;
      listIosBanner(this.queryParams).then(response => {
        this.iosBannerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        imgUrl: null,
        sort: 1,
        jumpType: '',
        jumpId: null,
        isDeleted: null,
        status: false,
        createBy: null,
        updateBy: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加ios_社区首页banner图配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getIosBanner(id).then(response => {
        this.form = response.data;
        this.form.status = String(this.form.status)
        this.open = true;
        this.title = "修改ios_社区首页banner图配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateIosBanner(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIosBanner(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除ios_社区首页banner图配置编号为"' + ids + '"的数据项？').then(function () {
        return delIosBanner(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/iosBanner/export', {
        ...this.queryParams
      }, `iosBanner_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
