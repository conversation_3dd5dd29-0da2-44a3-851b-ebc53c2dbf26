<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="code" prop="code">
                <el-input v-model="queryParams.code" placeholder="请输入code" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="message" prop="message">
                <el-input v-model="queryParams.message" placeholder="请输入message" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="description" prop="description">
                <el-input v-model="queryParams.description" placeholder="请输入description " clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="codeList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="code" align="center" prop="code" />
            <el-table-column label="message" align="center" prop="message" />
            <el-table-column label="description" align="center" prop="description" />
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import { errorCode } from "@/api/operation/params";

export default {
    name: "User",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户信息表格数据
            codeList: [],
            // 控制卡片弹窗显示
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                code: null,
                message: null,
                description: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询用户信息列表 */
        getList() {
            this.loading = true;
            errorCode(this.queryParams).then(response => {
                this.codeList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 表单重置
        reset() {
            this.form = {
                code: null,
                message: null,
                description: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.daterangeVipBeginTime = [];
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        }
    }
};
</script>