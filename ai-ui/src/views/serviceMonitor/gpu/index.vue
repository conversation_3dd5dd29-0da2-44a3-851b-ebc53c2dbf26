<template>
  <div class="personal-analysis">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>piclumenGPU监控BI</h2>
      </el-col>
      <el-col :sm="24" :lg="12" class="refresh-col">
        <el-button type="primary" icon="el-icon-refresh" @click="fetchData">刷新</el-button>
      </el-col>
    </el-row>
    <el-divider />
    <el-row :gutter="20">
      <el-col :span="12">
        <el-select v-model="selectedType1" placeholder="请选择时间范围" @change="fetchData">
          <el-option label="1小时(跨度1分钟)" value="1"></el-option>
          <el-option label="3小时(跨度1分钟)" value="2"></el-option>
          <el-option label="12小时(跨度5分钟)" value="3"></el-option>
          <el-option label="24小时(跨度5分钟)" value="4"></el-option>
          <el-option label="2天(跨度1小时)" value="5"></el-option>
          <el-option label="7天(跨度1小时)" value="6"></el-option>
        </el-select>
      </el-col>
      <el-col :span="12">
        <el-select v-model="selectedType2" placeholder="请选择指标" @change="fetchData">
          <el-option label="GPU显存使用率" value="Gpumemusage"></el-option>
          <el-option label="GPU使用率" value="Gpuutil"></el-option>
          <el-option label="GPU显存使用量" value="GpuMemUsed"></el-option>
          <el-option label="CPU使用率" value="CpuUsage"></el-option>
          <el-option label="内存利用率" value="MemUsage"></el-option>
          <el-option label="内存使用量" value="MemUsed"></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col v-for="chart in charts" :key="chart.id" :span="12" class="chart-col">
        <div :id="chart.id" style="width: 100%; height: 400px; margin-top: 20px;"></div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { DateTime } from 'luxon';
import { getGpuMonitorData, getServerNames } from "@/api/serviceMonitor/gpu";

const defaultServerNames = {
  'ins-luu3w816': 'g1',
  'ins-okjxtfpe': 'g2',
  'ins-a7sja5ya': 'g3',
  'ins-k2znufm0': 'g4',
  'ins-6q1zimju': 'g5',
  'ins-kab72wq4': 'g6',
  'ins-bip3g0jk': 'g7',
  'ins-0gn84x9o': 'g8'
};

export default {
  data() {
    return {
      selectedType1: '1', // 默认时间范围
      selectedType2: 'Gpuutil', // 默认指标
      charts: [],
      maxMemory: 24,
      isMemUsed: false,
      serverNames: defaultServerNames // 默认的 serverNames
    };
  },
  mounted() {
    this.fetchServerNames(); // 组件加载时获取服务器名称数据
    this.fetchData(); // 组件加载时获取数据
  },
  methods: {
    async fetchServerNames() {
      try {
        const response = await getServerNames();
        if (response.data && typeof response.data === 'object') {
          this.serverNames = response.data;
        } else {
          console.error('Invalid serverNames format:', response.data);
          this.serverNames = defaultServerNames; // 使用默认数据
        }
      } catch (error) {
        console.error('Error fetching server names:', error);
        this.serverNames = defaultServerNames; // 使用默认数据
      }
    },

    async fetchData() {
      try {
        const response = await getGpuMonitorData(this.selectedType1, this.selectedType2);
        const dataPoints = response.data.dataPoints;

        if (!dataPoints || !Array.isArray(dataPoints)) {
          console.error('Invalid dataPoints format:', dataPoints);
          throw new Error('Invalid dataPoints format');
        }

        // Process data and store chart data
        this.charts = dataPoints.map((dataPoint, index) => this.processDataPoint(dataPoint, index));
        
        // Render charts after DOM updates
        this.$nextTick(() => {
          this.renderCharts();
        });
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    },

    processDataPoint(dataPoint, index) {
      const timestamps = (dataPoint.timestamps || []).map(ts => DateTime.fromSeconds(parseInt(ts)).toFormat('yyyy-MM-dd HH:mm:ss'));
      let avgValues = dataPoint.avgValues || [];
      const instanceId = dataPoint.dimensions.find(d => d.name === 'InstanceId')?.value || 'Unknown';
      const serverName = this.serverNames[instanceId] || 'Unknown Server'; // 使用映射获取服务器名称
      // If the selected type is GPU 内存使用量, convert values to GB and set unit
      if (this.selectedType2 === 'GpuMemUsed') {
        avgValues = avgValues.map(value => value / 1024); // Convert MB to GB
        this.isMemUsed = true;
        this.maxMemory = 24;
      } else if (this.selectedType2 === 'MemUsed') {
        avgValues = avgValues.map(value => value / 1024); // Convert MB to GB
        this.maxMemory = 32;
        this.isMemUsed = true;
      } else {
        this.isMemUsed = false;
      }

      return {
        id: `chart-${index}`,
        title: `服务器: ${serverName}`,
        xAxisData: timestamps,
        seriesData: avgValues
      };
    },

    renderCharts() {
      this.charts.forEach(chart => {
        const chartDom = document.getElementById(chart.id);
        if (!chartDom) {
          console.warn(`Chart container with ID ${chart.id} not found.`);
          return;
        }

        const myChart = echarts.init(chartDom);

        const option = {
          title: {
            text: chart.title,
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: chart.xAxisData
          },
          yAxis: {
            type: 'value',
            max: this.isMemUsed ? this.maxMemory : 100, // 根据指标选择最大值
            axisLabel: {
              formatter: this.isMemUsed ? '{value} GB' : '{value} %' // 根据指标选择单位
            }
          },
          series: [{
            name: 'Value',
            type: 'line',
            data: chart.seriesData
          }]
        };

        myChart.setOption(option);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.personal-analysis {
  .el-select {
    margin-right: 20px;
  }
  .refresh-col {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .chart-col {
    padding: 10px;
  }
}
</style>
