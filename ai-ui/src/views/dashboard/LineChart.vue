<template>
  <div :class="className" :style="{height:height,width:width}"/>
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { getKpiMixWeek } from "@/api/operation/mix";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null,
      chartData: {
        dau: [],
        newRegisters: [],
        dates: []
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.fetchData()
    })
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ dau, newRegisters, dates }) {
      this.chart.setOption({
             title: {
          text: '7天活跃指标',
           textStyle: {
          color: '#000000' // 设置标题颜色为黑色
        }
        },
        xAxis: {
          data: dates,
          boundaryGap: true, // Allow space for the labels
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 20, // Increase space on the right
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        legend: {
          data: ['日活数量', '新注册用户数量']
        },
        series: [
          {
            name: '日活数量',
            smooth: true,
            type: 'line',
            itemStyle: {
              normal: {
                color: '#FF005A',
                lineStyle: {
                  color: '#FF005A',
                  width: 2
                }
              }
            },
            data: dau,
            animationDuration: 2800,
            animationEasing: 'cubicInOut'
          },
          {
            name: '新注册用户数量',
            smooth: true,
            type: 'line',
            itemStyle: {
              normal: {
                color: '#3888fa',
                lineStyle: {
                  color: '#3888fa',
                  width: 2
                },
                areaStyle: {
                  color: '#f3f8ff'
                }
              }
            },
            data: newRegisters,
            animationDuration: 2800,
            animationEasing: 'quadraticOut'
          }
        ]
      })
    },
    fetchData() {
      getKpiMixWeek(1).then(response => {
        const data = response.data;
        const dau = data.map(item => item.dau);
        const newRegisters = data.map(item => item.newRegisters);
        const dates = data.map(item => item.recordDate);
        
        this.chartData = { dau, newRegisters, dates };
      }).catch(error => {
        console.error("Error fetching data: ", error);
      });
    }
  }
}
</script>
