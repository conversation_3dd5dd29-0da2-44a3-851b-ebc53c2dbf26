<template>
    <div class="app-container">
        <div class="fixed-toolbar">

            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                label-width="40px">
                <el-form-item label="账号" prop="loginName">
                    <el-input v-model="queryParams.loginName" placeholder="请输入用户登录账号" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-date-picker v-model="value2" type="datetimerange" :picker-options="pickerOptions"
                        value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" align="right">
                    </el-date-picker>
                </el-form-item>
                <el-form-item v-if="queryParams.loginName || (value2 && value2.length === 2)" label="平台"
                    prop="platform">
                    <el-input v-model="queryParams.platform" placeholder="请输入平台" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-if="queryParams.loginName || (value2 && value2.length === 2)" label="ip 国家"
                    prop="ipCountry">
                    <el-input v-model="queryParams.ipCountry" placeholder="请输入ip 国家" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                        v-hasPermi="['task:instance:export']">导出</el-button>
                </el-col>
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
        </div>
        <el-table v-loading="loading" :data="actionLogList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户账号" align="center" prop="userLoginName" />
            <el-table-column label="用户Id" align="center" prop="userId" />
            <el-table-column label="平台" align="center" prop="platform" />
            <!-- <el-table-column label="请求内容" align="center" prop="requestContent" /> -->
            <el-table-column label="请求内容" align="center" prop="requestContent" class-name="ellipsis-text">
                <template slot-scope="scope">
                    <span class="ellipsis-text" @click="showDetail('请求内容', scope.row.requestContent)">
                        {{ formatText(scope.row.requestContent) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="请求ip" align="center" prop="requestIp" />
            <el-table-column label="ip所属国家" align="center" prop="ipCountry" />
            <el-table-column label="请求路径" align="center" prop="requestUrl" />
            <el-table-column label="请求描述" align="center" prop="requestName" />

            <!-- <el-table-column label="功能类型" align="center" prop="featureName" /> -->
            <el-table-column label="功能类型" align="center" prop="featureName">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.features_type_dict" :value="scope.row.featureName" />
                </template>
            </el-table-column>
            <el-table-column label="模型" align="center" prop="modelId">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.prompt_record_model_id" :value="scope.row.modelId" />
                </template>
            </el-table-column>
            <!-- <el-table-column label="模型id" align="center" prop="modelId" /> -->
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
                </template>
            </el-table-column>
        </el-table>
        <pagination layout="sizes, prev, next" :total="10000000" :page.sync="queryParams.pageNum"
            :page-sizes="[10, 20, 30, 50, 100, 200]" :limit.sync="queryParams.pageSize" @pagination="getList"
            @prev="prevClick" @next="nextClick" />
        <el-dialog :title="detailTitle" :visible.sync="detailVisible" width="500px" append-to-body>
            <p>{{ detailContent }}</p>
            <div slot="footer" class="dialog-footer">
                <el-button @click="detailVisible = false">关闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getActionList } from "@/api/userLog/actionLog";
export default {
    name: "actionLog",
    dicts: ['features_type_dict', 'prompt_record_model_id'],

    data() {
        return {
            pickerOptions: {
                shortcuts: [{
                    text: '最近一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                },]
            },
            value2: [],
            isCheckedAll: false, //是否全选
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            actionLogList: [],
            // 查询参数
            queryParams: {
                pageSize: 10,
                isNext: null,
                markFileId: null,
                loginName: null,
                startTime: null,
                endTime: null,
                platform: null
            },
            detailVisible: false,
            detailTitle: '',
            detailContent: '',
        };
    },
    created() {
        this.getList();
    },
    watch: {
        'queryParams.loginName'(newVal) {
            this.handlePlatformVisibility();
        },
        value2(newVal) {
            this.handlePlatformVisibility();
        }
    },
    computed: {
    },
    methods: {
        handlePlatformVisibility() {
            const hasLoginName = !!this.queryParams.loginName;
            const hasDateRange = this.value2 && this.value2.length === 2;

            if (!hasLoginName && !hasDateRange) {
                this.queryParams.platform = ''; // 自动清空平台字段
                this.queryParams.ipCountry = '';
            }
        },
        formatText(text) {
            return text.length > 50 ? text.substring(0, 50) + '...' : text;
        },
        showDetail(title, content) {
            this.detailTitle = title;
            this.detailContent = content;
            this.detailVisible = true;
        },
        parseTime(time, format) {
            if (!time) return '';
            const date = new Date(time);
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false,
            };
            const formatter = new Intl.DateTimeFormat('zh-CN', options);
            const parts = formatter.formatToParts(date);
            const formatted = parts.map(part => part.value).join('');
            return formatted;
        },
        dateChange() {
            this.queryParams.markFileId = null;
            this.queryParams.isNext = null;
        },
        nextClick() {
            this.queryParams.markFileId = this.actionLogList[this.actionLogList.length - 1].id
            this.queryParams.isNext = true;
        },
        prevClick() {
            this.queryParams.markFileId = this.actionLogList[0].id
            this.queryParams.isNext = false;
        },
        /** 查询用户图片公开审核列表 */
        getList() {
            this.loading = true;
            if (null != this.value2 && '' != this.value2) {
                const start = new Date(this.value2[0]);
                const end = new Date(this.value2[1]);

                const diffTime = end.getTime() - start.getTime();
                const diffDays = diffTime / (1000 * 60 * 60 * 24);

                if (diffDays > 7) {
                    this.$modal.msgWarning("开始时间与结束时间不能超过 7 天！");
                    this.loading = false;
                    return;
                }

                this.queryParams.startTime = this.value2[0];
                this.queryParams.endTime = this.value2[1];
            }
            console.log(this.queryParams)
            getActionList(this.queryParams).then(response => {
                if (response.rows.length === 0) {
                    this.$modal.msgSuccess("未查询到数据 返回上个列表页面");
                    this.loading = false;
                    return;
                }
                this.actionLogList = response.rows.map(item => {
                    return item
                });
                this.loading = false;
                this.ids = [];
                this.isCheckedAll = false;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.markFileId = null;
            this.queryParams.isNext = null;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.value2 = [];
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                "userLog/actionLog/export",
                {
                    ...this.queryParams,
                },
                `用户行为日志_${new Date().getTime()}.xlsx`
            );
        },
    }
};
</script>
<style scoped></style>