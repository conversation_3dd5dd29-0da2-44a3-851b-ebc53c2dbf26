<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务提交时间(单日)"  label-width="135px" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          style="width: 197px"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择日期（默认昨日）">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="任务提交时间" label-width="100px">
        <el-date-picker v-model="daterangeGenStartTime" style="width: 335px" value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange" range-separator="-" start-placeholder="开始日期（默认昨日）"
          end-placeholder="结束日期（默认昨日）"></el-date-picker>
      </el-form-item>
      <el-form-item label="任务是否成功"  label-width="100px" prop="sendWsFailure">
        <el-select v-model="queryParams.isSuccess" placeholder="请选择任务是否成功" clearable>
          <el-option v-for="dict in isSuccessList" 
             :key="dict.value" 
            :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户账号" prop="loginName">
        <el-input
          v-model="queryParams.loginName"
          placeholder="请输入用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务id" prop="promptId">
        <el-input
          v-model="queryParams.promptId"
          placeholder="请输入任务id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['admin:record:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->
    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务是否成功" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.fileIds ? 'success' : 'fail' }}</span>
        </template>
      </el-table-column> <el-table-column label="promptId" align="center" prop="promptId" />
      <el-table-column label="用户账号" align="center" prop="loginName" />
      <el-table-column label="本次任务参数" align="center" prop="genInfo" class-name="ellipsis-text">
        <template slot-scope="scope">
          <span class="ellipsis-text" @click="showDetail('本次任务参数', scope.row.genInfo)">
            {{ formatText(scope.row.genInfo) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="生图开始时间" align="center" prop="genStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.genStartTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生图结束时间" align="center" prop="genEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.genEndTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务提交时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="本次任务生成的图像信息" align="center">
        <template #default="scope">
          <div v-if="scope.row.thumbnailUrls">
            <div v-for="(url, index) in parseThumbnailUrls(scope.row.thumbnailUrls)" :key="index">
              <el-image style="width: 100px; height: 100px; margin-right: 10px;" :src="url" @click="showImage(url)"
                lazy />
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 添加或修改用户生图任务记录对话框 -->
    <el-dialog :title="detailTitle" :visible.sync="detailVisible" width="500px" append-to-body>
      <p>{{ detailContent }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserProfile } from "@/api/admin/record";

export default {
  name: "Record",
  dicts: ['gpt_prompt_record_origin_create', 'send_ws_failure', 'prompt_record_model_id'],
  data() {
    return {
      loading: false,
      showSearch: true,
      total: 0,
      recordList: [],
      daterangeGenStartTime: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sendWsFailure: null,
        beginGenCreateTime: null,
        endGenCreateTime: null,
        createTime: null,
        loginName:null,
        promptId:null
      },
      isSuccessList:[{
        label:"success",
        value:1
      },{
        label:"fail",
        value:0
      }],
      detailVisible: false,
      detailTitle: '',
      detailContent: '',
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 假设这是你的 parseTime 方法
    parseTime(time, format) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleString('zh-CN', { hour12: false });
    },
    formatText(text) {
      return text.length > 10 ? text.substring(0, 10) + '...' : text;
    },
    showDetail(title, content) {
      this.detailTitle = title;
      this.detailContent = content;
      this.detailVisible = true;
    },
    /** 查询用户生图任务记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.daterangeGenStartTime.length === 2) {
        this.queryParams.beginGenCreateTime = this.daterangeGenStartTime[0];
        this.queryParams.endGenCreateTime = this.daterangeGenStartTime[1];
      }
      getUserProfile(this.queryParams).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 清空日期范围选择
      this.daterangeGenStartTime = [];
      // 清空其他查询参数
      this.queryParams.sendWsFailure = null;
      this.queryParams.isSuccess=null;
      this.queryParams.pageNum = 1;
      this.queryParams.beginGenCreateTime = null;
      this.queryParams.endGenCreateTime = null;
      this.queryParams.createTime =null;
      // 重新设置表单数据
      this.$nextTick(() => {
        this.$refs.queryForm.resetFields();
        this.getList();
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('admin/record/export', {
    //     ...this.queryParams
    //   }, `record_${new Date().getTime()}.xlsx`);
    // },
    parseThumbnailUrls(urls) {
      return urls.split(',');
    },
  }
};
</script>
