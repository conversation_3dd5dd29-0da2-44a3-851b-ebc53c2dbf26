import request from '@/utils/request'


// 查询缓存名称列表
export function listCacheName() {
    return request({
      url: '/task/cache/getNames',
      method: 'get'
    })
  }
  
  // 查询缓存键名列表
  export function listCacheKey(cacheName) {
    return request({
      url: '/task/cache/getKeys/' + cacheName,
      method: 'get'
    })
  }
  
  // 查询缓存内容
  export function getCacheValue(cacheName, cacheKey) {
    return request({
      url: '/task/cache/getValue/' + cacheName + '/' + cacheKey,
      method: 'get'
    })
  }

  // 清理指定键名缓存
export function clearCacheKey(cacheKey) {
  return request({
    url: '/task/cache/clearCacheKey/' + cacheKey,
    method: 'delete'
  })
}

  // 根据日期获取儿童色情词 过滤次数
  export function getChildPornCountByDate(date) {
    return request({
      url: '/task/cache/get-child-porn-count?date=' + date,
      method: 'get'
    })
  }
