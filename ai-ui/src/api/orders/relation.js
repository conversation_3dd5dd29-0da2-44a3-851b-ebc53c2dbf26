import request from '@/utils/request'

// 查询Stripe 中客户信息列表
export function listRelation(query) {
  return request({
    url: '/orders/relation/list',
    method: 'get',
    params: query
  })
}

// 查询Stripe 中客户信息详细
export function getRelation(id) {
  return request({
    url: '/orders/relation/' + id,
    method: 'get'
  })
}

// 新增Stripe 中客户信息
export function addRelation(data) {
  return request({
    url: '/orders/relation',
    method: 'post',
    data: data
  })
}

// 修改Stripe 中客户信息
export function updateRelation(data) {
  return request({
    url: '/orders/relation',
    method: 'put',
    data: data
  })
}

// 删除Stripe 中客户信息
export function delRelation(id) {
  return request({
    url: '/orders/relation/' + id,
    method: 'delete'
  })
}
