import request from '@/utils/request'

// 查询stripe预定阅记录日志列表
export function listScheduleLog(query) {
  return request({
    url: '/orders/scheduleLog/list',
    method: 'get',
    params: query
  })
}

// 查询stripe预定阅记录日志详细
export function getScheduleLog(id) {
  return request({
    url: '/orders/scheduleLog/' + id,
    method: 'get'
  })
}

// 新增stripe预定阅记录日志
export function addScheduleLog(data) {
  return request({
    url: '/orders/scheduleLog',
    method: 'post',
    data: data
  })
}

// 修改stripe预定阅记录日志
export function updateScheduleLog(data) {
  return request({
    url: '/orders/scheduleLog',
    method: 'put',
    data: data
  })
}

// 删除stripe预定阅记录日志
export function delScheduleLog(id) {
  return request({
    url: '/orders/scheduleLog/' + id,
    method: 'delete'
  })
}
