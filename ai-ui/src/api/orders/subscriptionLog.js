import request from '@/utils/request'

// 查询stripe 订阅日志列表
export function listSubscriptionLog(query) {
  return request({
    url: '/orders/subscriptionLog/list',
    method: 'get',
    params: query
  })
}

// 查询stripe 订阅日志详细
export function getSubscriptionLog(id) {
  return request({
    url: '/orders/subscriptionLog/' + id,
    method: 'get'
  })
}

// 新增stripe 订阅日志
export function addSubscriptionLog(data) {
  return request({
    url: '/orders/subscriptionLog',
    method: 'post',
    data: data
  })
}

// 修改stripe 订阅日志
export function updateSubscriptionLog(data) {
  return request({
    url: '/orders/subscriptionLog',
    method: 'put',
    data: data
  })
}

// 删除stripe 订阅日志
export function delSubscriptionLog(id) {
  return request({
    url: '/orders/subscriptionLog/' + id,
    method: 'delete'
  })
}
