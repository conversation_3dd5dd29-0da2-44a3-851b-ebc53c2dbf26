import request from '@/utils/request'

// 查询lummen购买记录列表
export function listRecord(query) {
  return request({
    url: '/orders/record/list',
    method: 'get',
    params: query
  })
}

// 查询lummen购买记录详细
export function getRecord(id) {
  return request({
    url: '/orders/record/' + id,
    method: 'get'
  })
}

// 新增lummen购买记录
export function addRecord(data) {
  return request({
    url: '/orders/record',
    method: 'post',
    data: data
  })
}

// 修改lummen购买记录
export function updateRecord(data) {
  return request({
    url: '/orders/record',
    method: 'put',
    data: data
  })
}

// 删除lummen购买记录
export function delRecord(id) {
  return request({
    url: '/orders/record/' + id,
    method: 'delete'
  })
}

// 查看订单交易记录
export function orderTradeRecordList(query){
  return request({
    url: '/orders/record/trade-record-list',
    method: 'get',
    params: query
  })
}