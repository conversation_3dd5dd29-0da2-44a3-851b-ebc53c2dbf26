import request from '@/utils/request'

// 查询苹果支付JWS交易记录列表
export function listTransaction(query) {
  return request({
    url: '/orders/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询苹果支付JWS交易记录详细
export function getTransaction(id) {
  return request({
    url: '/orders/transaction/' + id,
    method: 'get'
  })
}

// 新增苹果支付JWS交易记录
export function addTransaction(data) {
  return request({
    url: '/orders/transaction',
    method: 'post',
    data: data
  })
}

// 修改苹果支付JWS交易记录
export function updateTransaction(data) {
  return request({
    url: '/orders/transaction',
    method: 'put',
    data: data
  })
}

// 删除苹果支付JWS交易记录
export function delTransaction(id) {
  return request({
    url: '/orders/transaction/' + id,
    method: 'delete'
  })
}
