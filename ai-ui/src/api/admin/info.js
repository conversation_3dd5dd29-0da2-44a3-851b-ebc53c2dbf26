import request from '@/utils/request'

// 查询运营信息列表
export function listInfo(query) {
  return request({
    url: '/admin/info/list',
    method: 'get',
    params: query
  })
}

// 查询运营信息详细
export function getInfo(id) {
  return request({
    url: '/admin/info/' + id,
    method: 'get'
  })
}

// 新增运营信息
export function addInfo(data) {
  return request({
    url: '/admin/info',
    method: 'post',
    data: data
  })
}

// 修改运营信息
export function updateInfo(data) {
  return request({
    url: '/admin/info',
    method: 'put',
    data: data
  })
}

// 删除运营信息
export function delInfo(id) {
  return request({
    url: '/admin/info/' + id,
    method: 'delete'
  })
}
