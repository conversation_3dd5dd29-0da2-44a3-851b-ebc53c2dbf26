import request from '@/utils/request'

// 查询社区首页banner图配置列表
export function listImg(query) {
  return request({
    url: '/admin/img/list',
    method: 'get',
    params: query
  })
}

// 查询社区首页banner图配置详细
export function getImg(id) {
  return request({
    url: '/admin/img/' + id,
    method: 'get'
  })
}

// 新增社区首页banner图配置
export function addImg(data) {
  return request({
    url: '/admin/img',
    method: 'post',
    data: data
  })
}

// 修改社区首页banner图配置
export function updateImg(data) {
  return request({
    url: '/admin/img',
    method: 'put',
    data: data
  })
}

// 删除社区首页banner图配置
export function delImg(id) {
  return request({
    url: '/admin/img/' + id,
    method: 'delete'
  })
}
