import request from '@/utils/request'

// 查询联系我们列表
export function listContacts(query) {
    return request({
        url: '/admin/contacts/list',
        method: 'get',
        params: query
    })
}

// 查询联系我们详细
export function getContacts(id) {
    return request({
        url: '/admin/contacts/' + id,
        method: 'get'
    })
}

// 新增联系我们
export function addContacts(data) {
    return request({
        url: '/admin/contacts',
        method: 'post',
        data: data
    })
}

// 修改联系我们
export function updateContacts(data) {
    return request({
        url: '/admin/contacts',
        method: 'put',
        data: data
    })
}

// 删除联系我们
export function delContacts(id) {
    return request({
        url: '/admin/contacts/' + id,
        method: 'delete'
    })
}


// 联系我们(反馈数量)
export function getContactsCount() {
    return request({
        url: '/admin/contacts/count',
        method: 'get'
    })
}
