import request from '@/utils/request'

// 查询web端版本管理列表
export function listWeb(query) {
  return request({
    url: '/version/web/list',
    method: 'get',
    params: query
  })
}

// 查询web端版本管理详细
export function getWeb(id) {
  return request({
    url: '/version/web/' + id,
    method: 'get'
  })
}

// 新增web端版本管理
export function addWeb(data) {
  return request({
    url: '/version/web',
    method: 'post',
    data: data
  })
}

// 修改web端版本管理
export function updateWeb(data) {
  return request({
    url: '/version/web',
    method: 'put',
    data: data
  })
}

// 删除web端版本管理
export function delWeb(id) {
  return request({
    url: '/version/web/' + id,
    method: 'delete'
  })
}
