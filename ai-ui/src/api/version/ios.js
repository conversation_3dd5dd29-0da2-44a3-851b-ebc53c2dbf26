import request from '@/utils/request'

// 查询ios端版本管理列表
export function listIos(query) {
  return request({
    url: '/version/ios/list',
    method: 'get',
    params: query
  })
}

// 查询ios端版本管理详细
export function getIos(id) {
  return request({
    url: '/version/ios/' + id,
    method: 'get'
  })
}

// 新增ios端版本管理
export function addIos(data) {
  return request({
    url: '/version/ios',
    method: 'post',
    data: data
  })
}

// 修改ios端版本管理
export function updateIos(data) {
  return request({
    url: '/version/ios',
    method: 'put',
    data: data
  })
}

// 删除ios端版本管理
export function delIos(id) {
  return request({
    url: '/version/ios/' + id,
    method: 'delete'
  })
}
