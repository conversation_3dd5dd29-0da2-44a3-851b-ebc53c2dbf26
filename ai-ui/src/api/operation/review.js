import request from '@/utils/request'

// 查询用户图片公开审核列表
export function listReview(query) {
  return request({
    url: '/operation/review/list',
    method: 'get',
    params: query
  })
}

// 查询用户图片公开审核详细
export function getReview(id) {
  return request({
    url: '/operation/review/' + id,
    method: 'get'
  })
}

// 新增用户图片公开审核
export function addReview(data) {
  return request({
    url: '/operation/review',
    method: 'post',
    data: data
  })
}

// 修改用户图片公开审核
export function updateReview(data) {
  return request({
    url: '/operation/review',
    method: 'put',
    data: data
  })
}

// 删除用户图片公开审核
export function delReview(id) {
  return request({
    url: '/operation/review/' + id,
    method: 'delete'
  })
}

// 批量通过
export function passInBatches(id,featured,activity) {
  return request({
    url: '/operation/review/passInBatches/' + id + '/' +featured + '/' + activity,
    method: 'get'
  })
}

// 批量拒绝
export function rejectInBatches(data) {
  return request({
    url: '/operation/review/rejectInBatches',
    method: 'post',
    data: data
  })
}


export function listImg(query) {
  return request({
    url: '/operation/review/img-list',
    method: 'get',
    params: query
  })
}

// 设置图片为精选
export function setFeatured(id) {
  return request({
    url: '/operation/review/setFeatured/' + id,
    method: 'get'
  })
}


// 取消图片精选
export function cancelFeatured(id) {
  return request({
    url: '/operation/review/cancelFeatured/' + id,
    method: 'get'
  })
}

// 删除社区图片
export function deleteImgs(id) {
  return request({
    url: '/operation/review/deleteImgs/' + id,
    method: 'get'
  })
}

// 取消活动图片
export function cancelActivityImgs(id) {
  return request({
    url: '/operation/review/cancel-activity-imgs/' + id,
    method: 'get'
  })
}