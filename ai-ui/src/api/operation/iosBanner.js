import request from '@/utils/request'

// 查询ios_社区首页banner图配置列表
export function listIosBanner(query) {
  return request({
    url: '/operation/iosBanner/list',
    method: 'get',
    params: query
  })
}

// 查询ios_社区首页banner图配置详细
export function getIosBanner(id) {
  return request({
    url: '/operation/iosBanner/' + id,
    method: 'get'
  })
}

// 新增ios_社区首页banner图配置
export function addIosBanner(data) {
  return request({
    url: '/operation/iosBanner',
    method: 'post',
    data: data
  })
}

// 修改ios_社区首页banner图配置
export function updateIosBanner(data) {
  return request({
    url: '/operation/iosBanner',
    method: 'put',
    data: data
  })
}

// 删除ios_社区首页banner图配置
export function delIosBanner(id) {
  return request({
    url: '/operation/iosBanner/' + id,
    method: 'delete'
  })
}

export function getJumpIdTree() {
  return request({
    url: '/operation/iosBanner/get-jump_id_tree',
    method: 'get'
  })
}
