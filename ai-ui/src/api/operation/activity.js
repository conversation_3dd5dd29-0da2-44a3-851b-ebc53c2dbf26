import request from '@/utils/request'

// 查询平台公告通知列表
export function listActivity(query) {
  return request({
    url: '/operation/activity/list',
    method: 'get',
    params: query
  })
}

// 查询平台公告通知详细
export function getActivity(id) {
  return request({
    url: '/operation/activity/' + id,
    method: 'get'
  })
}

// 新增平台公告通知
export function addActivity(data) {
  return request({
    url: '/operation/activity',
    method: 'post',
    data: data
  })
}

// 修改平台公告通知
export function updateActivity(data) {
  return request({
    url: '/operation/activity',
    method: 'put',
    data: data
  })
}

// 删除平台公告通知
export function delActivity(id) {
  return request({
    url: '/operation/activity/' + id,
    method: 'delete'
  })
}


// 发布平台公告通知
export function toPublish(id) {
  return request({
    url: '/operation/activity/to-publish/' + id,
    method: 'get'
  })
}

// 刷新发布数量到缓存
export function refreshPublish(){
  return request({
    url: '/operation/activity/refresh-publish',
    method: 'get'
  })
}
