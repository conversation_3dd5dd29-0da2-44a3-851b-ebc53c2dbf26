import request from '@/utils/request'

// 查询用户问卷调查回答列表
export function listAnswer(query) {
  return request({
    url: '/operation/answer/list',
    method: 'get',
    params: query
  })
}

// 查询用户问卷调查回答详细
export function getAnswer(id) {
  return request({
    url: '/operation/answer/' + id,
    method: 'get'
  })
}

// 新增用户问卷调查回答
export function addAnswer(data) {
  return request({
    url: '/operation/answer',
    method: 'post',
    data: data
  })
}

// 修改用户问卷调查回答
export function updateAnswer(data) {
  return request({
    url: '/operation/answer',
    method: 'put',
    data: data
  })
}

// 删除用户问卷调查回答
export function delAnswer(id) {
  return request({
    url: '/operation/answer/' + id,
    method: 'delete'
  })
}

// 查询用户问卷调查问题-回答详细
export function getQuestionAnswer(id) {
  return request({
    url: '/operation/answer/question-answer/' + id,
    method: 'get'
  })
}