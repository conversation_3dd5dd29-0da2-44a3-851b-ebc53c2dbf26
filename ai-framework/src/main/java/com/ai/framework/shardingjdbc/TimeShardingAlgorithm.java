package com.ai.framework.shardingjdbc;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;

/**
 * 按照时间进行分片，包含精确时间和范围时间
 */
public class TimeShardingAlgorithm implements StandardShardingAlgorithm<LocalDateTime> {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy_M");

    private static final String logicTable = "gpt_event_log_";


    /**
     * 精确分片
     * @param tableNames 对应分片库中所有分片表的集合
     * @param preciseShardingValue 分片键的精确值
     * @return 分片表名
     */
    @Override
    public String doSharding(Collection<String> tableNames, PreciseShardingValue<LocalDateTime> preciseShardingValue) {
        // 获取分片键的值
        LocalDateTime createTime = preciseShardingValue.getValue();
        String monthKey = createTime.format(DATE_FORMATTER);
        String targetTable = logicTable + monthKey;

        // 如果目标表在可用的表集合中，返回目标表
        if (tableNames.contains(targetTable)) {
            return targetTable;
        }

        throw new IllegalArgumentException("No available target table found for key: " + monthKey);
    }

    /**
     * 范围分片
     * @param tableNames 对应分片库中所有分片表的集合
     * @param rangeShardingValue 分片键的范围值
     * @return 表名集合
     */
    @Override
    public Collection<String> doSharding(Collection<String> tableNames, RangeShardingValue<LocalDateTime> rangeShardingValue) {
        Set<String> result = new HashSet<>();

        // 获取分片键的范围
        LocalDateTime lowerBound = rangeShardingValue.getValueRange().hasLowerBound() ? rangeShardingValue.getValueRange().lowerEndpoint() : null;
        LocalDateTime upperBound = rangeShardingValue.getValueRange().hasUpperBound() ? rangeShardingValue.getValueRange().upperEndpoint() : null;

        if (lowerBound == null || upperBound == null) {
            throw new IllegalArgumentException("Range sharding value must have both lower and upper bounds.");
        }

        // 将 upperBound 转换为对应月份的最后时刻（完善比较的逻辑）
        upperBound = LocalDate.of(upperBound.getYear(), upperBound.getMonth(),
                upperBound.toLocalDate().lengthOfMonth())
                .atTime(23, 59, 59);

        // 遍历范围内的每一个月份
        LocalDateTime current = lowerBound;
        while (current.isBefore(upperBound) || current.isEqual(upperBound)) {
            String monthKey = current.format(DATE_FORMATTER);
            String targetTable = logicTable + monthKey;

            // 检查目标表是否在可用的表集合中
            if (tableNames.contains(targetTable)) {
                result.add(targetTable);
            }

            // 增加到下一个月
            current = current.plusMonths(1);
        }

        return result;
    }

    @Override
    public String getType() {
        // 返回自定义的算法类型名称，用于配置文件中的类型标识
        return "TIME_SHARDING_ALGORITHM";
    }

    @Override
    public Properties getProps() {
        return null;
    }

    @Override
    public void init(Properties properties) {

    }
}
