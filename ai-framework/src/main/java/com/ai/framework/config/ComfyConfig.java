package com.ai.framework.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "comfy.server")
public class ComfyConfig {

    @Value("${comfy.server.maxRetry:5}")
    private int maxRetry;

    private List<String> emailReceiver;

    private String clientId;

    private String clientSecret;

    private String clientCode;

    private String remoteUrl;

    @Getter
    @Setter
    public static class AiEaseServer {
        private String name;
        private String address;
    }
}
