package com.ai.framework.myBatisPlus;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ai.common.utils.SecurityUtils;

import java.time.LocalDateTime;
import java.util.Date;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    public static final String CREATE_BY = "createBy";
    public static final String CREATE_TIME = "createTime";
    public static final String UPDATE_BY = "updateBy";
    public static final String UPDATE_TIME = "updateTime";
//    public static final String DELETED = "deleted";

    /**
     * 插入时的填充策略
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
//        log.info("start intsert fill ....");
        //strictInsertFill(MetaObject metaObject, String fieldName, Class<T> fieldType, E fieldVal)
        // 起始版本 3.3.0(推荐使用)
        String userName = "ScheduleTask";
        try {
            userName = SecurityUtils.getUsername();
        } catch (Exception e) {
            //定时任务无需处理
        }
        this.setFieldValByName(CREATE_BY, userName, metaObject);
        this.setFieldValByName(CREATE_TIME, formatDate(metaObject.getSetterType(CREATE_TIME)), metaObject);
        this.setFieldValByName(UPDATE_BY, userName, metaObject);
        this.setFieldValByName(UPDATE_TIME, formatDate(metaObject.getSetterType(CREATE_TIME)), metaObject);
//        this.setFieldValByName(DELETED, 0,metaObject);
    }


    /**
     * 更新时的填充策略
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
//        log.info("start update fill ....");
        String userName = "ScheduleTask";
        try {
            userName = SecurityUtils.getUsername();
        } catch (Exception e) {
            //定时任务无需处理
        }
        this.setFieldValByName(UPDATE_BY, userName, metaObject);
        this.setFieldValByName(UPDATE_TIME, formatDate(metaObject.getSetterType(CREATE_TIME)), metaObject);
    }


    /**
     * 处理特殊日期
     *
     * @param setterType 参数类型
     * @return 日期类型
     */
    private Object formatDate(Class<?> setterType) {
        if (Date.class.equals(setterType)) {
            return new Date();
        } else if (LocalDateTime.class.equals(setterType)) {
            return LocalDateTime.now();
        } else if (Long.class.equals(setterType)) {
            return System.currentTimeMillis();
        }
        return null;
    }

}